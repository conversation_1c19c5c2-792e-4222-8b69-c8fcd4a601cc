<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_payment_result_payu_money" modulePackage="com.bigwheel.winners" filePath="app\src\main\res\layout\activity_payment_result_payu_money.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_payment_result_payu_money_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="216" endOffset="14"/></Target><Target id="@+id/card_view" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="17" startOffset="4" endLine="213" endOffset="39"/></Target><Target id="@+id/animationView" view="com.airbnb.lottie.LottieAnimationView"><Expressions/><location startLine="39" startOffset="16" endLine="45" endOffset="43"/></Target><Target id="@+id/finaldata" view="TextView"><Expressions/><location startLine="54" startOffset="16" endLine="64" endOffset="21"/></Target><Target id="@+id/textinfo" view="TextView"><Expressions/><location startLine="66" startOffset="16" endLine="76" endOffset="21"/></Target><Target id="@+id/date" view="TextView"><Expressions/><location startLine="78" startOffset="16" endLine="88" endOffset="21"/></Target><Target id="@+id/tranID" view="TextView"><Expressions/><location startLine="109" startOffset="16" endLine="119" endOffset="21"/></Target><Target id="@+id/tranAmu" view="TextView"><Expressions/><location startLine="140" startOffset="16" endLine="150" endOffset="21"/></Target><Target id="@+id/tranemail" view="TextView"><Expressions/><location startLine="171" startOffset="16" endLine="181" endOffset="21"/></Target><Target id="@+id/okbtn" view="Button"><Expressions/><location startLine="192" startOffset="16" endLine="202" endOffset="21"/></Target></Targets></Layout>