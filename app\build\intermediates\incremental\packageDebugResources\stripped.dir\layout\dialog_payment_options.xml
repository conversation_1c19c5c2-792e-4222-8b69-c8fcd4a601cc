<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/ic_rounded_theme_white"
    android:orientation="vertical"
    android:padding="20dp">

    <!-- Header -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="💳 Choose Payment Method"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:gravity="center"
        android:layout_marginBottom="10dp" />

    <!-- Amount Display -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/premium_gold_light"
        android:padding="15dp"
        android:gravity="center"
        android:layout_marginBottom="20dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Amount to Pay: "
            android:textSize="16sp"
            android:textColor="@color/black" />

        <TextView
            android:id="@+id/payment_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="₹100"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/premium_gold" />

    </LinearLayout>

    <!-- Google Pay Option -->
    <LinearLayout
        android:id="@+id/layout_google_pay"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="@drawable/bg_rounded_button"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="15dp"
        android:layout_marginBottom="10dp"
        android:clickable="true"
        android:focusable="true">

        <ImageView
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_google_pay"
            android:layout_marginEnd="15dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Google Pay"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/white" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="→"
            android:textSize="20sp"
            android:textColor="@color/white" />

    </LinearLayout>

    <!-- PhonePe Option -->
    <LinearLayout
        android:id="@+id/layout_phone_pay"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="@drawable/bg_rounded_button"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="15dp"
        android:layout_marginBottom="10dp"
        android:clickable="true"
        android:focusable="true">

        <ImageView
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_phonepe"
            android:layout_marginEnd="15dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="PhonePe"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/white" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="→"
            android:textSize="20sp"
            android:textColor="@color/white" />

    </LinearLayout>

    <!-- Paytm Option -->
    <LinearLayout
        android:id="@+id/layout_paytm"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="@drawable/bg_rounded_button"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="15dp"
        android:layout_marginBottom="20dp"
        android:clickable="true"
        android:focusable="true">

        <ImageView
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_paytm"
            android:layout_marginEnd="15dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Paytm"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/white" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="→"
            android:textSize="20sp"
            android:textColor="@color/white" />

    </LinearLayout>

    <!-- Cancel Button -->
    <Button
        android:id="@+id/btn_cancel_payment"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:text="Cancel"
        android:textColor="@color/black"
        android:background="@drawable/bg_card_basic"
        android:textSize="16sp"
        android:textStyle="bold" />

</LinearLayout>
