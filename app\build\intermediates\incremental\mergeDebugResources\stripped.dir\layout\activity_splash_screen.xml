<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/themeBgColor"
    tools:context=".SplashScreen">


    <ImageView
        android:id="@+id/linearLayout"
        android:layout_width="161dp"
        android:layout_height="150dp"
        android:layout_marginBottom="11dp"
        android:src="@drawable/ic_splash_text"
        app:layout_constraintBottom_toTopOf="@+id/textView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">


    </ImageView>

    <TextView
        android:id="@+id/textView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="26.59dp"
        android:fontFamily="@font/poppins"
        android:text="Big Wheel-Daily Reward"
        android:textColor="@color/white"
        android:textSize="16dp"
        app:layout_constraintBottom_toTopOf="@+id/imageView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="200dp"
        android:layout_height="200dp"
        android:src="@drawable/spinbg4"
        android:layout_marginBottom="110dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.72" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/btnGetMoney"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="71.39dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginLeft="44dp"
        android:background="@drawable/ic_rounded_theme_yellow"
        android:layout_marginRight="45dp"
        app:layout_constraintTop_toBottomOf="@+id/imageView">

        <TextView
            android:id="@+id/textView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="25dp"
            android:fontFamily="@font/poppins"
            android:text="GET MONEY"
            android:textColor="@color/white"
            android:includeFontPadding="false"
            android:textSize="20dp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:src="@drawable/ic_play"
            android:layout_marginBottom="2dp"
            app:layout_constraintBottom_toBottomOf="@+id/textView2"
            app:layout_constraintStart_toEndOf="@+id/textView2"
            app:layout_constraintTop_toTopOf="@+id/textView2"
            app:tint="@color/white" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>