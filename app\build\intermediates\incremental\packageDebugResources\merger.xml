<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\android project\new big 22-06\app\src\main\res"/><source path="D:\android project\new big 22-06\app\build\generated\res\rs\debug"/><source path="D:\android project\new big 22-06\app\build\generated\res\resValues\debug"/><source path="D:\android project\new big 22-06\app\build\generated\res\google-services\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\android project\new big 22-06\app\src\main\res"><file name="button_press" path="D:\android project\new big 22-06\app\src\main\res\anim\button_press.xml" qualifiers="" type="anim"/><file name="dialog_in" path="D:\android project\new big 22-06\app\src\main\res\anim\dialog_in.xml" qualifiers="" type="anim"/><file name="dialog_out" path="D:\android project\new big 22-06\app\src\main\res\anim\dialog_out.xml" qualifiers="" type="anim"/><file name="plan_select" path="D:\android project\new big 22-06\app\src\main\res\anim\plan_select.xml" qualifiers="" type="anim"/><file name="popup_enter" path="D:\android project\new big 22-06\app\src\main\res\anim\popup_enter.xml" qualifiers="" type="anim"/><file name="popup_exit" path="D:\android project\new big 22-06\app\src\main\res\anim\popup_exit.xml" qualifiers="" type="anim"/><file name="all_upi" path="D:\android project\new big 22-06\app\src\main\res\drawable\all_upi.png" qualifiers="" type="drawable"/><file name="all_wallets" path="D:\android project\new big 22-06\app\src\main\res\drawable\all_wallets.png" qualifiers="" type="drawable"/><file name="bg_discount_badge" path="D:\android project\new big 22-06\app\src\main\res\drawable\bg_discount_badge.xml" qualifiers="" type="drawable"/><file name="bg_plan_basic" path="D:\android project\new big 22-06\app\src\main\res\drawable\bg_plan_basic.xml" qualifiers="" type="drawable"/><file name="bg_plan_popular" path="D:\android project\new big 22-06\app\src\main\res\drawable\bg_plan_popular.xml" qualifiers="" type="drawable"/><file name="bg_plan_premium" path="D:\android project\new big 22-06\app\src\main\res\drawable\bg_plan_premium.xml" qualifiers="" type="drawable"/><file name="bg_plan_vip" path="D:\android project\new big 22-06\app\src\main\res\drawable\bg_plan_vip.xml" qualifiers="" type="drawable"/><file name="bg_premium_button" path="D:\android project\new big 22-06\app\src\main\res\drawable\bg_premium_button.xml" qualifiers="" type="drawable"/><file name="bg_premium_popup" path="D:\android project\new big 22-06\app\src\main\res\drawable\bg_premium_popup.xml" qualifiers="" type="drawable"/><file name="bg_rounded_button" path="D:\android project\new big 22-06\app\src\main\res\drawable\bg_rounded_button.xml" qualifiers="" type="drawable"/><file name="cardbackground" path="D:\android project\new big 22-06\app\src\main\res\drawable\cardbackground.xml" qualifiers="" type="drawable"/><file name="crowns" path="D:\android project\new big 22-06\app\src\main\res\drawable\crowns.png" qualifiers="" type="drawable"/><file name="female" path="D:\android project\new big 22-06\app\src\main\res\drawable\female.png" qualifiers="" type="drawable"/><file name="icgrop" path="D:\android project\new big 22-06\app\src\main\res\drawable\icgrop.png" qualifiers="" type="drawable"/><file name="ic_baseline_backspace_24" path="D:\android project\new big 22-06\app\src\main\res\drawable\ic_baseline_backspace_24.xml" qualifiers="" type="drawable"/><file name="ic_baseline_close_24" path="D:\android project\new big 22-06\app\src\main\res\drawable\ic_baseline_close_24.xml" qualifiers="" type="drawable"/><file name="ic_baseline_privacy_tip_24" path="D:\android project\new big 22-06\app\src\main\res\drawable\ic_baseline_privacy_tip_24.xml" qualifiers="" type="drawable"/><file name="ic_delete" path="D:\android project\new big 22-06\app\src\main\res\drawable\ic_delete.xml" qualifiers="" type="drawable"/><file name="ic_group" path="D:\android project\new big 22-06\app\src\main\res\drawable\ic_group.png" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\android project\new big 22-06\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\android project\new big 22-06\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_menu" path="D:\android project\new big 22-06\app\src\main\res\drawable\ic_menu.xml" qualifiers="" type="drawable"/><file name="ic_paytm" path="D:\android project\new big 22-06\app\src\main\res\drawable\ic_paytm.xml" qualifiers="" type="drawable"/><file name="ic_play" path="D:\android project\new big 22-06\app\src\main\res\drawable\ic_play.xml" qualifiers="" type="drawable"/><file name="ic_rounded_theme_blue" path="D:\android project\new big 22-06\app\src\main\res\drawable\ic_rounded_theme_blue.xml" qualifiers="" type="drawable"/><file name="ic_rounded_theme_white" path="D:\android project\new big 22-06\app\src\main\res\drawable\ic_rounded_theme_white.xml" qualifiers="" type="drawable"/><file name="ic_rounded_theme_white_black" path="D:\android project\new big 22-06\app\src\main\res\drawable\ic_rounded_theme_white_black.xml" qualifiers="" type="drawable"/><file name="ic_rounded_theme_yellow" path="D:\android project\new big 22-06\app\src\main\res\drawable\ic_rounded_theme_yellow.xml" qualifiers="" type="drawable"/><file name="ic_rounded_theme_yellow1" path="D:\android project\new big 22-06\app\src\main\res\drawable\ic_rounded_theme_yellow1.xml" qualifiers="" type="drawable"/><file name="ic_splash_text" path="D:\android project\new big 22-06\app\src\main\res\drawable\ic_splash_text.xml" qualifiers="" type="drawable"/><file name="ic_xml_button_" path="D:\android project\new big 22-06\app\src\main\res\drawable\ic_xml_button_.xml" qualifiers="" type="drawable"/><file name="insurance" path="D:\android project\new big 22-06\app\src\main\res\drawable\insurance.png" qualifiers="" type="drawable"/><file name="logo" path="D:\android project\new big 22-06\app\src\main\res\drawable\logo.png" qualifiers="" type="drawable"/><file name="logout" path="D:\android project\new big 22-06\app\src\main\res\drawable\logout.png" qualifiers="" type="drawable"/><file name="mail" path="D:\android project\new big 22-06\app\src\main\res\drawable\mail.png" qualifiers="" type="drawable"/><file name="male" path="D:\android project\new big 22-06\app\src\main\res\drawable\male.png" qualifiers="" type="drawable"/><file name="more" path="D:\android project\new big 22-06\app\src\main\res\drawable\more.png" qualifiers="" type="drawable"/><file name="roundedbutton" path="D:\android project\new big 22-06\app\src\main\res\drawable\roundedbutton.xml" qualifiers="" type="drawable"/><file name="search" path="D:\android project\new big 22-06\app\src\main\res\drawable\search.png" qualifiers="" type="drawable"/><file name="share" path="D:\android project\new big 22-06\app\src\main\res\drawable\share.png" qualifiers="" type="drawable"/><file name="spinbg" path="D:\android project\new big 22-06\app\src\main\res\drawable\spinbg.jpg" qualifiers="" type="drawable"/><file name="spinbg2" path="D:\android project\new big 22-06\app\src\main\res\drawable\spinbg2.png" qualifiers="" type="drawable"/><file name="spinbg3" path="D:\android project\new big 22-06\app\src\main\res\drawable\spinbg3.png" qualifiers="" type="drawable"/><file name="spinbg4" path="D:\android project\new big 22-06\app\src\main\res\drawable\spinbg4.png" qualifiers="" type="drawable"/><file name="splashbg" path="D:\android project\new big 22-06\app\src\main\res\drawable\splashbg.png" qualifiers="" type="drawable"/><file name="sp_bg" path="D:\android project\new big 22-06\app\src\main\res\drawable\sp_bg.png" qualifiers="" type="drawable"/><file name="step1" path="D:\android project\new big 22-06\app\src\main\res\drawable\step1.jpg" qualifiers="" type="drawable"/><file name="step2" path="D:\android project\new big 22-06\app\src\main\res\drawable\step2.jpg" qualifiers="" type="drawable"/><file name="step3" path="D:\android project\new big 22-06\app\src\main\res\drawable\step3.jpg" qualifiers="" type="drawable"/><file name="step4" path="D:\android project\new big 22-06\app\src\main\res\drawable\step4.jpg" qualifiers="" type="drawable"/><file name="step5" path="D:\android project\new big 22-06\app\src\main\res\drawable\step5.jpg" qualifiers="" type="drawable"/><file name="step6" path="D:\android project\new big 22-06\app\src\main\res\drawable\step6.jpg" qualifiers="" type="drawable"/><file name="strok" path="D:\android project\new big 22-06\app\src\main\res\drawable\strok.xml" qualifiers="" type="drawable"/><file name="teer" path="D:\android project\new big 22-06\app\src\main\res\drawable\teer.png" qualifiers="" type="drawable"/><file name="teer2" path="D:\android project\new big 22-06\app\src\main\res\drawable\teer2.png" qualifiers="" type="drawable"/><file name="terms" path="D:\android project\new big 22-06\app\src\main\res\drawable\terms.png" qualifiers="" type="drawable"/><file name="trophy" path="D:\android project\new big 22-06\app\src\main\res\drawable\trophy.png" qualifiers="" type="drawable"/><file name="wheels" path="D:\android project\new big 22-06\app\src\main\res\drawable\wheels.png" qualifiers="" type="drawable"/><file name="wheelss" path="D:\android project\new big 22-06\app\src\main\res\drawable\wheelss.png" qualifiers="" type="drawable"/><file name="winner" path="D:\android project\new big 22-06\app\src\main\res\drawable\winner.png" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\android project\new big 22-06\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="font" path="D:\android project\new big 22-06\app\src\main\res\font\font.ttf" qualifiers="" type="font"/><file name="poppins" path="D:\android project\new big 22-06\app\src\main\res\font\poppins.xml" qualifiers="" type="font"/><file name="poppins_bold" path="D:\android project\new big 22-06\app\src\main\res\font\poppins_bold.xml" qualifiers="" type="font"/><file name="poppins_light" path="D:\android project\new big 22-06\app\src\main\res\font\poppins_light.xml" qualifiers="" type="font"/><file name="poppins_medium" path="D:\android project\new big 22-06\app\src\main\res\font\poppins_medium.xml" qualifiers="" type="font"/><file name="poppins_semibold" path="D:\android project\new big 22-06\app\src\main\res\font\poppins_semibold.xml" qualifiers="" type="font"/><file name="stardosstencil" path="D:\android project\new big 22-06\app\src\main\res\font\stardosstencil.ttf" qualifiers="" type="font"/><file name="stardos_stencil" path="D:\android project\new big 22-06\app\src\main\res\font\stardos_stencil.xml" qualifiers="" type="font"/><file name="activity_google_login" path="D:\android project\new big 22-06\app\src\main\res\layout\activity_google_login.xml" qualifiers="" type="layout"/><file name="activity_main" path="D:\android project\new big 22-06\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_mains" path="D:\android project\new big 22-06\app\src\main\res\layout\activity_mains.xml" qualifiers="" type="layout"/><file name="activity_payment_methods" path="D:\android project\new big 22-06\app\src\main\res\layout\activity_payment_methods.xml" qualifiers="" type="layout"/><file name="activity_payment_result" path="D:\android project\new big 22-06\app\src\main\res\layout\activity_payment_result.xml" qualifiers="" type="layout"/><file name="activity_payment_result_payu_money" path="D:\android project\new big 22-06\app\src\main\res\layout\activity_payment_result_payu_money.xml" qualifiers="" type="layout"/><file name="activity_payment_tutorial" path="D:\android project\new big 22-06\app\src\main\res\layout\activity_payment_tutorial.xml" qualifiers="" type="layout"/><file name="activity_payment_via_u_p_i" path="D:\android project\new big 22-06\app\src\main\res\layout\activity_payment_via_u_p_i.xml" qualifiers="" type="layout"/><file name="activity_screenfive" path="D:\android project\new big 22-06\app\src\main\res\layout\activity_screenfive.xml" qualifiers="" type="layout"/><file name="activity_screenfour" path="D:\android project\new big 22-06\app\src\main\res\layout\activity_screenfour.xml" qualifiers="" type="layout"/><file name="activity_screensix" path="D:\android project\new big 22-06\app\src\main\res\layout\activity_screensix.xml" qualifiers="" type="layout"/><file name="activity_screenthree" path="D:\android project\new big 22-06\app\src\main\res\layout\activity_screenthree.xml" qualifiers="" type="layout"/><file name="activity_screentwo" path="D:\android project\new big 22-06\app\src\main\res\layout\activity_screentwo.xml" qualifiers="" type="layout"/><file name="activity_screen_one" path="D:\android project\new big 22-06\app\src\main\res\layout\activity_screen_one.xml" qualifiers="" type="layout"/><file name="activity_splash" path="D:\android project\new big 22-06\app\src\main\res\layout\activity_splash.xml" qualifiers="" type="layout"/><file name="activity_splash_screen" path="D:\android project\new big 22-06\app\src\main\res\layout\activity_splash_screen.xml" qualifiers="" type="layout"/><file name="areyousurdilog" path="D:\android project\new big 22-06\app\src\main\res\layout\areyousurdilog.xml" qualifiers="" type="layout"/><file name="dialog_rate_us" path="D:\android project\new big 22-06\app\src\main\res\layout\dialog_rate_us.xml" qualifiers="" type="layout"/><file name="drawerlayout" path="D:\android project\new big 22-06\app\src\main\res\layout\drawerlayout.xml" qualifiers="" type="layout"/><file name="layout_si_details" path="D:\android project\new big 22-06\app\src\main\res\layout\layout_si_details.xml" qualifiers="" type="layout"/><file name="nav_header_navigation_menu" path="D:\android project\new big 22-06\app\src\main\res\layout\nav_header_navigation_menu.xml" qualifiers="" type="layout"/><file name="review_order_row_layout" path="D:\android project\new big 22-06\app\src\main\res\layout\review_order_row_layout.xml" qualifiers="" type="layout"/><file name="activity_navigation_menu_drawer" path="D:\android project\new big 22-06\app\src\main\res\menu\activity_navigation_menu_drawer.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="D:\android project\new big 22-06\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\android project\new big 22-06\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\android project\new big 22-06\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\android project\new big 22-06\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\android project\new big 22-06\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\android project\new big 22-06\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\android project\new big 22-06\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\android project\new big 22-06\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\android project\new big 22-06\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\android project\new big 22-06\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\android project\new big 22-06\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\android project\new big 22-06\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="audio" path="D:\android project\new big 22-06\app\src\main\res\raw\audio.mp3" qualifiers="" type="raw"/><file name="fail" path="D:\android project\new big 22-06\app\src\main\res\raw\fail.json" qualifiers="" type="raw"/><file name="failmusic" path="D:\android project\new big 22-06\app\src\main\res\raw\failmusic.mp3" qualifiers="" type="raw"/><file name="lossanim" path="D:\android project\new big 22-06\app\src\main\res\raw\lossanim.json" qualifiers="" type="raw"/><file name="sucesso" path="D:\android project\new big 22-06\app\src\main\res\raw\sucesso.json" qualifiers="" type="raw"/><file name="wheelsong" path="D:\android project\new big 22-06\app\src\main\res\raw\wheelsong.mp3" qualifiers="" type="raw"/><file name="winner" path="D:\android project\new big 22-06\app\src\main\res\raw\winner.json" qualifiers="" type="raw"/><file name="winningsong" path="D:\android project\new big 22-06\app\src\main\res\raw\winningsong.mp3" qualifiers="" type="raw"/><file path="D:\android project\new big 22-06\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#f27125</color><color name="purple_500">#f27125</color><color name="purple_700">#303F9F</color><color name="purple_7001">#6F1F7C</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="themeBgColor">#1A1B3A</color><color name="themeColor">#6366F1</color><color name="themeColor22">#8B5CF6</color><color name="themeColor2">#581945</color><color name="gray">#666666</color><color name="newWhitr">#ffffff</color><color name="tran">#0055322F</color><color name="blue">#6432BE</color><color name="payumoney_black">#000</color><color name="light_white">#fff</color><color name="red_btn_bg_color">#f27125</color><color name="premium_gold">#FFD700</color><color name="premium_gold_dark">#B8860B</color><color name="premium_gold_light">#FFF8DC</color><color name="luxury_bronze">#CD7F32</color><color name="gradient_primary_start">#667EEA</color><color name="gradient_primary_end">#764BA2</color><color name="gradient_secondary_start">#F093FB</color><color name="gradient_secondary_end">#F5576C</color><color name="gradient_success_start">#4FACFE</color><color name="gradient_success_end">#00F2FE</color><color name="gradient_warning_start">#FDBB2D</color><color name="gradient_warning_end">#22C1C3</color><color name="plan_basic">#E3F2FD</color><color name="plan_basic_border">#2196F3</color><color name="plan_popular">#FFF3E0</color><color name="plan_popular_border">#FF9800</color><color name="plan_premium">#F3E5F5</color><color name="plan_premium_border">#9C27B0</color><color name="plan_vip">#FFF8E1</color><color name="plan_vip_border">#FFD700</color><color name="success_green">#10B981</color><color name="error_red">#EF4444</color><color name="warning_orange">#F59E0B</color><color name="info_blue">#3B82F6</color><color name="bg_primary">#1F2937</color><color name="bg_secondary">#374151</color><color name="bg_card">#FFFFFF</color><color name="bg_overlay">#80000000</color><color name="text_primary">#111827</color><color name="text_secondary">#6B7280</color><color name="text_accent">#6366F1</color><color name="text_white">#FFFFFF</color><color name="shadow_light">#1A000000</color><color name="shadow_medium">#33000000</color><color name="shadow_dark">#4D000000</color></file><file path="D:\android project\new big 22-06\app\src\main\res\values\font_certs.xml" qualifiers=""><array name="com_google_android_gms_fonts_certs">
        <item>@array/com_google_android_gms_fonts_certs_dev</item>
        <item>@array/com_google_android_gms_fonts_certs_prod</item>
    </array><string-array name="com_google_android_gms_fonts_certs_dev">
        <item>
            MIIEqDCCA5CgAwIBAgIJANWFuGx90071MA0GCSqGSIb3DQEBBAUAMIGUMQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEQMA4GA1UEChMHQW5kcm9pZDEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDEiMCAGCSqGSIb3DQEJARYTYW5kcm9pZEBhbmRyb2lkLmNvbTAeFw0wODA0MTUyMzM2NTZaFw0zNTA5MDEyMzM2NTZaMIGUMQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEQMA4GA1UEChMHQW5kcm9pZDEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDEiMCAGCSqGSIb3DQEJARYTYW5kcm9pZEBhbmRyb2lkLmNvbTCCASAwDQYJKoZIhvcNAQEBBQADggENADCCAQgCggEBANbOLggKv+IxTdGNs8/TGFy0PTP6DHThvbbR24kT9ixcOd9W+EaBPWW+wPPKQmsHxajtWjmQwWfna8mZuSeJS48LIgAZlKkpFeVyxW0qMBujb8X8ETrWy550NaFtI6t9+u7hZeTfHwqNvacKhp1RbE6dBRGWynwMVX8XW8N1+UjFaq6GCJukT4qmpN2afb8sCjUigq0GuMwYXrFVee74bQgLHWGJwPmvmLHC69EH6kWr22ijx4OKXlSIx2xT1AsSHee70w5iDBiK4aph27yH3TxkXy9V89TDdexAcKk/cVHYNnDBapcavl7y0RiQ4biu8ymM8Ga/nmzhRKya6G0cGw8CAQOjgfwwgfkwHQYDVR0OBBYEFI0cxb6VTEM8YYY6FbBMvAPyT+CyMIHJBgNVHSMEgcEwgb6AFI0cxb6VTEM8YYY6FbBMvAPyT+CyoYGapIGXMIGUMQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEQMA4GA1UEChMHQW5kcm9pZDEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDEiMCAGCSqGSIb3DQEJARYTYW5kcm9pZEBhbmRyb2lkLmNvbYIJANWFuGx90071MAwGA1UdEwQFMAMBAf8wDQYJKoZIhvcNAQEEBQADggEBABnTDPEF+3iSP0wNfdIjIz1AlnrPzgAIHVvXxunW7SBrDhEglQZBbKJEk5kT0mtKoOD1JMrSu1xuTKEBahWRbqHsXclaXjoBADb0kkjVEJu/Lh5hgYZnOjvlba8Ld7HCKePCVePoTJBdI4fvugnL8TsgK05aIskyY0hKI9L8KfqfGTl1lzOv2KoWD0KWwtAWPoGChZxmQ+nBli+gwYMzM1vAkP+aayLe0a1EQimlOalO762r0GXO0ks+UeXde2Z4e+8S/pf7pITEI/tP+MxJTALw9QUWEv9lKTk+jkbqxbsh8nfBUapfKqYn0eidpwq2AzVp3juYl7//fKnaPhJD9gs=
        </item>
    </string-array><string-array name="com_google_android_gms_fonts_certs_prod">
        <item>
            MIIEQzCCAyugAwIBAgIJAMLgh0ZkSjCNMA0GCSqGSIb3DQEBBAUAMHQxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpDYWxpZm9ybmlhMRYwFAYDVQQHEw1Nb3VudGFpbiBWaWV3MRQwEgYDVQQKEwtHb29nbGUgSW5jLjEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDAeFw0wODA4MjEyMzEzMzRaFw0zNjAxMDcyMzEzMzRaMHQxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpDYWxpZm9ybmlhMRYwFAYDVQQHEw1Nb3VudGFpbiBWaWV3MRQwEgYDVQQKEwtHb29nbGUgSW5jLjEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDCCASAwDQYJKoZIhvcNAQEBBQADggENADCCAQgCggEBAKtWLgDYO6IIrgqWbxJOKdoR8qtW0I9Y4sypEwPpt1TTcvZApxsdyxMJZ2JORland2qSGT2y5b+3JKkedxiLDmpHpDsz2WCbdxgxRczfey5YZnTJ4VZbH0xqWVW/8lGmPav5xVwnIiJS6HXk+BVKZF+JcWjAsb/GEuq/eFdpuzSqeYTcfi6idkyugwfYwXFU1+5fZKUaRKYCwkkFQVfcAs1fXA5V+++FGfvjJ/CxURaSxaBvGdGDhfXE28LWuT9ozCl5xw4Yq5OGazvV24mZVSoOO0yZ31j7kYvtwYK6NeADwbSxDdJEqO4k//0zOHKrUiGYXtqw/A0LFFtqoZKFjnkCAQOjgdkwgdYwHQYDVR0OBBYEFMd9jMIhF1Ylmn/Tgt9r45jk14alMIGmBgNVHSMEgZ4wgZuAFMd9jMIhF1Ylmn/Tgt9r45jk14aloXikdjB0MQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEUMBIGA1UEChMLR29vZ2xlIEluYy4xEDAOBgNVBAsTB0FuZHJvaWQxEDAOBgNVBAMTB0FuZHJvaWSCCQDC4IdGZEowjTAMBgNVHRMEBTADAQH/MA0GCSqGSIb3DQEBBAUAA4IBAQBt0lLO74UwLDYKqs6Tm8/yzKkEu116FmH4rkaymUIE0P9KaMftGlMexFlaYjzmB2OxZyl6euNXEsQH8gjwyxCUKRJNexBiGcCEyj6z+a1fuHHvkiaai+KL8W1EyNmgjmyy8AW7P+LLlkR+ho5zEHatRbM/YAnqGcFh5iZBqpknHf1SKMXFh4dd239FJ1jWYfbMDMy3NS5CTMQ2XFI1MvcyUTdZPErjQfTbQe3aDQsQcafEQPD+nqActifKZ0Np0IS9L9kR/wbNvyz6ENwPiTrjV2KRkEjH78ZMcUQXg0L3BYHJ3lc69Vs5Ddf9uUGGMYldX3WfMBEmh/9iFBDAaTCK
        </item>
    </string-array></file><file path="D:\android project\new big 22-06\app\src\main\res\values\preloaded_fonts.xml" qualifiers=""><array name="preloaded_fonts" translatable="false">
        <item>@font/poppins</item>
        <item>@font/poppins_bold</item>
        <item>@font/poppins_light</item>
        <item>@font/poppins_medium</item>
        <item>@font/poppins_semibold</item>
        <item>@font/stardos_stencil</item>
    </array></file><file path="D:\android project\new big 22-06\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Big Wheel</string><string name="and_i_agree_with_its">and i agree with its</string><string name="LOADING">Loading...</string><string name="phone_number"><font size="13">Enter Phone Number without +91</font></string><string name="drawer_open">open</string><string name="drawer_close">close</string><string name="some_error_occurred">Some error occurred...</string><string name="transaction_cancelled_by_user">Transaction cancelled by user</string><string name="pay_via_si">Pay using SI</string><string name="billingCycle">billingCycle</string><string name="api_version">api_version</string><string name="si">si</string><string name="billingInterval">billingInterval</string><string name="billingAmount">billingAmount</string><string name="billingCurrency">billingCurrency</string><string name="paymentStartDate">paymentStartDate</string><string name="paymentEndDate">paymentEndDate</string><string name="remarks">remarks</string><string name="si_date_hint">YYYY-MM-DD</string><string name="free_trial">Free Trial</string><string name="sign_in_with_google">Sign in With Google</string></file><file path="D:\android project\new big 22-06\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.BigWheel" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style><style name="BottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheetStyle</item>
    </style><style name="BottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/black</item>
    </style><style name="MyDialogTheme">
        <item name="android:typeface">monospace</item>
        <item name="android:windowEnterAnimation">@anim/dialog_in</item>
        <item name="android:windowExitAnimation">@anim/dialog_out</item>
    </style><style name="NavigationView">
        <item name="android:textColorSecondary">@color/white</item>
    </style><style name="ButtonStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingBottom">@dimen/_10sdp</item>
        <item name="android:paddingTop">@dimen/_10sdp</item>
        <item name="android:textColor">#000</item>
        <item name="android:textSize">@dimen/_18sdp</item>
        <item name="android:background">@drawable/bg_rounded_button</item>
        <item name="android:gravity">center</item>
    </style></file><file path="D:\android project\new big 22-06\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.BigWheel" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style><style name="BottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheetStyle</item>
    </style><style name="BottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
    </style><style name="MyDialogTheme">
        <item name="android:typeface">monospace</item>
        <item name="android:windowEnterAnimation">@anim/dialog_in</item>
        <item name="android:windowExitAnimation">@anim/dialog_out</item>
    </style><style name="NavigationView">
        <item name="android:textColorSecondary">@color/white</item>
    </style><style name="ButtonStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingBottom">@dimen/_10sdp</item>
        <item name="android:paddingTop">@dimen/_10sdp</item>
        <item name="android:textColor">#ffffff</item>
        <item name="android:textSize">@dimen/_18sdp</item>
        <item name="android:background">@drawable/bg_rounded_button</item>
        <item name="android:gravity">center</item>
    </style></file><file name="bg_card_basic" path="D:\android project\new big 22-06\app\src\main\res\drawable\bg_card_basic.xml" qualifiers="" type="drawable"/><file name="bg_card_popular" path="D:\android project\new big 22-06\app\src\main\res\drawable\bg_card_popular.xml" qualifiers="" type="drawable"/><file name="bg_card_premium" path="D:\android project\new big 22-06\app\src\main\res\drawable\bg_card_premium.xml" qualifiers="" type="drawable"/><file name="bg_card_vip" path="D:\android project\new big 22-06\app\src\main\res\drawable\bg_card_vip.xml" qualifiers="" type="drawable"/><file name="bg_card_selected" path="D:\android project\new big 22-06\app\src\main\res\drawable\bg_card_selected.xml" qualifiers="" type="drawable"/><file name="ic_google_pay" path="D:\android project\new big 22-06\app\src\main\res\drawable\ic_google_pay.xml" qualifiers="" type="drawable"/><file name="ic_phonepe" path="D:\android project\new big 22-06\app\src\main\res\drawable\ic_phonepe.xml" qualifiers="" type="drawable"/><file name="dialog_payment_options" path="D:\android project\new big 22-06\app\src\main\res\layout\dialog_payment_options.xml" qualifiers="" type="layout"/><file name="dialog_payment_success" path="D:\android project\new big 22-06\app\src\main\res\layout\dialog_payment_success.xml" qualifiers="" type="layout"/></source><source path="D:\android project\new big 22-06\app\build\generated\res\rs\debug"/><source path="D:\android project\new big 22-06\app\build\generated\res\resValues\debug"/><source path="D:\android project\new big 22-06\app\build\generated\res\google-services\debug"><file path="D:\android project\new big 22-06\app\build\generated\res\google-services\debug\values\values.xml" qualifiers=""><string name="default_web_client_id" translatable="false">429680107287-a094s0hn92a21q2jm6jlfb5ra5hc1g7s.apps.googleusercontent.com</string><string name="firebase_database_url" translatable="false">https://big-wheel-72b71-default-rtdb.firebaseio.com</string><string name="gcm_defaultSenderId" translatable="false">429680107287</string><string name="google_api_key" translatable="false">AIzaSyAbzP4GDlRGPIix03GhgvURwkV-2i7OvpU</string><string name="google_app_id" translatable="false">1:429680107287:android:23b63ee1c5478704cca152</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyAbzP4GDlRGPIix03GhgvURwkV-2i7OvpU</string><string name="google_storage_bucket" translatable="false">big-wheel-72b71.firebasestorage.app</string><string name="project_id" translatable="false">big-wheel-72b71</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\android project\new big 22-06\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\android project\new big 22-06\app\src\debug\res"/></dataSet><mergedItems/></merger>