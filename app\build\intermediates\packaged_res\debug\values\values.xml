<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <array name="com_google_android_gms_fonts_certs">
        <item>@array/com_google_android_gms_fonts_certs_dev</item>
        <item>@array/com_google_android_gms_fonts_certs_prod</item>
    </array>
    <string-array name="com_google_android_gms_fonts_certs_dev">
        <item>
            MIIEqDCCA5CgAwIBAgIJANWFuGx90071MA0GCSqGSIb3DQEBBAUAMIGUMQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEQMA4GA1UEChMHQW5kcm9pZDEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDEiMCAGCSqGSIb3DQEJARYTYW5kcm9pZEBhbmRyb2lkLmNvbTAeFw0wODA0MTUyMzM2NTZaFw0zNTA5MDEyMzM2NTZaMIGUMQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEQMA4GA1UEChMHQW5kcm9pZDEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDEiMCAGCSqGSIb3DQEJARYTYW5kcm9pZEBhbmRyb2lkLmNvbTCCASAwDQYJKoZIhvcNAQEBBQADggENADCCAQgCggEBANbOLggKv+IxTdGNs8/TGFy0PTP6DHThvbbR24kT9ixcOd9W+EaBPWW+wPPKQmsHxajtWjmQwWfna8mZuSeJS48LIgAZlKkpFeVyxW0qMBujb8X8ETrWy550NaFtI6t9+u7hZeTfHwqNvacKhp1RbE6dBRGWynwMVX8XW8N1+UjFaq6GCJukT4qmpN2afb8sCjUigq0GuMwYXrFVee74bQgLHWGJwPmvmLHC69EH6kWr22ijx4OKXlSIx2xT1AsSHee70w5iDBiK4aph27yH3TxkXy9V89TDdexAcKk/cVHYNnDBapcavl7y0RiQ4biu8ymM8Ga/nmzhRKya6G0cGw8CAQOjgfwwgfkwHQYDVR0OBBYEFI0cxb6VTEM8YYY6FbBMvAPyT+CyMIHJBgNVHSMEgcEwgb6AFI0cxb6VTEM8YYY6FbBMvAPyT+CyoYGapIGXMIGUMQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEQMA4GA1UEChMHQW5kcm9pZDEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDEiMCAGCSqGSIb3DQEJARYTYW5kcm9pZEBhbmRyb2lkLmNvbYIJANWFuGx90071MAwGA1UdEwQFMAMBAf8wDQYJKoZIhvcNAQEEBQADggEBABnTDPEF+3iSP0wNfdIjIz1AlnrPzgAIHVvXxunW7SBrDhEglQZBbKJEk5kT0mtKoOD1JMrSu1xuTKEBahWRbqHsXclaXjoBADb0kkjVEJu/Lh5hgYZnOjvlba8Ld7HCKePCVePoTJBdI4fvugnL8TsgK05aIskyY0hKI9L8KfqfGTl1lzOv2KoWD0KWwtAWPoGChZxmQ+nBli+gwYMzM1vAkP+aayLe0a1EQimlOalO762r0GXO0ks+UeXde2Z4e+8S/pf7pITEI/tP+MxJTALw9QUWEv9lKTk+jkbqxbsh8nfBUapfKqYn0eidpwq2AzVp3juYl7//fKnaPhJD9gs=
        </item>
    </string-array>
    <string-array name="com_google_android_gms_fonts_certs_prod">
        <item>
            MIIEQzCCAyugAwIBAgIJAMLgh0ZkSjCNMA0GCSqGSIb3DQEBBAUAMHQxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpDYWxpZm9ybmlhMRYwFAYDVQQHEw1Nb3VudGFpbiBWaWV3MRQwEgYDVQQKEwtHb29nbGUgSW5jLjEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDAeFw0wODA4MjEyMzEzMzRaFw0zNjAxMDcyMzEzMzRaMHQxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpDYWxpZm9ybmlhMRYwFAYDVQQHEw1Nb3VudGFpbiBWaWV3MRQwEgYDVQQKEwtHb29nbGUgSW5jLjEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDCCASAwDQYJKoZIhvcNAQEBBQADggENADCCAQgCggEBAKtWLgDYO6IIrgqWbxJOKdoR8qtW0I9Y4sypEwPpt1TTcvZApxsdyxMJZ2JORland2qSGT2y5b+3JKkedxiLDmpHpDsz2WCbdxgxRczfey5YZnTJ4VZbH0xqWVW/8lGmPav5xVwnIiJS6HXk+BVKZF+JcWjAsb/GEuq/eFdpuzSqeYTcfi6idkyugwfYwXFU1+5fZKUaRKYCwkkFQVfcAs1fXA5V+++FGfvjJ/CxURaSxaBvGdGDhfXE28LWuT9ozCl5xw4Yq5OGazvV24mZVSoOO0yZ31j7kYvtwYK6NeADwbSxDdJEqO4k//0zOHKrUiGYXtqw/A0LFFtqoZKFjnkCAQOjgdkwgdYwHQYDVR0OBBYEFMd9jMIhF1Ylmn/Tgt9r45jk14alMIGmBgNVHSMEgZ4wgZuAFMd9jMIhF1Ylmn/Tgt9r45jk14aloXikdjB0MQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEUMBIGA1UEChMLR29vZ2xlIEluYy4xEDAOBgNVBAsTB0FuZHJvaWQxEDAOBgNVBAMTB0FuZHJvaWSCCQDC4IdGZEowjTAMBgNVHRMEBTADAQH/MA0GCSqGSIb3DQEBBAUAA4IBAQBt0lLO74UwLDYKqs6Tm8/yzKkEu116FmH4rkaymUIE0P9KaMftGlMexFlaYjzmB2OxZyl6euNXEsQH8gjwyxCUKRJNexBiGcCEyj6z+a1fuHHvkiaai+KL8W1EyNmgjmyy8AW7P+LLlkR+ho5zEHatRbM/YAnqGcFh5iZBqpknHf1SKMXFh4dd239FJ1jWYfbMDMy3NS5CTMQ2XFI1MvcyUTdZPErjQfTbQe3aDQsQcafEQPD+nqActifKZ0Np0IS9L9kR/wbNvyz6ENwPiTrjV2KRkEjH78ZMcUQXg0L3BYHJ3lc69Vs5Ddf9uUGGMYldX3WfMBEmh/9iFBDAaTCK
        </item>
    </string-array>
    <array name="preloaded_fonts" translatable="false">
        <item>@font/poppins</item>
        <item>@font/poppins_bold</item>
        <item>@font/poppins_light</item>
        <item>@font/poppins_medium</item>
        <item>@font/poppins_semibold</item>
        <item>@font/stardos_stencil</item>
    </array>
    <color name="bg_card">#FFFFFF</color>
    <color name="bg_overlay">#80000000</color>
    <color name="bg_primary">#1F2937</color>
    <color name="bg_secondary">#374151</color>
    <color name="black">#FF000000</color>
    <color name="blue">#6432BE</color>
    <color name="error_red">#EF4444</color>
    <color name="gradient_primary_end">#764BA2</color>
    <color name="gradient_primary_start">#667EEA</color>
    <color name="gradient_secondary_end">#F5576C</color>
    <color name="gradient_secondary_start">#F093FB</color>
    <color name="gradient_success_end">#00F2FE</color>
    <color name="gradient_success_start">#4FACFE</color>
    <color name="gradient_warning_end">#22C1C3</color>
    <color name="gradient_warning_start">#FDBB2D</color>
    <color name="gray">#666666</color>
    <color name="info_blue">#3B82F6</color>
    <color name="light_white">#fff</color>
    <color name="luxury_bronze">#CD7F32</color>
    <color name="newWhitr">#ffffff</color>
    <color name="payumoney_black">#000</color>
    <color name="plan_basic">#E3F2FD</color>
    <color name="plan_basic_border">#2196F3</color>
    <color name="plan_popular">#FFF3E0</color>
    <color name="plan_popular_border">#FF9800</color>
    <color name="plan_premium">#F3E5F5</color>
    <color name="plan_premium_border">#9C27B0</color>
    <color name="plan_vip">#FFF8E1</color>
    <color name="plan_vip_border">#FFD700</color>
    <color name="premium_gold">#FFD700</color>
    <color name="premium_gold_dark">#B8860B</color>
    <color name="premium_gold_light">#FFF8DC</color>
    <color name="purple_200">#f27125</color>
    <color name="purple_500">#f27125</color>
    <color name="purple_700">#303F9F</color>
    <color name="purple_7001">#6F1F7C</color>
    <color name="red_btn_bg_color">#f27125</color>
    <color name="shadow_dark">#4D000000</color>
    <color name="shadow_light">#1A000000</color>
    <color name="shadow_medium">#33000000</color>
    <color name="success_green">#10B981</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_accent">#6366F1</color>
    <color name="text_primary">#111827</color>
    <color name="text_secondary">#6B7280</color>
    <color name="text_white">#FFFFFF</color>
    <color name="themeBgColor">#1A1B3A</color>
    <color name="themeColor">#6366F1</color>
    <color name="themeColor2">#581945</color>
    <color name="themeColor22">#8B5CF6</color>
    <color name="tran">#0055322F</color>
    <color name="warning_orange">#F59E0B</color>
    <color name="white">#FFFFFFFF</color>
    <string name="LOADING">Loading...</string>
    <string name="and_i_agree_with_its">and i agree with its</string>
    <string name="api_version">api_version</string>
    <string name="app_name">Big Wheel</string>
    <string name="billingAmount">billingAmount</string>
    <string name="billingCurrency">billingCurrency</string>
    <string name="billingCycle">billingCycle</string>
    <string name="billingInterval">billingInterval</string>
    <string name="default_web_client_id" translatable="false">429680107287-a094s0hn92a21q2jm6jlfb5ra5hc1g7s.apps.googleusercontent.com</string>
    <string name="drawer_close">close</string>
    <string name="drawer_open">open</string>
    <string name="firebase_database_url" translatable="false">https://big-wheel-72b71-default-rtdb.firebaseio.com</string>
    <string name="free_trial">Free Trial</string>
    <string name="gcm_defaultSenderId" translatable="false">429680107287</string>
    <string name="google_api_key" translatable="false">AIzaSyAbzP4GDlRGPIix03GhgvURwkV-2i7OvpU</string>
    <string name="google_app_id" translatable="false">1:429680107287:android:23b63ee1c5478704cca152</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyAbzP4GDlRGPIix03GhgvURwkV-2i7OvpU</string>
    <string name="google_storage_bucket" translatable="false">big-wheel-72b71.firebasestorage.app</string>
    <string name="pay_via_si">Pay using SI</string>
    <string name="paymentEndDate">paymentEndDate</string>
    <string name="paymentStartDate">paymentStartDate</string>
    <string name="phone_number"><font size="13">Enter Phone Number without +91</font></string>
    <string name="project_id" translatable="false">big-wheel-72b71</string>
    <string name="remarks">remarks</string>
    <string name="si">si</string>
    <string name="si_date_hint">YYYY-MM-DD</string>
    <string name="sign_in_with_google">Sign in With Google</string>
    <string name="some_error_occurred">Some error occurred...</string>
    <string name="transaction_cancelled_by_user">Transaction cancelled by user</string>
    <style name="BottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheetStyle</item>
    </style>
    <style name="BottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/black</item>
    </style>
    <style name="ButtonStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingBottom">@dimen/_10sdp</item>
        <item name="android:paddingTop">@dimen/_10sdp</item>
        <item name="android:textColor">#000</item>
        <item name="android:textSize">@dimen/_18sdp</item>
        <item name="android:background">@drawable/bg_rounded_button</item>
        <item name="android:gravity">center</item>
    </style>
    <style name="MyDialogTheme">
        <item name="android:typeface">monospace</item>
        <item name="android:windowEnterAnimation">@anim/dialog_in</item>
        <item name="android:windowExitAnimation">@anim/dialog_out</item>
    </style>
    <style name="NavigationView">
        <item name="android:textColorSecondary">@color/white</item>
    </style>
    <style name="Theme.BigWheel" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>
</resources>