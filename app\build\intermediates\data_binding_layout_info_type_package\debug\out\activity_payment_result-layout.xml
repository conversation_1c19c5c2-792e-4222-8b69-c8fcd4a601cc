<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_payment_result" modulePackage="com.bigwheel.winners" filePath="app\src\main\res\layout\activity_payment_result.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_payment_result_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="218" endOffset="14"/></Target><Target id="@+id/card_view" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="17" startOffset="4" endLine="215" endOffset="39"/></Target><Target id="@+id/animationView" view="com.airbnb.lottie.LottieAnimationView"><Expressions/><location startLine="40" startOffset="16" endLine="46" endOffset="43"/></Target><Target id="@+id/finaldata" view="TextView"><Expressions/><location startLine="55" startOffset="16" endLine="65" endOffset="21"/></Target><Target id="@+id/textinfo" view="TextView"><Expressions/><location startLine="67" startOffset="16" endLine="77" endOffset="21"/></Target><Target id="@+id/date" view="TextView"><Expressions/><location startLine="79" startOffset="16" endLine="89" endOffset="21"/></Target><Target id="@+id/tranID" view="TextView"><Expressions/><location startLine="110" startOffset="16" endLine="120" endOffset="21"/></Target><Target id="@+id/tranAmu" view="TextView"><Expressions/><location startLine="141" startOffset="16" endLine="151" endOffset="21"/></Target><Target id="@+id/tranemail" view="TextView"><Expressions/><location startLine="172" startOffset="16" endLine="182" endOffset="21"/></Target><Target id="@+id/okbtn" view="Button"><Expressions/><location startLine="194" startOffset="16" endLine="204" endOffset="21"/></Target></Targets></Layout>