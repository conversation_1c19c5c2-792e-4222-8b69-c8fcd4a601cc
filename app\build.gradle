plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'com.onesignal.androidsdk.onesignal-gradle-plugin'
}

apply plugin: 'com.google.gms.google-services'
android {
    compileSdk 32

    defaultConfig {
        applicationId "com.bigwheel.winners"
        minSdk 22
        targetSdk 32
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
        packagingOptions {
            exclude 'META-INF/services/javax.annotation.processing.Processor'
        }
        buildFeatures {
            viewBinding true
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.5.0'
    implementation 'com.google.android.material:material:1.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'


    implementation 'com.android.support.constraint:constraint-layout:2.0.4'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'

    // onsignal
    implementation 'com.onesignal:OneSignal:[4.0.0, 4.99.99]'
    // spin wheel
    implementation project(':luckyWheel')
    implementation 'com.github.f0ris.sweetalert:library:1.6.2'
    implementation 'com.airbnb.android:lottie:5.2.0'

    // firebase
    implementation platform('com.google.firebase:firebase-bom:26.7.0')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-database:20.1.0'

    // google login
    implementation 'com.google.android.gms:play-services-auth:20.4.0'

    // volley
    implementation 'com.android.volley:volley:1.2.1'
    implementation 'de.hdodenhof:circleimageview:3.1.0'
    implementation 'com.squareup.picasso:picasso:2.71828'

    // kotlin
    implementation 'com.intuit.sdp:sdp-android:1.0.6'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'androidx.core:core-ktx:1.8.0'
    implementation 'androidx.cardview:cardview:1.0.0'

    //payumoney
//     implementation('com.payu.olamoney:olamoney:1.0.1'){
//         exclude group: 'com.payu.paymentparamhelper', module: 'paymentparamhelper'
//       }
//     implementation 'com.payu.gpay:payu-gpay:1.3.6'
//      implementation 'com.payu.india:payu-checkout-pro:1.3.1'

    implementation 'com.airbnb.android:lottie:5.2.0'




}