// Generated by view binder compiler. Do not edit!
package com.bigwheel.winners.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatRadioButton;
import androidx.appcompat.widget.SwitchCompat;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bigwheel.winners.R;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainsBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final Button btnAddItem;

  @NonNull
  public final CoordinatorLayout clMain;

  @NonNull
  public final EditText etAmount;

  @NonNull
  public final EditText etFurl;

  @NonNull
  public final EditText etKey;

  @NonNull
  public final EditText etMerchantName;

  @NonNull
  public final EditText etPhone;

  @NonNull
  public final EditText etSalt;

  @NonNull
  public final EditText etSurePayCount;

  @NonNull
  public final EditText etSurl;

  @NonNull
  public final EditText etUserCredential;

  @NonNull
  public final LayoutSiDetailsBinding layoutSiDetails;

  @NonNull
  public final Button payNowButton;

  @NonNull
  public final AppCompatRadioButton radioBtnProduction;

  @NonNull
  public final AppCompatRadioButton radioBtnTest;

  @NonNull
  public final RadioGroup radioGrpEnv;

  @NonNull
  public final RelativeLayout rlReviewOrder;

  @NonNull
  public final RelativeLayout rlSiHeader;

  @NonNull
  public final RecyclerView rvReviewOrder;

  @NonNull
  public final SwitchCompat switchAutoApprove;

  @NonNull
  public final SwitchCompat switchAutoSelectOtp;

  @NonNull
  public final SwitchCompat switchDiableCBDialog;

  @NonNull
  public final SwitchCompat switchDiableUiDialog;

  @NonNull
  public final SwitchCompat switchEnableReviewOrder;

  @NonNull
  public final SwitchCompat switchHideCbToolBar;

  @NonNull
  public final SwitchCompat switchShowGooglePay;

  @NonNull
  public final SwitchCompat switchShowPaytm;

  @NonNull
  public final SwitchCompat switchShowPhonePe;

  @NonNull
  public final SwitchCompat switchSiOnOff;

  @NonNull
  public final TextInputLayout tilAmount;

  @NonNull
  public final TextInputLayout tilFurl;

  @NonNull
  public final TextInputLayout tilKey;

  @NonNull
  public final TextInputLayout tilMerchantName;

  @NonNull
  public final TextInputLayout tilPhone;

  @NonNull
  public final TextInputLayout tilSalt;

  @NonNull
  public final TextInputLayout tilSurePayCount;

  @NonNull
  public final TextInputLayout tilSurl;

  @NonNull
  public final TextInputLayout tilUserCredential;

  @NonNull
  public final TextView tvPayViaSi;

  private ActivityMainsBinding(@NonNull CoordinatorLayout rootView, @NonNull Button btnAddItem,
      @NonNull CoordinatorLayout clMain, @NonNull EditText etAmount, @NonNull EditText etFurl,
      @NonNull EditText etKey, @NonNull EditText etMerchantName, @NonNull EditText etPhone,
      @NonNull EditText etSalt, @NonNull EditText etSurePayCount, @NonNull EditText etSurl,
      @NonNull EditText etUserCredential, @NonNull LayoutSiDetailsBinding layoutSiDetails,
      @NonNull Button payNowButton, @NonNull AppCompatRadioButton radioBtnProduction,
      @NonNull AppCompatRadioButton radioBtnTest, @NonNull RadioGroup radioGrpEnv,
      @NonNull RelativeLayout rlReviewOrder, @NonNull RelativeLayout rlSiHeader,
      @NonNull RecyclerView rvReviewOrder, @NonNull SwitchCompat switchAutoApprove,
      @NonNull SwitchCompat switchAutoSelectOtp, @NonNull SwitchCompat switchDiableCBDialog,
      @NonNull SwitchCompat switchDiableUiDialog, @NonNull SwitchCompat switchEnableReviewOrder,
      @NonNull SwitchCompat switchHideCbToolBar, @NonNull SwitchCompat switchShowGooglePay,
      @NonNull SwitchCompat switchShowPaytm, @NonNull SwitchCompat switchShowPhonePe,
      @NonNull SwitchCompat switchSiOnOff, @NonNull TextInputLayout tilAmount,
      @NonNull TextInputLayout tilFurl, @NonNull TextInputLayout tilKey,
      @NonNull TextInputLayout tilMerchantName, @NonNull TextInputLayout tilPhone,
      @NonNull TextInputLayout tilSalt, @NonNull TextInputLayout tilSurePayCount,
      @NonNull TextInputLayout tilSurl, @NonNull TextInputLayout tilUserCredential,
      @NonNull TextView tvPayViaSi) {
    this.rootView = rootView;
    this.btnAddItem = btnAddItem;
    this.clMain = clMain;
    this.etAmount = etAmount;
    this.etFurl = etFurl;
    this.etKey = etKey;
    this.etMerchantName = etMerchantName;
    this.etPhone = etPhone;
    this.etSalt = etSalt;
    this.etSurePayCount = etSurePayCount;
    this.etSurl = etSurl;
    this.etUserCredential = etUserCredential;
    this.layoutSiDetails = layoutSiDetails;
    this.payNowButton = payNowButton;
    this.radioBtnProduction = radioBtnProduction;
    this.radioBtnTest = radioBtnTest;
    this.radioGrpEnv = radioGrpEnv;
    this.rlReviewOrder = rlReviewOrder;
    this.rlSiHeader = rlSiHeader;
    this.rvReviewOrder = rvReviewOrder;
    this.switchAutoApprove = switchAutoApprove;
    this.switchAutoSelectOtp = switchAutoSelectOtp;
    this.switchDiableCBDialog = switchDiableCBDialog;
    this.switchDiableUiDialog = switchDiableUiDialog;
    this.switchEnableReviewOrder = switchEnableReviewOrder;
    this.switchHideCbToolBar = switchHideCbToolBar;
    this.switchShowGooglePay = switchShowGooglePay;
    this.switchShowPaytm = switchShowPaytm;
    this.switchShowPhonePe = switchShowPhonePe;
    this.switchSiOnOff = switchSiOnOff;
    this.tilAmount = tilAmount;
    this.tilFurl = tilFurl;
    this.tilKey = tilKey;
    this.tilMerchantName = tilMerchantName;
    this.tilPhone = tilPhone;
    this.tilSalt = tilSalt;
    this.tilSurePayCount = tilSurePayCount;
    this.tilSurl = tilSurl;
    this.tilUserCredential = tilUserCredential;
    this.tvPayViaSi = tvPayViaSi;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_mains, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnAddItem;
      Button btnAddItem = ViewBindings.findChildViewById(rootView, id);
      if (btnAddItem == null) {
        break missingId;
      }

      CoordinatorLayout clMain = (CoordinatorLayout) rootView;

      id = R.id.etAmount;
      EditText etAmount = ViewBindings.findChildViewById(rootView, id);
      if (etAmount == null) {
        break missingId;
      }

      id = R.id.etFurl;
      EditText etFurl = ViewBindings.findChildViewById(rootView, id);
      if (etFurl == null) {
        break missingId;
      }

      id = R.id.etKey;
      EditText etKey = ViewBindings.findChildViewById(rootView, id);
      if (etKey == null) {
        break missingId;
      }

      id = R.id.etMerchantName;
      EditText etMerchantName = ViewBindings.findChildViewById(rootView, id);
      if (etMerchantName == null) {
        break missingId;
      }

      id = R.id.etPhone;
      EditText etPhone = ViewBindings.findChildViewById(rootView, id);
      if (etPhone == null) {
        break missingId;
      }

      id = R.id.etSalt;
      EditText etSalt = ViewBindings.findChildViewById(rootView, id);
      if (etSalt == null) {
        break missingId;
      }

      id = R.id.etSurePayCount;
      EditText etSurePayCount = ViewBindings.findChildViewById(rootView, id);
      if (etSurePayCount == null) {
        break missingId;
      }

      id = R.id.etSurl;
      EditText etSurl = ViewBindings.findChildViewById(rootView, id);
      if (etSurl == null) {
        break missingId;
      }

      id = R.id.etUserCredential;
      EditText etUserCredential = ViewBindings.findChildViewById(rootView, id);
      if (etUserCredential == null) {
        break missingId;
      }

      id = R.id.layout_si_details;
      View layoutSiDetails = ViewBindings.findChildViewById(rootView, id);
      if (layoutSiDetails == null) {
        break missingId;
      }
      LayoutSiDetailsBinding binding_layoutSiDetails = LayoutSiDetailsBinding.bind(layoutSiDetails);

      id = R.id.pay_now_button;
      Button payNowButton = ViewBindings.findChildViewById(rootView, id);
      if (payNowButton == null) {
        break missingId;
      }

      id = R.id.radioBtnProduction;
      AppCompatRadioButton radioBtnProduction = ViewBindings.findChildViewById(rootView, id);
      if (radioBtnProduction == null) {
        break missingId;
      }

      id = R.id.radioBtnTest;
      AppCompatRadioButton radioBtnTest = ViewBindings.findChildViewById(rootView, id);
      if (radioBtnTest == null) {
        break missingId;
      }

      id = R.id.radioGrpEnv;
      RadioGroup radioGrpEnv = ViewBindings.findChildViewById(rootView, id);
      if (radioGrpEnv == null) {
        break missingId;
      }

      id = R.id.rlReviewOrder;
      RelativeLayout rlReviewOrder = ViewBindings.findChildViewById(rootView, id);
      if (rlReviewOrder == null) {
        break missingId;
      }

      id = R.id.rl_si_header;
      RelativeLayout rlSiHeader = ViewBindings.findChildViewById(rootView, id);
      if (rlSiHeader == null) {
        break missingId;
      }

      id = R.id.rvReviewOrder;
      RecyclerView rvReviewOrder = ViewBindings.findChildViewById(rootView, id);
      if (rvReviewOrder == null) {
        break missingId;
      }

      id = R.id.switchAutoApprove;
      SwitchCompat switchAutoApprove = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoApprove == null) {
        break missingId;
      }

      id = R.id.switchAutoSelectOtp;
      SwitchCompat switchAutoSelectOtp = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoSelectOtp == null) {
        break missingId;
      }

      id = R.id.switchDiableCBDialog;
      SwitchCompat switchDiableCBDialog = ViewBindings.findChildViewById(rootView, id);
      if (switchDiableCBDialog == null) {
        break missingId;
      }

      id = R.id.switchDiableUiDialog;
      SwitchCompat switchDiableUiDialog = ViewBindings.findChildViewById(rootView, id);
      if (switchDiableUiDialog == null) {
        break missingId;
      }

      id = R.id.switchEnableReviewOrder;
      SwitchCompat switchEnableReviewOrder = ViewBindings.findChildViewById(rootView, id);
      if (switchEnableReviewOrder == null) {
        break missingId;
      }

      id = R.id.switchHideCbToolBar;
      SwitchCompat switchHideCbToolBar = ViewBindings.findChildViewById(rootView, id);
      if (switchHideCbToolBar == null) {
        break missingId;
      }

      id = R.id.switchShowGooglePay;
      SwitchCompat switchShowGooglePay = ViewBindings.findChildViewById(rootView, id);
      if (switchShowGooglePay == null) {
        break missingId;
      }

      id = R.id.switchShowPaytm;
      SwitchCompat switchShowPaytm = ViewBindings.findChildViewById(rootView, id);
      if (switchShowPaytm == null) {
        break missingId;
      }

      id = R.id.switchShowPhonePe;
      SwitchCompat switchShowPhonePe = ViewBindings.findChildViewById(rootView, id);
      if (switchShowPhonePe == null) {
        break missingId;
      }

      id = R.id.switch_si_on_off;
      SwitchCompat switchSiOnOff = ViewBindings.findChildViewById(rootView, id);
      if (switchSiOnOff == null) {
        break missingId;
      }

      id = R.id.tilAmount;
      TextInputLayout tilAmount = ViewBindings.findChildViewById(rootView, id);
      if (tilAmount == null) {
        break missingId;
      }

      id = R.id.tilFurl;
      TextInputLayout tilFurl = ViewBindings.findChildViewById(rootView, id);
      if (tilFurl == null) {
        break missingId;
      }

      id = R.id.tilKey;
      TextInputLayout tilKey = ViewBindings.findChildViewById(rootView, id);
      if (tilKey == null) {
        break missingId;
      }

      id = R.id.tilMerchantName;
      TextInputLayout tilMerchantName = ViewBindings.findChildViewById(rootView, id);
      if (tilMerchantName == null) {
        break missingId;
      }

      id = R.id.tilPhone;
      TextInputLayout tilPhone = ViewBindings.findChildViewById(rootView, id);
      if (tilPhone == null) {
        break missingId;
      }

      id = R.id.tilSalt;
      TextInputLayout tilSalt = ViewBindings.findChildViewById(rootView, id);
      if (tilSalt == null) {
        break missingId;
      }

      id = R.id.tilSurePayCount;
      TextInputLayout tilSurePayCount = ViewBindings.findChildViewById(rootView, id);
      if (tilSurePayCount == null) {
        break missingId;
      }

      id = R.id.tilSurl;
      TextInputLayout tilSurl = ViewBindings.findChildViewById(rootView, id);
      if (tilSurl == null) {
        break missingId;
      }

      id = R.id.tilUserCredential;
      TextInputLayout tilUserCredential = ViewBindings.findChildViewById(rootView, id);
      if (tilUserCredential == null) {
        break missingId;
      }

      id = R.id.tv_pay_via_si;
      TextView tvPayViaSi = ViewBindings.findChildViewById(rootView, id);
      if (tvPayViaSi == null) {
        break missingId;
      }

      return new ActivityMainsBinding((CoordinatorLayout) rootView, btnAddItem, clMain, etAmount,
          etFurl, etKey, etMerchantName, etPhone, etSalt, etSurePayCount, etSurl, etUserCredential,
          binding_layoutSiDetails, payNowButton, radioBtnProduction, radioBtnTest, radioGrpEnv,
          rlReviewOrder, rlSiHeader, rvReviewOrder, switchAutoApprove, switchAutoSelectOtp,
          switchDiableCBDialog, switchDiableUiDialog, switchEnableReviewOrder, switchHideCbToolBar,
          switchShowGooglePay, switchShowPaytm, switchShowPhonePe, switchSiOnOff, tilAmount,
          tilFurl, tilKey, tilMerchantName, tilPhone, tilSalt, tilSurePayCount, tilSurl,
          tilUserCredential, tvPayViaSi);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
