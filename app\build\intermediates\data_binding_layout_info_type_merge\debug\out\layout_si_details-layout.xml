<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="layout_si_details" modulePackage="com.bigwheel.winners" filePath="app\src\main\res\layout\layout_si_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/layout_si_details_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="240" endOffset="14"/></Target><Target id="@+id/et_api_version" view="EditText"><Expressions/><location startLine="11" startOffset="8" endLine="19" endOffset="64"/></Target><Target id="@+id/et_api_version_value" view="EditText"><Expressions/><location startLine="21" startOffset="8" endLine="27" endOffset="30"/></Target><Target id="@+id/et_si" view="EditText"><Expressions/><location startLine="35" startOffset="8" endLine="43" endOffset="64"/></Target><Target id="@+id/et_si_value" view="EditText"><Expressions/><location startLine="45" startOffset="8" endLine="51" endOffset="30"/></Target><Target id="@+id/et_billingCycle" view="EditText"><Expressions/><location startLine="59" startOffset="8" endLine="67" endOffset="64"/></Target><Target id="@+id/et_billingCycle_value" view="androidx.appcompat.widget.AppCompatSpinner"><Expressions/><location startLine="69" startOffset="8" endLine="73" endOffset="39"/></Target><Target id="@+id/et_billingInterval" view="EditText"><Expressions/><location startLine="81" startOffset="8" endLine="89" endOffset="64"/></Target><Target id="@+id/et_billingInterval_value" view="EditText"><Expressions/><location startLine="91" startOffset="8" endLine="97" endOffset="30"/></Target><Target id="@+id/et_billingAmount" view="EditText"><Expressions/><location startLine="105" startOffset="8" endLine="113" endOffset="64"/></Target><Target id="@+id/et_billingAmount_value" view="EditText"><Expressions/><location startLine="115" startOffset="8" endLine="121" endOffset="33"/></Target><Target id="@+id/et_billingCurrency" view="EditText"><Expressions/><location startLine="129" startOffset="8" endLine="137" endOffset="64"/></Target><Target id="@+id/et_billingCurrency_value" view="EditText"><Expressions/><location startLine="139" startOffset="8" endLine="145" endOffset="32"/></Target><Target id="@+id/et_paymentStartDate" view="EditText"><Expressions/><location startLine="153" startOffset="8" endLine="161" endOffset="64"/></Target><Target id="@+id/et_paymentStartDate_value" view="EditText"><Expressions/><location startLine="163" startOffset="8" endLine="169" endOffset="39"/></Target><Target id="@+id/et_paymentEndDate" view="EditText"><Expressions/><location startLine="177" startOffset="8" endLine="185" endOffset="64"/></Target><Target id="@+id/et_paymentEndDate_value" view="EditText"><Expressions/><location startLine="187" startOffset="8" endLine="193" endOffset="39"/></Target><Target id="@+id/sp_free_trial_text" view="EditText"><Expressions/><location startLine="202" startOffset="8" endLine="210" endOffset="64"/></Target><Target id="@+id/sp_free_trial" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="212" startOffset="8" endLine="216" endOffset="39"/></Target><Target id="@+id/et_remarks" view="EditText"><Expressions/><location startLine="224" startOffset="8" endLine="232" endOffset="64"/></Target><Target id="@+id/et_remarks_value" view="EditText"><Expressions/><location startLine="234" startOffset="8" endLine="238" endOffset="39"/></Target></Targets></Layout>