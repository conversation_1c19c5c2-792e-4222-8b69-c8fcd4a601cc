<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="150dp"
    android:background="@color/themeColor"
    android:orientation="horizontal"
    android:weightSum="3"
    android:theme="@style/ThemeOverlay.AppCompat.Dark">


 <LinearLayout
     android:layout_width="0dp"
     android:layout_height="match_parent"
     android:orientation="vertical"
     android:gravity="center"
     android:layout_marginTop="20dp"
     android:layout_weight="0.8">

    <de.hdodenhof.circleimageview.CircleImageView
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/userimg"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:src="@drawable/wheelss"
        app:civ_border_width="2dp"
        android:layout_gravity="center|center_vertical"
        app:civ_border_color="#FF000000"/>
 </LinearLayout>
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_marginTop="20dp"
        android:layout_weight="2.2">
    <TextView
        android:id="@+id/username"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:gravity="center_vertical"
        android:text="Jaimin Patel"
        android:textColor="@color/white"
        android:fontFamily="@font/poppins"
        android:textSize="22dp"

        android:textStyle="bold" />

        <TextView
            android:id="@+id/useremail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:fontFamily="@font/poppins"
            android:gravity="center_vertical"
            android:text="<EMAIL>"
            android:textColor="@color/white"
            android:textSize="12dp"
            android:textStyle="bold" />
    </LinearLayout>


</LinearLayout>
