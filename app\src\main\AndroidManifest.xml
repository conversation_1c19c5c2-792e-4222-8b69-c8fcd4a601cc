<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.bigwheel.winners">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET"/>

    <application
        android:allowBackup="true"
        android:hardwareAccelerated="true"
        android:icon="@drawable/logo"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@drawable/logo"
        android:supportsRtl="true"
        android:theme="@style/Theme.BigWheel"
        android:usesCleartextTraffic="true">
        <activity
            android:name=".splash.screensix"
            android:exported="false" />
        <activity
            android:name=".splash.screenfive"
            android:exported="false" />
        <activity
            android:name=".splash.screenfour"
            android:exported="false" />
        <activity
            android:name=".splash.screenthree"
            android:exported="false" />
        <activity
            android:name=".splash.screentwo"
            android:exported="false" />
        <activity
            android:name=".splash.screenOne"
            android:exported="false" />
        <activity
            android:name=".splash.splash"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name=".pay.PaymentTutorial" />
        <activity android:name=".pay.PaymentViaUPI" />
        <activity android:name=".pay.PaymentResultPayuMoney" />
        <activity android:name=".pay.PaymentMethods" />
        <activity
            android:name=".GoogleLogin"
            android:screenOrientation="portrait" />
        <activity
            android:name=".MainActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".pay.PaymentResult"
            android:screenOrientation="portrait" />
        <activity
            android:name=".pay.MainActivitys"
            android:screenOrientation="portrait" />
        <activity
            android:name=".SplashScreen"
            android:screenOrientation="portrait" />
    </application>

</manifest>