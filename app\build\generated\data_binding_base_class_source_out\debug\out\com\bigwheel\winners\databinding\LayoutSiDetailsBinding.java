// Generated by view binder compiler. Do not edit!
package com.bigwheel.winners.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatSpinner;
import androidx.appcompat.widget.SwitchCompat;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bigwheel.winners.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutSiDetailsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final EditText etApiVersion;

  @NonNull
  public final EditText etApiVersionValue;

  @NonNull
  public final EditText etBillingAmount;

  @NonNull
  public final EditText etBillingAmountValue;

  @NonNull
  public final EditText etBillingCurrency;

  @NonNull
  public final EditText etBillingCurrencyValue;

  @NonNull
  public final EditText etBillingCycle;

  @NonNull
  public final AppCompatSpinner etBillingCycleValue;

  @NonNull
  public final EditText etBillingInterval;

  @NonNull
  public final EditText etBillingIntervalValue;

  @NonNull
  public final EditText etPaymentEndDate;

  @NonNull
  public final EditText etPaymentEndDateValue;

  @NonNull
  public final EditText etPaymentStartDate;

  @NonNull
  public final EditText etPaymentStartDateValue;

  @NonNull
  public final EditText etRemarks;

  @NonNull
  public final EditText etRemarksValue;

  @NonNull
  public final EditText etSi;

  @NonNull
  public final EditText etSiValue;

  @NonNull
  public final SwitchCompat spFreeTrial;

  @NonNull
  public final EditText spFreeTrialText;

  private LayoutSiDetailsBinding(@NonNull LinearLayout rootView, @NonNull EditText etApiVersion,
      @NonNull EditText etApiVersionValue, @NonNull EditText etBillingAmount,
      @NonNull EditText etBillingAmountValue, @NonNull EditText etBillingCurrency,
      @NonNull EditText etBillingCurrencyValue, @NonNull EditText etBillingCycle,
      @NonNull AppCompatSpinner etBillingCycleValue, @NonNull EditText etBillingInterval,
      @NonNull EditText etBillingIntervalValue, @NonNull EditText etPaymentEndDate,
      @NonNull EditText etPaymentEndDateValue, @NonNull EditText etPaymentStartDate,
      @NonNull EditText etPaymentStartDateValue, @NonNull EditText etRemarks,
      @NonNull EditText etRemarksValue, @NonNull EditText etSi, @NonNull EditText etSiValue,
      @NonNull SwitchCompat spFreeTrial, @NonNull EditText spFreeTrialText) {
    this.rootView = rootView;
    this.etApiVersion = etApiVersion;
    this.etApiVersionValue = etApiVersionValue;
    this.etBillingAmount = etBillingAmount;
    this.etBillingAmountValue = etBillingAmountValue;
    this.etBillingCurrency = etBillingCurrency;
    this.etBillingCurrencyValue = etBillingCurrencyValue;
    this.etBillingCycle = etBillingCycle;
    this.etBillingCycleValue = etBillingCycleValue;
    this.etBillingInterval = etBillingInterval;
    this.etBillingIntervalValue = etBillingIntervalValue;
    this.etPaymentEndDate = etPaymentEndDate;
    this.etPaymentEndDateValue = etPaymentEndDateValue;
    this.etPaymentStartDate = etPaymentStartDate;
    this.etPaymentStartDateValue = etPaymentStartDateValue;
    this.etRemarks = etRemarks;
    this.etRemarksValue = etRemarksValue;
    this.etSi = etSi;
    this.etSiValue = etSiValue;
    this.spFreeTrial = spFreeTrial;
    this.spFreeTrialText = spFreeTrialText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutSiDetailsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutSiDetailsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_si_details, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutSiDetailsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.et_api_version;
      EditText etApiVersion = ViewBindings.findChildViewById(rootView, id);
      if (etApiVersion == null) {
        break missingId;
      }

      id = R.id.et_api_version_value;
      EditText etApiVersionValue = ViewBindings.findChildViewById(rootView, id);
      if (etApiVersionValue == null) {
        break missingId;
      }

      id = R.id.et_billingAmount;
      EditText etBillingAmount = ViewBindings.findChildViewById(rootView, id);
      if (etBillingAmount == null) {
        break missingId;
      }

      id = R.id.et_billingAmount_value;
      EditText etBillingAmountValue = ViewBindings.findChildViewById(rootView, id);
      if (etBillingAmountValue == null) {
        break missingId;
      }

      id = R.id.et_billingCurrency;
      EditText etBillingCurrency = ViewBindings.findChildViewById(rootView, id);
      if (etBillingCurrency == null) {
        break missingId;
      }

      id = R.id.et_billingCurrency_value;
      EditText etBillingCurrencyValue = ViewBindings.findChildViewById(rootView, id);
      if (etBillingCurrencyValue == null) {
        break missingId;
      }

      id = R.id.et_billingCycle;
      EditText etBillingCycle = ViewBindings.findChildViewById(rootView, id);
      if (etBillingCycle == null) {
        break missingId;
      }

      id = R.id.et_billingCycle_value;
      AppCompatSpinner etBillingCycleValue = ViewBindings.findChildViewById(rootView, id);
      if (etBillingCycleValue == null) {
        break missingId;
      }

      id = R.id.et_billingInterval;
      EditText etBillingInterval = ViewBindings.findChildViewById(rootView, id);
      if (etBillingInterval == null) {
        break missingId;
      }

      id = R.id.et_billingInterval_value;
      EditText etBillingIntervalValue = ViewBindings.findChildViewById(rootView, id);
      if (etBillingIntervalValue == null) {
        break missingId;
      }

      id = R.id.et_paymentEndDate;
      EditText etPaymentEndDate = ViewBindings.findChildViewById(rootView, id);
      if (etPaymentEndDate == null) {
        break missingId;
      }

      id = R.id.et_paymentEndDate_value;
      EditText etPaymentEndDateValue = ViewBindings.findChildViewById(rootView, id);
      if (etPaymentEndDateValue == null) {
        break missingId;
      }

      id = R.id.et_paymentStartDate;
      EditText etPaymentStartDate = ViewBindings.findChildViewById(rootView, id);
      if (etPaymentStartDate == null) {
        break missingId;
      }

      id = R.id.et_paymentStartDate_value;
      EditText etPaymentStartDateValue = ViewBindings.findChildViewById(rootView, id);
      if (etPaymentStartDateValue == null) {
        break missingId;
      }

      id = R.id.et_remarks;
      EditText etRemarks = ViewBindings.findChildViewById(rootView, id);
      if (etRemarks == null) {
        break missingId;
      }

      id = R.id.et_remarks_value;
      EditText etRemarksValue = ViewBindings.findChildViewById(rootView, id);
      if (etRemarksValue == null) {
        break missingId;
      }

      id = R.id.et_si;
      EditText etSi = ViewBindings.findChildViewById(rootView, id);
      if (etSi == null) {
        break missingId;
      }

      id = R.id.et_si_value;
      EditText etSiValue = ViewBindings.findChildViewById(rootView, id);
      if (etSiValue == null) {
        break missingId;
      }

      id = R.id.sp_free_trial;
      SwitchCompat spFreeTrial = ViewBindings.findChildViewById(rootView, id);
      if (spFreeTrial == null) {
        break missingId;
      }

      id = R.id.sp_free_trial_text;
      EditText spFreeTrialText = ViewBindings.findChildViewById(rootView, id);
      if (spFreeTrialText == null) {
        break missingId;
      }

      return new LayoutSiDetailsBinding((LinearLayout) rootView, etApiVersion, etApiVersionValue,
          etBillingAmount, etBillingAmountValue, etBillingCurrency, etBillingCurrencyValue,
          etBillingCycle, etBillingCycleValue, etBillingInterval, etBillingIntervalValue,
          etPaymentEndDate, etPaymentEndDateValue, etPaymentStartDate, etPaymentStartDateValue,
          etRemarks, etRemarksValue, etSi, etSiValue, spFreeTrial, spFreeTrialText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
