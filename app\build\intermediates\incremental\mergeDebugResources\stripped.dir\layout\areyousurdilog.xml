<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    app:cardCornerRadius="20dp"
    android:padding="20dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:backgroundTint="@color/themeBgColor"
        android:padding="20dp"
        android:background="@drawable/strok"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="Are You Sure want to Quit The Game?"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />
            <TextView
                android:id="@+id/winamount"
                android:layout_width="wrap_content"
                android:layout_marginStart="5dp"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:layout_height="wrap_content"
                android:text=""></TextView>



        </LinearLayout>


        <TextView
            android:id="@+id/tvDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginHorizontal="10dp"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:text="Have A Nice Day"
            android:textColor="@color/white"
            android:textSize="12sp" />

        <LinearLayout
            android:id="@+id/btnLater"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_gravity="center"
                android:layout_marginLeft="5dp"
                android:layout_marginHorizontal="5dp"
                android:layout_weight="1"
                android:background="@drawable/ic_xml_button_">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginRight="5dp"
                    android:gravity="center"
                    android:text="Cancel"
                    android:textColor="@color/white" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/btnRateNow"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_gravity="center"
                android:layout_marginLeft="5dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="5dp"
                android:background="@drawable/ic_xml_button_">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:text="Yes"
                    android:textColor="@color/white" />
            </LinearLayout>


        </LinearLayout>

    </LinearLayout>


</androidx.cardview.widget.CardView>