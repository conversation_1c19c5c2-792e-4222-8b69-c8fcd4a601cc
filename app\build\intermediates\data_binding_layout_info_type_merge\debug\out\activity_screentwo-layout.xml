<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_screentwo" modulePackage="com.bigwheel.winners" filePath="app\src\main\res\layout\activity_screentwo.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_screentwo_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="0" startOffset="0" endLine="292" endOffset="51"/></Target><Target id="@+id/checkbox_terms" view="CheckBox"><Expressions/><location startLine="17" startOffset="4" endLine="27" endOffset="69"/></Target><Target id="@+id/textView12" view="TextView"><Expressions/><location startLine="29" startOffset="4" endLine="40" endOffset="66"/></Target><Target id="@+id/tv_privacy" view="TextView"><Expressions/><location startLine="42" startOffset="4" endLine="52" endOffset="62"/></Target><Target id="@+id/iv_googlelogin" view="LinearLayout"><Expressions/><location startLine="75" startOffset="4" endLine="103" endOffset="18"/></Target><Target id="@+id/textView3" view="TextView"><Expressions/><location startLine="113" startOffset="4" endLine="126" endOffset="53"/></Target><Target id="@+id/constraintLayout2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="128" startOffset="4" endLine="251" endOffset="55"/></Target><Target id="@+id/textView4" view="TextView"><Expressions/><location startLine="143" startOffset="8" endLine="154" endOffset="57"/></Target><Target id="@+id/etNumber" view="EditText"><Expressions/><location startLine="156" startOffset="8" endLine="170" endOffset="68"/></Target><Target id="@+id/textView5" view="TextView"><Expressions/><location startLine="172" startOffset="8" endLine="183" endOffset="67"/></Target><Target id="@+id/etPassword" view="EditText"><Expressions/><location startLine="185" startOffset="8" endLine="199" endOffset="68"/></Target><Target id="@+id/tvForgotPassword" view="TextView"><Expressions/><location startLine="201" startOffset="8" endLine="213" endOffset="69"/></Target><Target id="@+id/btnSignIn" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="215" startOffset="8" endLine="240" endOffset="59"/></Target><Target id="@+id/textView2" view="TextView"><Expressions/><location startLine="227" startOffset="12" endLine="239" endOffset="61"/></Target><Target id="@+id/tv_or" view="TextView"><Expressions/><location startLine="242" startOffset="8" endLine="250" endOffset="68"/></Target><Target id="@+id/hr" view="View"><Expressions/><location startLine="253" startOffset="4" endLine="263" endOffset="57"/></Target><Target id="@+id/textView6" view="TextView"><Expressions/><location startLine="265" startOffset="4" endLine="277" endOffset="57"/></Target><Target id="@+id/btnSignUp" view="TextView"><Expressions/><location startLine="279" startOffset="4" endLine="291" endOffset="64"/></Target></Targets></Layout>