// Generated by view binder compiler. Do not edit!
package com.bigwheel.winners.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Switch;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bigwheel.winners.R;
import com.google.android.material.navigation.NavigationView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import rubikstudio.library.LuckyWheelView;

public final class ActivityScreenOneBinding implements ViewBinding {
  @NonNull
  private final DrawerLayout rootView;

  @NonNull
  public final ConstraintLayout addMoneyLayout;

  @NonNull
  public final LinearLayout btnAddMoneys;

  @NonNull
  public final ImageView btnCloseaddmoney;

  @NonNull
  public final ImageView btnClosewithdraw;

  @NonNull
  public final LinearLayout btnDeposite;

  @NonNull
  public final LinearLayout btnSpin;

  @NonNull
  public final LinearLayout btnWithdrawFinal;

  @NonNull
  public final LinearLayout btnWithdraws;

  @NonNull
  public final ImageView cursorView;

  @NonNull
  public final LinearLayout deposite100;

  @NonNull
  public final LinearLayout deposite150;

  @NonNull
  public final LinearLayout deposite200;

  @NonNull
  public final LinearLayout deposite50;

  @NonNull
  public final DrawerLayout drwa;

  @NonNull
  public final EditText etDeposite;

  @NonNull
  public final EditText etWithdraw;

  @NonNull
  public final EditText etWithdrawAmmount;

  @NonNull
  public final LinearLayout linearLayout2;

  @NonNull
  public final LinearLayout linearLayout3;

  @NonNull
  public final LinearLayout linearLayout4;

  @NonNull
  public final LinearLayout linearLayout5;

  @NonNull
  public final LinearLayout linearLayout6;

  @NonNull
  public final LinearLayout linearLayout9;

  @NonNull
  public final LuckyWheelView luckyWheel;

  @NonNull
  public final NavigationView navView;

  @NonNull
  public final TextView price1;

  @NonNull
  public final TextView price2;

  @NonNull
  public final TextView price3;

  @NonNull
  public final TextView price4;

  @NonNull
  public final Switch swith;

  @NonNull
  public final TextView textView11;

  @NonNull
  public final TextView textView7;

  @NonNull
  public final TextView textView8;

  @NonNull
  public final TextView textView9;

  @NonNull
  public final TextView tvDepositeAmmount;

  @NonNull
  public final TextView tvWiningeAmmount;

  @NonNull
  public final ConstraintLayout wheelLayout;

  @NonNull
  public final ConstraintLayout withdrawMoneyLayout;

  private ActivityScreenOneBinding(@NonNull DrawerLayout rootView,
      @NonNull ConstraintLayout addMoneyLayout, @NonNull LinearLayout btnAddMoneys,
      @NonNull ImageView btnCloseaddmoney, @NonNull ImageView btnClosewithdraw,
      @NonNull LinearLayout btnDeposite, @NonNull LinearLayout btnSpin,
      @NonNull LinearLayout btnWithdrawFinal, @NonNull LinearLayout btnWithdraws,
      @NonNull ImageView cursorView, @NonNull LinearLayout deposite100,
      @NonNull LinearLayout deposite150, @NonNull LinearLayout deposite200,
      @NonNull LinearLayout deposite50, @NonNull DrawerLayout drwa, @NonNull EditText etDeposite,
      @NonNull EditText etWithdraw, @NonNull EditText etWithdrawAmmount,
      @NonNull LinearLayout linearLayout2, @NonNull LinearLayout linearLayout3,
      @NonNull LinearLayout linearLayout4, @NonNull LinearLayout linearLayout5,
      @NonNull LinearLayout linearLayout6, @NonNull LinearLayout linearLayout9,
      @NonNull LuckyWheelView luckyWheel, @NonNull NavigationView navView, @NonNull TextView price1,
      @NonNull TextView price2, @NonNull TextView price3, @NonNull TextView price4,
      @NonNull Switch swith, @NonNull TextView textView11, @NonNull TextView textView7,
      @NonNull TextView textView8, @NonNull TextView textView9, @NonNull TextView tvDepositeAmmount,
      @NonNull TextView tvWiningeAmmount, @NonNull ConstraintLayout wheelLayout,
      @NonNull ConstraintLayout withdrawMoneyLayout) {
    this.rootView = rootView;
    this.addMoneyLayout = addMoneyLayout;
    this.btnAddMoneys = btnAddMoneys;
    this.btnCloseaddmoney = btnCloseaddmoney;
    this.btnClosewithdraw = btnClosewithdraw;
    this.btnDeposite = btnDeposite;
    this.btnSpin = btnSpin;
    this.btnWithdrawFinal = btnWithdrawFinal;
    this.btnWithdraws = btnWithdraws;
    this.cursorView = cursorView;
    this.deposite100 = deposite100;
    this.deposite150 = deposite150;
    this.deposite200 = deposite200;
    this.deposite50 = deposite50;
    this.drwa = drwa;
    this.etDeposite = etDeposite;
    this.etWithdraw = etWithdraw;
    this.etWithdrawAmmount = etWithdrawAmmount;
    this.linearLayout2 = linearLayout2;
    this.linearLayout3 = linearLayout3;
    this.linearLayout4 = linearLayout4;
    this.linearLayout5 = linearLayout5;
    this.linearLayout6 = linearLayout6;
    this.linearLayout9 = linearLayout9;
    this.luckyWheel = luckyWheel;
    this.navView = navView;
    this.price1 = price1;
    this.price2 = price2;
    this.price3 = price3;
    this.price4 = price4;
    this.swith = swith;
    this.textView11 = textView11;
    this.textView7 = textView7;
    this.textView8 = textView8;
    this.textView9 = textView9;
    this.tvDepositeAmmount = tvDepositeAmmount;
    this.tvWiningeAmmount = tvWiningeAmmount;
    this.wheelLayout = wheelLayout;
    this.withdrawMoneyLayout = withdrawMoneyLayout;
  }

  @Override
  @NonNull
  public DrawerLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityScreenOneBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityScreenOneBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_screen_one, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityScreenOneBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addMoneyLayout;
      ConstraintLayout addMoneyLayout = ViewBindings.findChildViewById(rootView, id);
      if (addMoneyLayout == null) {
        break missingId;
      }

      id = R.id.btnAddMoneys;
      LinearLayout btnAddMoneys = ViewBindings.findChildViewById(rootView, id);
      if (btnAddMoneys == null) {
        break missingId;
      }

      id = R.id.btn_closeaddmoney;
      ImageView btnCloseaddmoney = ViewBindings.findChildViewById(rootView, id);
      if (btnCloseaddmoney == null) {
        break missingId;
      }

      id = R.id.btn_closewithdraw;
      ImageView btnClosewithdraw = ViewBindings.findChildViewById(rootView, id);
      if (btnClosewithdraw == null) {
        break missingId;
      }

      id = R.id.btnDeposite;
      LinearLayout btnDeposite = ViewBindings.findChildViewById(rootView, id);
      if (btnDeposite == null) {
        break missingId;
      }

      id = R.id.btnSpin;
      LinearLayout btnSpin = ViewBindings.findChildViewById(rootView, id);
      if (btnSpin == null) {
        break missingId;
      }

      id = R.id.btnWithdrawFinal;
      LinearLayout btnWithdrawFinal = ViewBindings.findChildViewById(rootView, id);
      if (btnWithdrawFinal == null) {
        break missingId;
      }

      id = R.id.btnWithdraws;
      LinearLayout btnWithdraws = ViewBindings.findChildViewById(rootView, id);
      if (btnWithdraws == null) {
        break missingId;
      }

      id = R.id.cursorView;
      ImageView cursorView = ViewBindings.findChildViewById(rootView, id);
      if (cursorView == null) {
        break missingId;
      }

      id = R.id.deposite_100;
      LinearLayout deposite100 = ViewBindings.findChildViewById(rootView, id);
      if (deposite100 == null) {
        break missingId;
      }

      id = R.id.deposite_150;
      LinearLayout deposite150 = ViewBindings.findChildViewById(rootView, id);
      if (deposite150 == null) {
        break missingId;
      }

      id = R.id.deposite_200;
      LinearLayout deposite200 = ViewBindings.findChildViewById(rootView, id);
      if (deposite200 == null) {
        break missingId;
      }

      id = R.id.deposite_50;
      LinearLayout deposite50 = ViewBindings.findChildViewById(rootView, id);
      if (deposite50 == null) {
        break missingId;
      }

      DrawerLayout drwa = (DrawerLayout) rootView;

      id = R.id.etDeposite;
      EditText etDeposite = ViewBindings.findChildViewById(rootView, id);
      if (etDeposite == null) {
        break missingId;
      }

      id = R.id.etWithdraw;
      EditText etWithdraw = ViewBindings.findChildViewById(rootView, id);
      if (etWithdraw == null) {
        break missingId;
      }

      id = R.id.etWithdrawAmmount;
      EditText etWithdrawAmmount = ViewBindings.findChildViewById(rootView, id);
      if (etWithdrawAmmount == null) {
        break missingId;
      }

      id = R.id.linearLayout2;
      LinearLayout linearLayout2 = ViewBindings.findChildViewById(rootView, id);
      if (linearLayout2 == null) {
        break missingId;
      }

      id = R.id.linearLayout3;
      LinearLayout linearLayout3 = ViewBindings.findChildViewById(rootView, id);
      if (linearLayout3 == null) {
        break missingId;
      }

      id = R.id.linearLayout4;
      LinearLayout linearLayout4 = ViewBindings.findChildViewById(rootView, id);
      if (linearLayout4 == null) {
        break missingId;
      }

      id = R.id.linearLayout5;
      LinearLayout linearLayout5 = ViewBindings.findChildViewById(rootView, id);
      if (linearLayout5 == null) {
        break missingId;
      }

      id = R.id.linearLayout6;
      LinearLayout linearLayout6 = ViewBindings.findChildViewById(rootView, id);
      if (linearLayout6 == null) {
        break missingId;
      }

      id = R.id.linearLayout9;
      LinearLayout linearLayout9 = ViewBindings.findChildViewById(rootView, id);
      if (linearLayout9 == null) {
        break missingId;
      }

      id = R.id.luckyWheel;
      LuckyWheelView luckyWheel = ViewBindings.findChildViewById(rootView, id);
      if (luckyWheel == null) {
        break missingId;
      }

      id = R.id.nav_view;
      NavigationView navView = ViewBindings.findChildViewById(rootView, id);
      if (navView == null) {
        break missingId;
      }

      id = R.id.price1;
      TextView price1 = ViewBindings.findChildViewById(rootView, id);
      if (price1 == null) {
        break missingId;
      }

      id = R.id.price2;
      TextView price2 = ViewBindings.findChildViewById(rootView, id);
      if (price2 == null) {
        break missingId;
      }

      id = R.id.price3;
      TextView price3 = ViewBindings.findChildViewById(rootView, id);
      if (price3 == null) {
        break missingId;
      }

      id = R.id.price4;
      TextView price4 = ViewBindings.findChildViewById(rootView, id);
      if (price4 == null) {
        break missingId;
      }

      id = R.id.swith;
      Switch swith = ViewBindings.findChildViewById(rootView, id);
      if (swith == null) {
        break missingId;
      }

      id = R.id.textView11;
      TextView textView11 = ViewBindings.findChildViewById(rootView, id);
      if (textView11 == null) {
        break missingId;
      }

      id = R.id.textView7;
      TextView textView7 = ViewBindings.findChildViewById(rootView, id);
      if (textView7 == null) {
        break missingId;
      }

      id = R.id.textView8;
      TextView textView8 = ViewBindings.findChildViewById(rootView, id);
      if (textView8 == null) {
        break missingId;
      }

      id = R.id.textView9;
      TextView textView9 = ViewBindings.findChildViewById(rootView, id);
      if (textView9 == null) {
        break missingId;
      }

      id = R.id.tvDepositeAmmount;
      TextView tvDepositeAmmount = ViewBindings.findChildViewById(rootView, id);
      if (tvDepositeAmmount == null) {
        break missingId;
      }

      id = R.id.tvWiningeAmmount;
      TextView tvWiningeAmmount = ViewBindings.findChildViewById(rootView, id);
      if (tvWiningeAmmount == null) {
        break missingId;
      }

      id = R.id.wheelLayout;
      ConstraintLayout wheelLayout = ViewBindings.findChildViewById(rootView, id);
      if (wheelLayout == null) {
        break missingId;
      }

      id = R.id.withdrawMoneyLayout;
      ConstraintLayout withdrawMoneyLayout = ViewBindings.findChildViewById(rootView, id);
      if (withdrawMoneyLayout == null) {
        break missingId;
      }

      return new ActivityScreenOneBinding((DrawerLayout) rootView, addMoneyLayout, btnAddMoneys,
          btnCloseaddmoney, btnClosewithdraw, btnDeposite, btnSpin, btnWithdrawFinal, btnWithdraws,
          cursorView, deposite100, deposite150, deposite200, deposite50, drwa, etDeposite,
          etWithdraw, etWithdrawAmmount, linearLayout2, linearLayout3, linearLayout4, linearLayout5,
          linearLayout6, linearLayout9, luckyWheel, navView, price1, price2, price3, price4, swith,
          textView11, textView7, textView8, textView9, tvDepositeAmmount, tvWiningeAmmount,
          wheelLayout, withdrawMoneyLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
