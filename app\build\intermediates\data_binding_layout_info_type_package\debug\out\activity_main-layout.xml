<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.bigwheel.winners" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.drawerlayout.widget.DrawerLayout" rootNodeViewId="@+id/drwa"><Targets><Target id="@+id/drwa" tag="layout/activity_main_0" view="androidx.drawerlayout.widget.DrawerLayout"><Expressions/><location startLine="1" startOffset="0" endLine="954" endOffset="43"/></Target><Target id="@+id/linearLayout5" view="LinearLayout"><Expressions/><location startLine="16" startOffset="8" endLine="255" endOffset="22"/></Target><Target id="@+id/menu" view="ImageView"><Expressions/><location startLine="35" startOffset="16" endLine="42" endOffset="47"/></Target><Target id="@+id/textView7" view="TextView"><Expressions/><location startLine="50" startOffset="20" endLine="62" endOffset="67"/></Target><Target id="@+id/textView8" view="TextView"><Expressions/><location startLine="64" startOffset="20" endLine="76" endOffset="78"/></Target><Target id="@+id/linearLayout2" view="LinearLayout"><Expressions/><location startLine="81" startOffset="12" endLine="182" endOffset="26"/></Target><Target id="@+id/tvDepositeAmmount" view="TextView"><Expressions/><location startLine="105" startOffset="24" endLine="117" endOffset="71"/></Target><Target id="@+id/tvWiningeAmmount" view="TextView"><Expressions/><location startLine="154" startOffset="24" endLine="166" endOffset="71"/></Target><Target id="@+id/btnAddMoney" view="LinearLayout"><Expressions/><location startLine="192" startOffset="16" endLine="211" endOffset="30"/></Target><Target id="@+id/btnWithdraw" view="LinearLayout"><Expressions/><location startLine="213" startOffset="16" endLine="235" endOffset="30"/></Target><Target id="@+id/swith" view="Switch"><Expressions/><location startLine="245" startOffset="16" endLine="253" endOffset="53"/></Target><Target id="@+id/scroller" view="TextView"><Expressions/><location startLine="258" startOffset="8" endLine="275" endOffset="70"/></Target><Target id="@+id/linearLayout4" view="LinearLayout"><Expressions/><location startLine="277" startOffset="8" endLine="887" endOffset="22"/></Target><Target id="@+id/wheelLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="285" startOffset="12" endLine="339" endOffset="63"/></Target><Target id="@+id/cursorView" view="ImageView"><Expressions/><location startLine="307" startOffset="20" endLine="313" endOffset="57"/></Target><Target id="@+id/luckyWheel" view="rubikstudio.library.LuckyWheelView"><Expressions/><location startLine="324" startOffset="16" endLine="336" endOffset="53"/></Target><Target id="@+id/addMoneyLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="343" startOffset="12" endLine="779" endOffset="63"/></Target><Target id="@+id/btn_closeaddmoney" view="ImageView"><Expressions/><location startLine="359" startOffset="16" endLine="370" endOffset="63"/></Target><Target id="@+id/tvPremiumTitle" view="TextView"><Expressions/><location startLine="373" startOffset="16" endLine="385" endOffset="63"/></Target><Target id="@+id/tvPremiumSubtitle" view="TextView"><Expressions/><location startLine="387" startOffset="16" endLine="398" endOffset="79"/></Target><Target id="@+id/cvSelectedAmount" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="401" startOffset="16" endLine="442" endOffset="51"/></Target><Target id="@+id/etDeposite" view="EditText"><Expressions/><location startLine="428" startOffset="24" endLine="439" endOffset="54"/></Target><Target id="@+id/llPricingPlans" view="LinearLayout"><Expressions/><location startLine="445" startOffset="16" endLine="738" endOffset="30"/></Target><Target id="@+id/deposite_50" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="461" startOffset="24" endLine="509" endOffset="59"/></Target><Target id="@+id/price1" view="TextView"><Expressions/><location startLine="488" startOffset="32" endLine="497" endOffset="62"/></Target><Target id="@+id/deposite_100" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="512" startOffset="24" endLine="582" endOffset="59"/></Target><Target id="@+id/price2" view="TextView"><Expressions/><location startLine="559" startOffset="36" endLine="567" endOffset="66"/></Target><Target id="@+id/deposite_150" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="593" startOffset="24" endLine="662" endOffset="59"/></Target><Target id="@+id/price3" view="TextView"><Expressions/><location startLine="639" startOffset="36" endLine="647" endOffset="66"/></Target><Target id="@+id/deposite_200" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="665" startOffset="24" endLine="734" endOffset="59"/></Target><Target id="@+id/price4" view="TextView"><Expressions/><location startLine="711" startOffset="36" endLine="719" endOffset="66"/></Target><Target id="@+id/btnDeposite" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="741" startOffset="16" endLine="777" endOffset="51"/></Target><Target id="@+id/withdrawMoneyLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="783" startOffset="12" endLine="886" endOffset="63"/></Target><Target id="@+id/textView11" view="TextView"><Expressions/><location startLine="796" startOffset="16" endLine="806" endOffset="63"/></Target><Target id="@+id/btn_closewithdraw" view="ImageView"><Expressions/><location startLine="808" startOffset="16" endLine="816" endOffset="63"/></Target><Target id="@+id/etWithdraw" view="EditText"><Expressions/><location startLine="827" startOffset="16" endLine="842" endOffset="75"/></Target><Target id="@+id/etWithdrawAmmount" view="EditText"><Expressions/><location startLine="844" startOffset="16" endLine="858" endOffset="75"/></Target><Target id="@+id/btnWithdrawFinal" view="LinearLayout"><Expressions/><location startLine="860" startOffset="16" endLine="883" endOffset="30"/></Target><Target id="@+id/linearLayout6" view="LinearLayout"><Expressions/><location startLine="891" startOffset="8" endLine="927" endOffset="22"/></Target><Target id="@+id/btnSpin" view="LinearLayout"><Expressions/><location startLine="902" startOffset="12" endLine="924" endOffset="26"/></Target><Target id="@+id/nav_view" view="com.google.android.material.navigation.NavigationView"><Expressions/><location startLine="940" startOffset="4" endLine="951" endOffset="58"/></Target></Targets></Layout>