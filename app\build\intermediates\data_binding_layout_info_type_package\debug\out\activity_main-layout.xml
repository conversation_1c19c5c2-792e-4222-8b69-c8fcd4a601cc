<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.bigwheel.winners" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.drawerlayout.widget.DrawerLayout" rootNodeViewId="@+id/drawer_layout"><Targets><Target id="@+id/drawer_layout" tag="layout/activity_main_0" view="androidx.drawerlayout.widget.DrawerLayout"><Expressions/><location startLine="1" startOffset="0" endLine="447" endOffset="43"/></Target><Target id="@+id/constraintLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="16" startOffset="8" endLine="83" endOffset="59"/></Target><Target id="@+id/btn_menu" view="ImageView"><Expressions/><location startLine="26" startOffset="12" endLine="32" endOffset="59"/></Target><Target id="@+id/textView" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="45" endOffset="59"/></Target><Target id="@+id/tvDepositeAmmount" view="TextView"><Expressions/><location startLine="54" startOffset="16" endLine="62" endOffset="52"/></Target><Target id="@+id/tvWiningeAmmount" view="TextView"><Expressions/><location startLine="72" startOffset="16" endLine="79" endOffset="45"/></Target><Target id="@+id/linearLayout4" view="LinearLayout"><Expressions/><location startLine="86" startOffset="8" endLine="386" endOffset="22"/></Target><Target id="@+id/addMoneyLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="97" startOffset="12" endLine="303" endOffset="63"/></Target><Target id="@+id/btn_closeaddmoney" view="ImageView"><Expressions/><location startLine="107" startOffset="16" endLine="116" endOffset="63"/></Target><Target id="@+id/textView9" view="TextView"><Expressions/><location startLine="119" startOffset="16" endLine="131" endOffset="63"/></Target><Target id="@+id/etDeposite" view="EditText"><Expressions/><location startLine="134" startOffset="16" endLine="149" endOffset="74"/></Target><Target id="@+id/llPricingPlans" view="LinearLayout"><Expressions/><location startLine="152" startOffset="16" endLine="277" endOffset="30"/></Target><Target id="@+id/plan50" view="LinearLayout"><Expressions/><location startLine="162" startOffset="20" endLine="188" endOffset="34"/></Target><Target id="@+id/plan100" view="LinearLayout"><Expressions/><location startLine="191" startOffset="20" endLine="217" endOffset="34"/></Target><Target id="@+id/plan150" view="LinearLayout"><Expressions/><location startLine="220" startOffset="20" endLine="246" endOffset="34"/></Target><Target id="@+id/plan200" view="LinearLayout"><Expressions/><location startLine="249" startOffset="20" endLine="275" endOffset="34"/></Target><Target id="@+id/btnDeposite" view="LinearLayout"><Expressions/><location startLine="280" startOffset="16" endLine="301" endOffset="30"/></Target><Target id="@+id/withdrawMoneyLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="306" startOffset="12" endLine="384" endOffset="63"/></Target><Target id="@+id/btn_closewithdraw" view="ImageView"><Expressions/><location startLine="315" startOffset="16" endLine="323" endOffset="63"/></Target><Target id="@+id/textView11" view="TextView"><Expressions/><location startLine="325" startOffset="16" endLine="335" endOffset="63"/></Target><Target id="@+id/etWithdraw" view="EditText"><Expressions/><location startLine="337" startOffset="16" endLine="348" endOffset="75"/></Target><Target id="@+id/etWithdrawAmmount" view="EditText"><Expressions/><location startLine="350" startOffset="16" endLine="361" endOffset="75"/></Target><Target id="@+id/btnWithdrawFinal" view="LinearLayout"><Expressions/><location startLine="363" startOffset="16" endLine="382" endOffset="30"/></Target><Target id="@+id/wheelLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="389" startOffset="8" endLine="429" endOffset="59"/></Target><Target id="@+id/luckyWheel" view="ImageView"><Expressions/><location startLine="397" startOffset="12" endLine="405" endOffset="59"/></Target><Target id="@+id/btnSpin" view="LinearLayout"><Expressions/><location startLine="408" startOffset="12" endLine="427" endOffset="26"/></Target><Target id="@+id/nav_view" view="com.google.android.material.navigation.NavigationView"><Expressions/><location startLine="434" startOffset="4" endLine="445" endOffset="58"/></Target></Targets></Layout>