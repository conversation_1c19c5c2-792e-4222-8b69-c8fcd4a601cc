<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.bigwheel.winners" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.drawerlayout.widget.DrawerLayout" rootNodeViewId="@+id/drawer_layout"><Targets><Target id="@+id/drawer_layout" tag="layout/activity_main_0" view="androidx.drawerlayout.widget.DrawerLayout"><Expressions/><location startLine="1" startOffset="0" endLine="669" endOffset="43"/></Target><Target id="@+id/constraintLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="16" startOffset="8" endLine="83" endOffset="59"/></Target><Target id="@+id/btn_menu" view="ImageView"><Expressions/><location startLine="26" startOffset="12" endLine="32" endOffset="59"/></Target><Target id="@+id/textView" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="45" endOffset="59"/></Target><Target id="@+id/tvDepositeAmmount" view="TextView"><Expressions/><location startLine="54" startOffset="16" endLine="62" endOffset="52"/></Target><Target id="@+id/tvWiningeAmmount" view="TextView"><Expressions/><location startLine="72" startOffset="16" endLine="79" endOffset="45"/></Target><Target id="@+id/linearLayout4" view="LinearLayout"><Expressions/><location startLine="86" startOffset="8" endLine="574" endOffset="22"/></Target><Target id="@+id/addMoneyLayout" view="ScrollView"><Expressions/><location startLine="99" startOffset="12" endLine="492" endOffset="24"/></Target><Target id="@+id/btn_closeaddmoney" view="ImageView"><Expressions/><location startLine="121" startOffset="20" endLine="132" endOffset="49"/></Target><Target id="@+id/textView9" view="TextView"><Expressions/><location startLine="135" startOffset="20" endLine="147" endOffset="67"/></Target><Target id="@+id/cvDiscountInfo" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="150" startOffset="20" endLine="208" endOffset="55"/></Target><Target id="@+id/etDeposite" view="EditText"><Expressions/><location startLine="211" startOffset="20" endLine="225" endOffset="83"/></Target><Target id="@+id/llPricingPlans" view="LinearLayout"><Expressions/><location startLine="228" startOffset="20" endLine="455" endOffset="34"/></Target><Target id="@+id/plan50" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="246" startOffset="28" endLine="292" endOffset="63"/></Target><Target id="@+id/price1" view="TextView"><Expressions/><location startLine="274" startOffset="36" endLine="281" endOffset="66"/></Target><Target id="@+id/plan100" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="295" startOffset="28" endLine="342" endOffset="63"/></Target><Target id="@+id/price2" view="TextView"><Expressions/><location startLine="323" startOffset="36" endLine="330" endOffset="66"/></Target><Target id="@+id/plan150" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="354" startOffset="28" endLine="401" endOffset="63"/></Target><Target id="@+id/price3" view="TextView"><Expressions/><location startLine="382" startOffset="36" endLine="389" endOffset="66"/></Target><Target id="@+id/plan200" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="404" startOffset="28" endLine="451" endOffset="63"/></Target><Target id="@+id/price4" view="TextView"><Expressions/><location startLine="432" startOffset="36" endLine="439" endOffset="66"/></Target><Target id="@+id/btnDeposite" view="LinearLayout"><Expressions/><location startLine="469" startOffset="24" endLine="488" endOffset="38"/></Target><Target id="@+id/withdrawMoneyLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="494" startOffset="12" endLine="572" endOffset="63"/></Target><Target id="@+id/btn_closewithdraw" view="ImageView"><Expressions/><location startLine="503" startOffset="16" endLine="511" endOffset="63"/></Target><Target id="@+id/textView11" view="TextView"><Expressions/><location startLine="513" startOffset="16" endLine="523" endOffset="63"/></Target><Target id="@+id/etWithdraw" view="EditText"><Expressions/><location startLine="525" startOffset="16" endLine="536" endOffset="75"/></Target><Target id="@+id/etWithdrawAmmount" view="EditText"><Expressions/><location startLine="538" startOffset="16" endLine="549" endOffset="75"/></Target><Target id="@+id/btnWithdrawFinal" view="LinearLayout"><Expressions/><location startLine="551" startOffset="16" endLine="570" endOffset="30"/></Target><Target id="@+id/wheelLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="577" startOffset="8" endLine="651" endOffset="59"/></Target><Target id="@+id/luckyWheel" view="ImageView"><Expressions/><location startLine="585" startOffset="12" endLine="593" endOffset="59"/></Target><Target id="@+id/btnAddMoney" view="LinearLayout"><Expressions/><location startLine="605" startOffset="16" endLine="625" endOffset="30"/></Target><Target id="@+id/btnSpin" view="LinearLayout"><Expressions/><location startLine="628" startOffset="16" endLine="647" endOffset="30"/></Target><Target id="@+id/nav_view" view="com.google.android.material.navigation.NavigationView"><Expressions/><location startLine="656" startOffset="4" endLine="667" endOffset="58"/></Target></Targets></Layout>