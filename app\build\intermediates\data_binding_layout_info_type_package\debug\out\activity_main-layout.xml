<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.bigwheel.winners" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.drawerlayout.widget.DrawerLayout" rootNodeViewId="@+id/drawer_layout"><Targets><Target id="@+id/drawer_layout" tag="layout/activity_main_0" view="androidx.drawerlayout.widget.DrawerLayout"><Expressions/><location startLine="1" startOffset="0" endLine="409" endOffset="43"/></Target><Target id="@+id/constraintLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="16" startOffset="8" endLine="58" endOffset="59"/></Target><Target id="@+id/btn_menu" view="ImageView"><Expressions/><location startLine="26" startOffset="12" endLine="32" endOffset="59"/></Target><Target id="@+id/textView" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="45" endOffset="59"/></Target><Target id="@+id/tvCoins" view="TextView"><Expressions/><location startLine="47" startOffset="12" endLine="56" endOffset="59"/></Target><Target id="@+id/linearLayout4" view="LinearLayout"><Expressions/><location startLine="61" startOffset="8" endLine="361" endOffset="22"/></Target><Target id="@+id/addMoneyLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="72" startOffset="12" endLine="278" endOffset="63"/></Target><Target id="@+id/btn_closeaddmoney" view="ImageView"><Expressions/><location startLine="82" startOffset="16" endLine="91" endOffset="63"/></Target><Target id="@+id/textView9" view="TextView"><Expressions/><location startLine="94" startOffset="16" endLine="106" endOffset="63"/></Target><Target id="@+id/etDeposite" view="EditText"><Expressions/><location startLine="109" startOffset="16" endLine="124" endOffset="74"/></Target><Target id="@+id/llPricingPlans" view="LinearLayout"><Expressions/><location startLine="127" startOffset="16" endLine="252" endOffset="30"/></Target><Target id="@+id/plan50" view="LinearLayout"><Expressions/><location startLine="137" startOffset="20" endLine="163" endOffset="34"/></Target><Target id="@+id/plan100" view="LinearLayout"><Expressions/><location startLine="166" startOffset="20" endLine="192" endOffset="34"/></Target><Target id="@+id/plan150" view="LinearLayout"><Expressions/><location startLine="195" startOffset="20" endLine="221" endOffset="34"/></Target><Target id="@+id/plan200" view="LinearLayout"><Expressions/><location startLine="224" startOffset="20" endLine="250" endOffset="34"/></Target><Target id="@+id/btnDeposite" view="LinearLayout"><Expressions/><location startLine="255" startOffset="16" endLine="276" endOffset="30"/></Target><Target id="@+id/withdrawMoneyLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="281" startOffset="12" endLine="359" endOffset="63"/></Target><Target id="@+id/btn_closewithdraw" view="ImageView"><Expressions/><location startLine="290" startOffset="16" endLine="298" endOffset="63"/></Target><Target id="@+id/textView11" view="TextView"><Expressions/><location startLine="300" startOffset="16" endLine="310" endOffset="63"/></Target><Target id="@+id/etWithdraw" view="EditText"><Expressions/><location startLine="312" startOffset="16" endLine="323" endOffset="75"/></Target><Target id="@+id/etWithdrawAmmount" view="EditText"><Expressions/><location startLine="325" startOffset="16" endLine="336" endOffset="75"/></Target><Target id="@+id/btnWithdrawFinal" view="LinearLayout"><Expressions/><location startLine="338" startOffset="16" endLine="357" endOffset="30"/></Target><Target id="@+id/linearLayout6" view="LinearLayout"><Expressions/><location startLine="364" startOffset="8" endLine="391" endOffset="22"/></Target><Target id="@+id/btnSpin" view="LinearLayout"><Expressions/><location startLine="372" startOffset="12" endLine="389" endOffset="26"/></Target><Target id="@+id/nav_view" view="com.google.android.material.navigation.NavigationView"><Expressions/><location startLine="396" startOffset="4" endLine="407" endOffset="58"/></Target></Targets></Layout>