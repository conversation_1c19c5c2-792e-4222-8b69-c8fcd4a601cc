<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.bigwheel.winners" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.drawerlayout.widget.DrawerLayout" rootNodeViewId="@+id/drawer_layout"><Targets><Target id="@+id/drawer_layout" tag="layout/activity_main_0" view="androidx.drawerlayout.widget.DrawerLayout"><Expressions/><location startLine="1" startOffset="0" endLine="634" endOffset="43"/></Target><Target id="@+id/constraintLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="16" startOffset="8" endLine="83" endOffset="59"/></Target><Target id="@+id/btn_menu" view="ImageView"><Expressions/><location startLine="26" startOffset="12" endLine="32" endOffset="59"/></Target><Target id="@+id/textView" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="45" endOffset="59"/></Target><Target id="@+id/tvDepositeAmmount" view="TextView"><Expressions/><location startLine="54" startOffset="16" endLine="62" endOffset="52"/></Target><Target id="@+id/tvWiningeAmmount" view="TextView"><Expressions/><location startLine="72" startOffset="16" endLine="79" endOffset="45"/></Target><Target id="@+id/linearLayout4" view="LinearLayout"><Expressions/><location startLine="86" startOffset="8" endLine="539" endOffset="22"/></Target><Target id="@+id/addMoneyLayout" view="ScrollView"><Expressions/><location startLine="97" startOffset="12" endLine="456" endOffset="24"/></Target><Target id="@+id/btn_closeaddmoney" view="ImageView"><Expressions/><location startLine="117" startOffset="16" endLine="128" endOffset="63"/></Target><Target id="@+id/textView9" view="TextView"><Expressions/><location startLine="131" startOffset="16" endLine="143" endOffset="63"/></Target><Target id="@+id/cvDiscountInfo" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="146" startOffset="16" endLine="184" endOffset="51"/></Target><Target id="@+id/etDeposite" view="EditText"><Expressions/><location startLine="187" startOffset="16" endLine="201" endOffset="79"/></Target><Target id="@+id/llPricingPlans" view="LinearLayout"><Expressions/><location startLine="204" startOffset="16" endLine="419" endOffset="30"/></Target><Target id="@+id/plan50" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="222" startOffset="24" endLine="265" endOffset="59"/></Target><Target id="@+id/price1" view="TextView"><Expressions/><location startLine="247" startOffset="32" endLine="254" endOffset="75"/></Target><Target id="@+id/plan100" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="268" startOffset="24" endLine="312" endOffset="59"/></Target><Target id="@+id/price2" view="TextView"><Expressions/><location startLine="293" startOffset="32" endLine="300" endOffset="82"/></Target><Target id="@+id/plan150" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="324" startOffset="24" endLine="368" endOffset="59"/></Target><Target id="@+id/price3" view="TextView"><Expressions/><location startLine="349" startOffset="32" endLine="356" endOffset="70"/></Target><Target id="@+id/plan200" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="371" startOffset="24" endLine="415" endOffset="59"/></Target><Target id="@+id/price4" view="TextView"><Expressions/><location startLine="396" startOffset="32" endLine="403" endOffset="77"/></Target><Target id="@+id/btnDeposite" view="LinearLayout"><Expressions/><location startLine="433" startOffset="20" endLine="452" endOffset="34"/></Target><Target id="@+id/withdrawMoneyLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="459" startOffset="12" endLine="537" endOffset="63"/></Target><Target id="@+id/btn_closewithdraw" view="ImageView"><Expressions/><location startLine="468" startOffset="16" endLine="476" endOffset="63"/></Target><Target id="@+id/textView11" view="TextView"><Expressions/><location startLine="478" startOffset="16" endLine="488" endOffset="63"/></Target><Target id="@+id/etWithdraw" view="EditText"><Expressions/><location startLine="490" startOffset="16" endLine="501" endOffset="75"/></Target><Target id="@+id/etWithdrawAmmount" view="EditText"><Expressions/><location startLine="503" startOffset="16" endLine="514" endOffset="75"/></Target><Target id="@+id/btnWithdrawFinal" view="LinearLayout"><Expressions/><location startLine="516" startOffset="16" endLine="535" endOffset="30"/></Target><Target id="@+id/wheelLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="542" startOffset="8" endLine="616" endOffset="59"/></Target><Target id="@+id/luckyWheel" view="ImageView"><Expressions/><location startLine="550" startOffset="12" endLine="558" endOffset="59"/></Target><Target id="@+id/btnAddMoney" view="LinearLayout"><Expressions/><location startLine="570" startOffset="16" endLine="590" endOffset="30"/></Target><Target id="@+id/btnSpin" view="LinearLayout"><Expressions/><location startLine="593" startOffset="16" endLine="612" endOffset="30"/></Target><Target id="@+id/nav_view" view="com.google.android.material.navigation.NavigationView"><Expressions/><location startLine="621" startOffset="4" endLine="632" endOffset="58"/></Target></Targets></Layout>