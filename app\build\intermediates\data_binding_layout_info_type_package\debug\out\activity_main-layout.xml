<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.bigwheel.winners" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.drawerlayout.widget.DrawerLayout" rootNodeViewId="@+id/drawer_layout"><Targets><Target id="@+id/drawer_layout" tag="layout/activity_main_0" view="androidx.drawerlayout.widget.DrawerLayout"><Expressions/><location startLine="1" startOffset="0" endLine="681" endOffset="43"/></Target><Target id="@+id/constraintLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="16" startOffset="8" endLine="83" endOffset="59"/></Target><Target id="@+id/btn_menu" view="ImageView"><Expressions/><location startLine="26" startOffset="12" endLine="32" endOffset="59"/></Target><Target id="@+id/textView" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="45" endOffset="59"/></Target><Target id="@+id/tvDepositeAmmount" view="TextView"><Expressions/><location startLine="54" startOffset="16" endLine="62" endOffset="52"/></Target><Target id="@+id/tvWiningeAmmount" view="TextView"><Expressions/><location startLine="72" startOffset="16" endLine="79" endOffset="45"/></Target><Target id="@+id/linearLayout4" view="LinearLayout"><Expressions/><location startLine="86" startOffset="8" endLine="586" endOffset="22"/></Target><Target id="@+id/addMoneyLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="97" startOffset="12" endLine="503" endOffset="63"/></Target><Target id="@+id/btn_closeaddmoney" view="ImageView"><Expressions/><location startLine="109" startOffset="16" endLine="120" endOffset="63"/></Target><Target id="@+id/textView9" view="TextView"><Expressions/><location startLine="123" startOffset="16" endLine="135" endOffset="63"/></Target><Target id="@+id/cvDiscountInfo" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="138" startOffset="16" endLine="176" endOffset="51"/></Target><Target id="@+id/etDeposite" view="EditText"><Expressions/><location startLine="179" startOffset="16" endLine="193" endOffset="79"/></Target><Target id="@+id/llPricingPlans" view="LinearLayout"><Expressions/><location startLine="196" startOffset="16" endLine="454" endOffset="30"/></Target><Target id="@+id/plan50" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="214" startOffset="24" endLine="267" endOffset="59"/></Target><Target id="@+id/price1" view="TextView"><Expressions/><location startLine="240" startOffset="32" endLine="248" endOffset="71"/></Target><Target id="@+id/plan100" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="270" startOffset="24" endLine="325" endOffset="59"/></Target><Target id="@+id/price2" view="TextView"><Expressions/><location startLine="296" startOffset="32" endLine="304" endOffset="71"/></Target><Target id="@+id/plan150" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="337" startOffset="24" endLine="392" endOffset="59"/></Target><Target id="@+id/price3" view="TextView"><Expressions/><location startLine="363" startOffset="32" endLine="371" endOffset="71"/></Target><Target id="@+id/plan200" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="395" startOffset="24" endLine="450" endOffset="59"/></Target><Target id="@+id/price4" view="TextView"><Expressions/><location startLine="421" startOffset="32" endLine="429" endOffset="71"/></Target><Target id="@+id/btnDeposite" view="LinearLayout"><Expressions/><location startLine="467" startOffset="20" endLine="500" endOffset="34"/></Target><Target id="@+id/withdrawMoneyLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="506" startOffset="12" endLine="584" endOffset="63"/></Target><Target id="@+id/btn_closewithdraw" view="ImageView"><Expressions/><location startLine="515" startOffset="16" endLine="523" endOffset="63"/></Target><Target id="@+id/textView11" view="TextView"><Expressions/><location startLine="525" startOffset="16" endLine="535" endOffset="63"/></Target><Target id="@+id/etWithdraw" view="EditText"><Expressions/><location startLine="537" startOffset="16" endLine="548" endOffset="75"/></Target><Target id="@+id/etWithdrawAmmount" view="EditText"><Expressions/><location startLine="550" startOffset="16" endLine="561" endOffset="75"/></Target><Target id="@+id/btnWithdrawFinal" view="LinearLayout"><Expressions/><location startLine="563" startOffset="16" endLine="582" endOffset="30"/></Target><Target id="@+id/wheelLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="589" startOffset="8" endLine="663" endOffset="59"/></Target><Target id="@+id/luckyWheel" view="ImageView"><Expressions/><location startLine="597" startOffset="12" endLine="605" endOffset="59"/></Target><Target id="@+id/btnAddMoney" view="LinearLayout"><Expressions/><location startLine="617" startOffset="16" endLine="637" endOffset="30"/></Target><Target id="@+id/btnSpin" view="LinearLayout"><Expressions/><location startLine="640" startOffset="16" endLine="659" endOffset="30"/></Target><Target id="@+id/nav_view" view="com.google.android.material.navigation.NavigationView"><Expressions/><location startLine="668" startOffset="4" endLine="679" endOffset="58"/></Target></Targets></Layout>