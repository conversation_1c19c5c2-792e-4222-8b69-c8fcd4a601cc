1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bigwheel.winners"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="22"
8-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="32" />
9-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
11-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:5:5-79
11-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:5:22-76
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:6:5-66
12-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:6:22-64
13
14    <!-- Create a unique permission for your app and use it so only your app can receive your OneSignal messages. -->
15    <permission
15-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:7:5-9:47
16        android:name="com.bigwheel.winners.permission.C2D_MESSAGE"
16-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:8:9-63
17        android:protectionLevel="signature" />
17-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:9:9-44
18
19    <uses-permission android:name="com.bigwheel.winners.permission.C2D_MESSAGE" /> <!-- OneSignal SDK has runtime checks Android version. -->
19-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:11:5-79
19-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:11:22-76
20    <!-- <uses-sdk tools:overrideLibrary="android.support.customtabs"/> -->
21    <!-- Required runtime permission to display notifications on Android 13 -->
22    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- c2dm RECEIVE are basic requirements for push messages through Google's FCM -->
22-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:16:5-77
22-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:16:22-74
23    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" /> <!-- Required, makes sure notifications are delivered on time. -->
23-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:18:5-82
23-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:18:22-79
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:19:5-68
24-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:19:22-65
25    <!--
26 Required so the device vibrates on receiving a push notification.
27         Vibration settings of the device still apply.
28    -->
29    <uses-permission android:name="android.permission.VIBRATE" />
29-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:24:5-66
29-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:24:22-63
30    <!--
31 Use to restore notifications the user hasn't interacted with.
32         They could be missed notifications if the user reboots their device if this isn't in place.
33    -->
34    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- START: ShortcutBadger -->
34-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:30:5-81
34-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:30:22-78
35    <!-- Samsung -->
36    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
36-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:32:5-86
36-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:32:22-83
37    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
37-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:33:5-87
37-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:33:22-84
38    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
38-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:34:5-81
38-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:34:22-78
39    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
39-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:35:5-83
39-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:35:22-80
40    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
40-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:36:5-88
40-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:36:22-85
41    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
41-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:37:5-92
41-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:37:22-89
42    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
42-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:38:5-84
42-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:38:22-81
43    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
43-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:39:5-83
43-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:39:22-80
44    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
44-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:40:5-91
44-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:40:22-88
45    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
45-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:41:5-92
45-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:41:22-89
46    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- ZUK -->
46-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:42:5-93
46-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:42:22-90
47    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- OPPO -->
47-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:43:5-73
47-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:43:22-70
48    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
48-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:44:5-82
48-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:44:22-79
49    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- EvMe -->
49-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:45:5-83
49-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:45:22-80
50    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
50-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:46:5-88
50-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:46:22-85
51    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
51-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:47:5-89
51-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:47:22-86
52    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
52-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:26:5-110
52-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:26:22-107
53    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
53-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
53-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:22-74
54
55    <application
55-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:8:5-64:19
56        android:allowBackup="true"
56-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:9:9-35
57        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
57-->[androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\f556f30d56993798d7ef134459fae338\transformed\core-1.8.0\AndroidManifest.xml:24:18-86
58        android:debuggable="true"
59        android:hardwareAccelerated="true"
59-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:10:9-43
60        android:icon="@drawable/logo"
60-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:11:9-38
61        android:label="@string/app_name"
61-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:12:9-41
62        android:requestLegacyExternalStorage="true"
62-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:13:9-52
63        android:roundIcon="@drawable/logo"
63-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:14:9-43
64        android:supportsRtl="true"
64-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:15:9-35
65        android:theme="@style/Theme.BigWheel"
65-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:16:9-46
66        android:usesCleartextTraffic="true" >
66-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:17:9-44
67        <activity
67-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:18:9-20:40
68            android:name="com.bigwheel.winners.splash.screensix"
68-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:19:13-45
69            android:exported="false" />
69-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:20:13-37
70        <activity
70-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:21:9-23:40
71            android:name="com.bigwheel.winners.splash.screenfive"
71-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:22:13-46
72            android:exported="false" />
72-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:23:13-37
73        <activity
73-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:24:9-26:40
74            android:name="com.bigwheel.winners.splash.screenfour"
74-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:25:13-46
75            android:exported="false" />
75-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:26:13-37
76        <activity
76-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:27:9-29:40
77            android:name="com.bigwheel.winners.splash.screenthree"
77-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:28:13-47
78            android:exported="false" />
78-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:29:13-37
79        <activity
79-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:30:9-32:40
80            android:name="com.bigwheel.winners.splash.screentwo"
80-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:31:13-45
81            android:exported="false" />
81-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:32:13-37
82        <activity
82-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:33:9-35:40
83            android:name="com.bigwheel.winners.splash.screenOne"
83-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:34:13-45
84            android:exported="false" />
84-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:35:13-37
85        <activity
85-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:36:9-44:20
86            android:name="com.bigwheel.winners.splash.splash"
86-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:37:13-42
87            android:exported="true" >
87-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:38:13-36
88            <intent-filter>
88-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:39:13-43:29
89                <action android:name="android.intent.action.MAIN" />
89-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:40:17-69
89-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:40:25-66
90
91                <category android:name="android.intent.category.LAUNCHER" />
91-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:42:17-77
91-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:42:27-74
92            </intent-filter>
93        </activity>
94        <activity android:name="com.bigwheel.winners.pay.PaymentTutorial" />
94-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:45:9-57
94-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:45:19-54
95        <activity android:name="com.bigwheel.winners.pay.PaymentViaUPI" />
95-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:46:9-55
95-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:46:19-52
96        <activity android:name="com.bigwheel.winners.pay.PaymentResultPayuMoney" />
96-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:47:9-64
96-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:47:19-61
97        <activity android:name="com.bigwheel.winners.pay.PaymentMethods" />
97-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:48:9-56
97-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:48:19-53
98        <activity
98-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:49:9-51:52
99            android:name="com.bigwheel.winners.GoogleLogin"
99-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:50:13-40
100            android:screenOrientation="portrait" />
100-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:51:13-49
101        <activity
101-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:52:9-54:52
102            android:name="com.bigwheel.winners.MainActivity"
102-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:53:13-41
103            android:screenOrientation="portrait" />
103-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:54:13-49
104        <activity
104-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:55:9-57:52
105            android:name="com.bigwheel.winners.pay.PaymentResult"
105-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:56:13-46
106            android:screenOrientation="portrait" />
106-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:57:13-49
107        <activity
107-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:58:9-60:52
108            android:name="com.bigwheel.winners.pay.MainActivitys"
108-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:59:13-46
109            android:screenOrientation="portrait" />
109-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:60:13-49
110        <activity
110-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:61:9-63:52
111            android:name="com.bigwheel.winners.SplashScreen"
111-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:62:13-41
112            android:screenOrientation="portrait" />
112-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:63:13-49
113
114        <receiver
114-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:50:9-61:20
115            android:name="com.onesignal.FCMBroadcastReceiver"
115-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:51:13-62
116            android:exported="true"
116-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:52:13-36
117            android:permission="com.google.android.c2dm.permission.SEND" >
117-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:53:13-73
118
119            <!-- High priority so OneSignal payloads can be filtered from other FCM receivers -->
120            <intent-filter android:priority="999" >
120-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:56:13-60:29
120-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:56:28-50
121                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
121-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:57:17-81
121-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:57:25-78
122
123                <category android:name="com.bigwheel.winners" />
123-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:59:17-61
123-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:59:27-58
124            </intent-filter>
125        </receiver>
126
127        <service
127-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:63:9-69:19
128            android:name="com.onesignal.HmsMessageServiceOneSignal"
128-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:64:13-68
129            android:exported="false" >
129-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:65:13-37
130            <intent-filter>
130-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:66:13-68:29
131                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
131-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:67:17-81
131-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:67:25-78
132            </intent-filter>
133        </service>
134
135        <activity
135-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:71:9-79:20
136            android:name="com.onesignal.NotificationOpenedActivityHMS"
136-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:72:13-71
137            android:exported="true"
137-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:73:13-36
138            android:noHistory="true"
138-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:74:13-37
139            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
139-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:75:13-72
140            <intent-filter>
140-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:76:13-78:29
141                <action android:name="android.intent.action.VIEW" />
141-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:77:17-69
141-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:77:25-66
142            </intent-filter>
143        </activity>
144
145        <service
145-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:81:9-83:40
146            android:name="com.onesignal.FCMIntentService"
146-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:82:13-58
147            android:exported="false" />
147-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:83:13-37
148        <service
148-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:84:9-87:72
149            android:name="com.onesignal.FCMIntentJobService"
149-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:85:13-61
150            android:exported="false"
150-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:86:13-37
151            android:permission="android.permission.BIND_JOB_SERVICE" />
151-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:87:13-69
152        <service
152-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:88:9-91:43
153            android:name="com.onesignal.SyncService"
153-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:89:13-53
154            android:exported="false"
154-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:90:13-37
155            android:stopWithTask="true" />
155-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:91:13-40
156        <service
156-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:92:9-95:72
157            android:name="com.onesignal.SyncJobService"
157-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:93:13-56
158            android:exported="false"
158-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:94:13-37
159            android:permission="android.permission.BIND_JOB_SERVICE" />
159-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:95:13-69
160
161        <activity
161-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:97:9-100:75
162            android:name="com.onesignal.PermissionsActivity"
162-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:98:13-61
163            android:exported="false"
163-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:99:13-37
164            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
164-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:100:13-72
165
166        <receiver
166-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:102:9-104:39
167            android:name="com.onesignal.NotificationDismissReceiver"
167-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:103:13-69
168            android:exported="true" />
168-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:104:13-36
169        <receiver
169-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:105:9-112:20
170            android:name="com.onesignal.BootUpReceiver"
170-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:106:13-56
171            android:exported="true" >
171-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:107:13-36
172            <intent-filter>
172-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:108:13-111:29
173                <action android:name="android.intent.action.BOOT_COMPLETED" />
173-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:109:17-79
173-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:109:25-76
174                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
174-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:110:17-82
174-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:110:25-79
175            </intent-filter>
176        </receiver>
177        <receiver
177-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:113:9-119:20
178            android:name="com.onesignal.UpgradeReceiver"
178-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:114:13-57
179            android:exported="true" >
179-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:115:13-36
180            <intent-filter>
180-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:116:13-118:29
181                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
181-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:117:17-84
181-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:117:25-81
182            </intent-filter>
183        </receiver>
184
185        <activity
185-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:121:9-127:75
186            android:name="com.onesignal.NotificationOpenedReceiver"
186-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:122:13-68
187            android:excludeFromRecents="true"
187-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:123:13-46
188            android:exported="true"
188-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:124:13-36
189            android:noHistory="true"
189-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:125:13-37
190            android:taskAffinity=""
190-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:126:13-36
191            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
191-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:127:13-72
192        <activity
192-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:128:9-133:75
193            android:name="com.onesignal.NotificationOpenedReceiverAndroid22AndOlder"
193-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:129:13-85
194            android:excludeFromRecents="true"
194-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:130:13-46
195            android:exported="true"
195-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:131:13-36
196            android:noHistory="true"
196-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:132:13-37
197            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
197-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:133:13-72
198
199        <service
199-->[com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:28:9-34:19
200            android:name="com.google.firebase.components.ComponentDiscoveryService"
200-->[com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:29:13-84
201            android:directBootAware="true"
201-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:34:13-43
202            android:exported="false" >
202-->[com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:30:13-37
203            <meta-data
203-->[com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:31:13-33:85
204                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
204-->[com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:32:17-109
205                android:value="com.google.firebase.components.ComponentRegistrar" />
205-->[com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:33:17-82
206            <meta-data
206-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:39:13-41:85
207                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
207-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:40:17-119
208                android:value="com.google.firebase.components.ComponentRegistrar" />
208-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:41:17-82
209            <meta-data
209-->[com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:32:13-34:85
210                android:name="com.google.firebase.components:com.google.firebase.iid.Registrar"
210-->[com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:33:17-96
211                android:value="com.google.firebase.components.ComponentRegistrar" />
211-->[com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:34:17-82
212            <meta-data
212-->[com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:30:13-32:85
213                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
213-->[com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:31:17-139
214                android:value="com.google.firebase.components.ComponentRegistrar" />
214-->[com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:32:17-82
215            <meta-data
215-->[com.google.firebase:firebase-datatransport:17.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\b723391e8f48268e968bf5f15f4f7103\transformed\jetified-firebase-datatransport-17.0.10\AndroidManifest.xml:28:13-30:85
216                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
216-->[com.google.firebase:firebase-datatransport:17.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\b723391e8f48268e968bf5f15f4f7103\transformed\jetified-firebase-datatransport-17.0.10\AndroidManifest.xml:29:17-115
217                android:value="com.google.firebase.components.ComponentRegistrar" />
217-->[com.google.firebase:firebase-datatransport:17.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\b723391e8f48268e968bf5f15f4f7103\transformed\jetified-firebase-datatransport-17.0.10\AndroidManifest.xml:30:17-82
218            <meta-data
218-->[com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\75afd29f29a59c314933f9e5db0ea997\transformed\jetified-firebase-installations-16.3.5\AndroidManifest.xml:18:13-20:85
219                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
219-->[com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\75afd29f29a59c314933f9e5db0ea997\transformed\jetified-firebase-installations-16.3.5\AndroidManifest.xml:19:17-127
220                android:value="com.google.firebase.components.ComponentRegistrar" />
220-->[com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\75afd29f29a59c314933f9e5db0ea997\transformed\jetified-firebase-installations-16.3.5\AndroidManifest.xml:20:17-82
221        </service>
222
223        <activity
223-->[com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:23:9-27:75
224            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
224-->[com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:24:13-93
225            android:excludeFromRecents="true"
225-->[com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:25:13-46
226            android:exported="false"
226-->[com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:26:13-37
227            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
227-->[com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:27:13-72
228        <!--
229            Service handling Google Sign-In user revocation. For apps that do not integrate with
230            Google Sign-In, this service will never be started.
231        -->
232        <service
232-->[com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:33:9-37:51
233            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
233-->[com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:34:13-89
234            android:exported="true"
234-->[com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:35:13-36
235            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
235-->[com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:36:13-107
236            android:visibleToInstantApps="true" />
236-->[com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:37:13-48
237
238        <provider
238-->[com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\transforms-3\214cedbbf2c701b1abf04d9c644dc4b3\transformed\jetified-picasso-2.71828\AndroidManifest.xml:8:9-11:40
239            android:name="com.squareup.picasso.PicassoProvider"
239-->[com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\transforms-3\214cedbbf2c701b1abf04d9c644dc4b3\transformed\jetified-picasso-2.71828\AndroidManifest.xml:9:13-64
240            android:authorities="com.bigwheel.winners.com.squareup.picasso"
240-->[com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\transforms-3\214cedbbf2c701b1abf04d9c644dc4b3\transformed\jetified-picasso-2.71828\AndroidManifest.xml:10:13-72
241            android:exported="false" />
241-->[com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\transforms-3\214cedbbf2c701b1abf04d9c644dc4b3\transformed\jetified-picasso-2.71828\AndroidManifest.xml:11:13-37
242        <!--
243             FirebaseMessagingService performs security checks at runtime,
244             but set to not exported to explicitly avoid allowing another app to call it.
245        -->
246        <service
246-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:28:9-35:19
247            android:name="com.google.firebase.messaging.FirebaseMessagingService"
247-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:29:13-82
248            android:directBootAware="true"
248-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:30:13-43
249            android:exported="false" >
249-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:31:13-37
250            <intent-filter android:priority="-500" >
250-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:32:13-34:29
250-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:32:28-51
251                <action android:name="com.google.firebase.MESSAGING_EVENT" />
251-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:33:17-78
251-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:33:25-75
252            </intent-filter>
253        </service>
254
255        <receiver
255-->[com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:37:9-44:20
256            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
256-->[com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:38:13-78
257            android:exported="true"
257-->[com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:39:13-36
258            android:permission="com.google.android.c2dm.permission.SEND" >
258-->[com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:40:13-73
259            <intent-filter>
259-->[com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:41:13-43:29
260                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
260-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:57:17-81
260-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:57:25-78
261            </intent-filter>
262        </receiver>
263
264        <activity
264-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\02436aca31e270f81bb7f76ee8fb72ca\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
265            android:name="com.google.android.gms.common.api.GoogleApiActivity"
265-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\02436aca31e270f81bb7f76ee8fb72ca\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
266            android:exported="false"
266-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\02436aca31e270f81bb7f76ee8fb72ca\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
267            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
267-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\02436aca31e270f81bb7f76ee8fb72ca\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
268
269        <provider
269-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:25:9-30:39
270            android:name="com.google.firebase.provider.FirebaseInitProvider"
270-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:26:13-77
271            android:authorities="com.bigwheel.winners.firebaseinitprovider"
271-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:27:13-72
272            android:directBootAware="true"
272-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:28:13-43
273            android:exported="false"
273-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:29:13-37
274            android:initOrder="100" />
274-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:30:13-36
275
276        <receiver
276-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:29:9-33:20
277            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
277-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:30:13-85
278            android:enabled="true"
278-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:31:13-35
279            android:exported="false" >
279-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:32:13-37
280        </receiver>
281
282        <service
282-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:35:9-38:40
283            android:name="com.google.android.gms.measurement.AppMeasurementService"
283-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:36:13-84
284            android:enabled="true"
284-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:37:13-35
285            android:exported="false" />
285-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:38:13-37
286        <service
286-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:39:9-43:72
287            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
287-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:40:13-87
288            android:enabled="true"
288-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:41:13-35
289            android:exported="false"
289-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:42:13-37
290            android:permission="android.permission.BIND_JOB_SERVICE" />
290-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:43:13-69
291
292        <meta-data
292-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b84e8a3931c74ff232fa18e9c8d1ffe2\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
293            android:name="com.google.android.gms.version"
293-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b84e8a3931c74ff232fa18e9c8d1ffe2\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
294            android:value="@integer/google_play_services_version" />
294-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b84e8a3931c74ff232fa18e9c8d1ffe2\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
295
296        <service
296-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
297            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
297-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
298            android:exported="false" >
298-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
299            <meta-data
299-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
300                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
300-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
301                android:value="cct" />
301-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
302        </service>
303
304        <provider
304-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
305            android:name="androidx.startup.InitializationProvider"
305-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
306            android:authorities="com.bigwheel.winners.androidx-startup"
306-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
307            android:exported="false" >
307-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
308            <meta-data
308-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
309                android:name="androidx.emoji2.text.EmojiCompatInitializer"
309-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
310                android:value="androidx.startup" />
310-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
311            <meta-data
311-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
312                android:name="androidx.work.WorkManagerInitializer"
312-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
313                android:value="androidx.startup" />
313-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
314            <meta-data
314-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ba820f391a6b9833f8808c735357937\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
315                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
315-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ba820f391a6b9833f8808c735357937\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
316                android:value="androidx.startup" />
316-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ba820f391a6b9833f8808c735357937\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
317        </provider>
318
319        <service
319-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
320            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
320-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
321            android:directBootAware="false"
321-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
322            android:enabled="@bool/enable_system_alarm_service_default"
322-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
323            android:exported="false" />
323-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
324        <service
324-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
325            android:name="androidx.work.impl.background.systemjob.SystemJobService"
325-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
326            android:directBootAware="false"
326-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
327            android:enabled="@bool/enable_system_job_service_default"
327-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
328            android:exported="true"
328-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
329            android:permission="android.permission.BIND_JOB_SERVICE" />
329-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
330        <service
330-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
331            android:name="androidx.work.impl.foreground.SystemForegroundService"
331-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
332            android:directBootAware="false"
332-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
333            android:enabled="@bool/enable_system_foreground_service_default"
333-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
334            android:exported="false" />
334-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
335
336        <receiver
336-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
337            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
337-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
338            android:directBootAware="false"
338-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
339            android:enabled="true"
339-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
340            android:exported="false" />
340-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
341        <receiver
341-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
342            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
342-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
343            android:directBootAware="false"
343-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
344            android:enabled="false"
344-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
345            android:exported="false" >
345-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
346            <intent-filter>
346-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
347                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
347-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
347-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
348                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
348-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
348-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
349            </intent-filter>
350        </receiver>
351        <receiver
351-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
352            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
352-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
353            android:directBootAware="false"
353-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
354            android:enabled="false"
354-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
355            android:exported="false" >
355-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
356            <intent-filter>
356-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
357                <action android:name="android.intent.action.BATTERY_OKAY" />
357-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
357-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
358                <action android:name="android.intent.action.BATTERY_LOW" />
358-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
358-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
359            </intent-filter>
360        </receiver>
361        <receiver
361-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
362            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
362-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
363            android:directBootAware="false"
363-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
364            android:enabled="false"
364-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
365            android:exported="false" >
365-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
366            <intent-filter>
366-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
367                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
367-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
367-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
368                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
368-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
368-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
369            </intent-filter>
370        </receiver>
371        <receiver
371-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
372            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
372-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
373            android:directBootAware="false"
373-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
374            android:enabled="false"
374-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
375            android:exported="false" >
375-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
376            <intent-filter>
376-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
377                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
377-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
377-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
378            </intent-filter>
379        </receiver>
380        <receiver
380-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
381            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
381-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
382            android:directBootAware="false"
382-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
383            android:enabled="false"
383-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
384            android:exported="false" >
384-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
385            <intent-filter>
385-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
386                <action android:name="android.intent.action.BOOT_COMPLETED" />
386-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:109:17-79
386-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:109:25-76
387                <action android:name="android.intent.action.TIME_SET" />
387-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
387-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
388                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
388-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
388-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
389            </intent-filter>
390        </receiver>
391        <receiver
391-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
392            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
392-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
393            android:directBootAware="false"
393-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
394            android:enabled="@bool/enable_system_alarm_service_default"
394-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
395            android:exported="false" >
395-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
396            <intent-filter>
396-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
397                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
397-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
397-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
398            </intent-filter>
399        </receiver>
400        <receiver
400-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
401            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
401-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
402            android:directBootAware="false"
402-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
403            android:enabled="true"
403-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
404            android:exported="true"
404-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
405            android:permission="android.permission.DUMP" >
405-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
406            <intent-filter>
406-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
407                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
407-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
407-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
408            </intent-filter>
409        </receiver>
410
411        <service
411-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\4dc13d69c529875d6c5460d4ad7e3591\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
412            android:name="androidx.room.MultiInstanceInvalidationService"
412-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\4dc13d69c529875d6c5460d4ad7e3591\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
413            android:directBootAware="true"
413-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\4dc13d69c529875d6c5460d4ad7e3591\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
414            android:exported="false" />
414-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\4dc13d69c529875d6c5460d4ad7e3591\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
415        <service
415-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:26:9-30:19
416            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
416-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:27:13-117
417            android:exported="false"
417-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:28:13-37
418            android:permission="android.permission.BIND_JOB_SERVICE" >
418-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:29:13-69
419        </service>
420
421        <receiver
421-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:32:9-34:40
422            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
422-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:33:13-132
423            android:exported="false" />
423-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:34:13-37
424    </application>
425
426</manifest>
