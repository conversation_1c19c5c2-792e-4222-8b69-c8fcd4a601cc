1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bigwheel.winners"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="22"
8-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="32" />
9-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
11-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:5:5-79
11-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:5:22-76
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:6:5-66
12-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:6:22-64
13
14    <!-- Create a unique permission for your app and use it so only your app can receive your OneSignal messages. -->
15    <permission
15-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:7:5-9:47
16        android:name="com.bigwheel.winners.permission.C2D_MESSAGE"
16-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:8:9-63
17        android:protectionLevel="signature" />
17-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:9:9-44
18
19    <uses-permission android:name="com.bigwheel.winners.permission.C2D_MESSAGE" /> <!-- OneSignal SDK has runtime checks Android version. -->
19-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:11:5-79
19-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:11:22-76
20    <!-- <uses-sdk tools:overrideLibrary="android.support.customtabs"/> -->
21    <!-- Required runtime permission to display notifications on Android 13 -->
22    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- c2dm RECEIVE are basic requirements for push messages through Google's FCM -->
22-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:16:5-77
22-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:16:22-74
23    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" /> <!-- Required, makes sure notifications are delivered on time. -->
23-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:18:5-82
23-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:18:22-79
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:19:5-68
24-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:19:22-65
25    <!--
26 Required so the device vibrates on receiving a push notification.
27         Vibration settings of the device still apply.
28    -->
29    <uses-permission android:name="android.permission.VIBRATE" />
29-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:24:5-66
29-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:24:22-63
30    <!--
31 Use to restore notifications the user hasn't interacted with.
32         They could be missed notifications if the user reboots their device if this isn't in place.
33    -->
34    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- START: ShortcutBadger -->
34-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:30:5-81
34-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:30:22-78
35    <!-- Samsung -->
36    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
36-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:32:5-86
36-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:32:22-83
37    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
37-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:33:5-87
37-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:33:22-84
38    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
38-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:34:5-81
38-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:34:22-78
39    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
39-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:35:5-83
39-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:35:22-80
40    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
40-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:36:5-88
40-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:36:22-85
41    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
41-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:37:5-92
41-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:37:22-89
42    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
42-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:38:5-84
42-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:38:22-81
43    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
43-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:39:5-83
43-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:39:22-80
44    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
44-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:40:5-91
44-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:40:22-88
45    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
45-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:41:5-92
45-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:41:22-89
46    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- ZUK -->
46-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:42:5-93
46-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:42:22-90
47    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- OPPO -->
47-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:43:5-73
47-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:43:22-70
48    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
48-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:44:5-82
48-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:44:22-79
49    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- EvMe -->
49-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:45:5-83
49-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:45:22-80
50    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
50-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:46:5-88
50-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:46:22-85
51    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
51-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:47:5-89
51-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:47:22-86
52    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
52-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:26:5-110
52-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:26:22-107
53    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
53-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
53-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:22-74
54
55    <application
55-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:8:5-64:19
56        android:allowBackup="true"
56-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:9:9-35
57        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
57-->[androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\f556f30d56993798d7ef134459fae338\transformed\core-1.8.0\AndroidManifest.xml:24:18-86
58        android:debuggable="true"
59        android:hardwareAccelerated="true"
59-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:10:9-43
60        android:icon="@drawable/logo"
60-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:11:9-38
61        android:label="@string/app_name"
61-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:12:9-41
62        android:requestLegacyExternalStorage="true"
62-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:13:9-52
63        android:roundIcon="@drawable/logo"
63-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:14:9-43
64        android:supportsRtl="true"
64-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:15:9-35
65        android:testOnly="true"
66        android:theme="@style/Theme.BigWheel"
66-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:16:9-46
67        android:usesCleartextTraffic="true" >
67-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:17:9-44
68        <activity
68-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:18:9-20:40
69            android:name="com.bigwheel.winners.splash.screensix"
69-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:19:13-45
70            android:exported="false" />
70-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:20:13-37
71        <activity
71-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:21:9-23:40
72            android:name="com.bigwheel.winners.splash.screenfive"
72-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:22:13-46
73            android:exported="false" />
73-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:23:13-37
74        <activity
74-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:24:9-26:40
75            android:name="com.bigwheel.winners.splash.screenfour"
75-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:25:13-46
76            android:exported="false" />
76-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:26:13-37
77        <activity
77-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:27:9-29:40
78            android:name="com.bigwheel.winners.splash.screenthree"
78-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:28:13-47
79            android:exported="false" />
79-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:29:13-37
80        <activity
80-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:30:9-32:40
81            android:name="com.bigwheel.winners.splash.screentwo"
81-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:31:13-45
82            android:exported="false" />
82-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:32:13-37
83        <activity
83-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:33:9-35:40
84            android:name="com.bigwheel.winners.splash.screenOne"
84-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:34:13-45
85            android:exported="false" />
85-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:35:13-37
86        <activity
86-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:36:9-44:20
87            android:name="com.bigwheel.winners.splash.splash"
87-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:37:13-42
88            android:exported="true" >
88-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:38:13-36
89            <intent-filter>
89-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:39:13-43:29
90                <action android:name="android.intent.action.MAIN" />
90-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:40:17-69
90-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:40:25-66
91
92                <category android:name="android.intent.category.LAUNCHER" />
92-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:42:17-77
92-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:42:27-74
93            </intent-filter>
94        </activity>
95        <activity android:name="com.bigwheel.winners.pay.PaymentTutorial" />
95-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:45:9-57
95-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:45:19-54
96        <activity android:name="com.bigwheel.winners.pay.PaymentViaUPI" />
96-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:46:9-55
96-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:46:19-52
97        <activity android:name="com.bigwheel.winners.pay.PaymentResultPayuMoney" />
97-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:47:9-64
97-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:47:19-61
98        <activity android:name="com.bigwheel.winners.pay.PaymentMethods" />
98-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:48:9-56
98-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:48:19-53
99        <activity
99-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:49:9-51:52
100            android:name="com.bigwheel.winners.GoogleLogin"
100-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:50:13-40
101            android:screenOrientation="portrait" />
101-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:51:13-49
102        <activity
102-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:52:9-54:52
103            android:name="com.bigwheel.winners.MainActivity"
103-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:53:13-41
104            android:screenOrientation="portrait" />
104-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:54:13-49
105        <activity
105-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:55:9-57:52
106            android:name="com.bigwheel.winners.pay.PaymentResult"
106-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:56:13-46
107            android:screenOrientation="portrait" />
107-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:57:13-49
108        <activity
108-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:58:9-60:52
109            android:name="com.bigwheel.winners.pay.MainActivitys"
109-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:59:13-46
110            android:screenOrientation="portrait" />
110-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:60:13-49
111        <activity
111-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:61:9-63:52
112            android:name="com.bigwheel.winners.SplashScreen"
112-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:62:13-41
113            android:screenOrientation="portrait" />
113-->D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:63:13-49
114
115        <receiver
115-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:50:9-61:20
116            android:name="com.onesignal.FCMBroadcastReceiver"
116-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:51:13-62
117            android:exported="true"
117-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:52:13-36
118            android:permission="com.google.android.c2dm.permission.SEND" >
118-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:53:13-73
119
120            <!-- High priority so OneSignal payloads can be filtered from other FCM receivers -->
121            <intent-filter android:priority="999" >
121-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:56:13-60:29
121-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:56:28-50
122                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
122-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:57:17-81
122-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:57:25-78
123
124                <category android:name="com.bigwheel.winners" />
124-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:59:17-61
124-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:59:27-58
125            </intent-filter>
126        </receiver>
127
128        <service
128-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:63:9-69:19
129            android:name="com.onesignal.HmsMessageServiceOneSignal"
129-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:64:13-68
130            android:exported="false" >
130-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:65:13-37
131            <intent-filter>
131-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:66:13-68:29
132                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
132-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:67:17-81
132-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:67:25-78
133            </intent-filter>
134        </service>
135
136        <activity
136-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:71:9-79:20
137            android:name="com.onesignal.NotificationOpenedActivityHMS"
137-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:72:13-71
138            android:exported="true"
138-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:73:13-36
139            android:noHistory="true"
139-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:74:13-37
140            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
140-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:75:13-72
141            <intent-filter>
141-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:76:13-78:29
142                <action android:name="android.intent.action.VIEW" />
142-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:77:17-69
142-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:77:25-66
143            </intent-filter>
144        </activity>
145
146        <service
146-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:81:9-83:40
147            android:name="com.onesignal.FCMIntentService"
147-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:82:13-58
148            android:exported="false" />
148-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:83:13-37
149        <service
149-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:84:9-87:72
150            android:name="com.onesignal.FCMIntentJobService"
150-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:85:13-61
151            android:exported="false"
151-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:86:13-37
152            android:permission="android.permission.BIND_JOB_SERVICE" />
152-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:87:13-69
153        <service
153-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:88:9-91:43
154            android:name="com.onesignal.SyncService"
154-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:89:13-53
155            android:exported="false"
155-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:90:13-37
156            android:stopWithTask="true" />
156-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:91:13-40
157        <service
157-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:92:9-95:72
158            android:name="com.onesignal.SyncJobService"
158-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:93:13-56
159            android:exported="false"
159-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:94:13-37
160            android:permission="android.permission.BIND_JOB_SERVICE" />
160-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:95:13-69
161
162        <activity
162-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:97:9-100:75
163            android:name="com.onesignal.PermissionsActivity"
163-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:98:13-61
164            android:exported="false"
164-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:99:13-37
165            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
165-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:100:13-72
166
167        <receiver
167-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:102:9-104:39
168            android:name="com.onesignal.NotificationDismissReceiver"
168-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:103:13-69
169            android:exported="true" />
169-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:104:13-36
170        <receiver
170-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:105:9-112:20
171            android:name="com.onesignal.BootUpReceiver"
171-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:106:13-56
172            android:exported="true" >
172-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:107:13-36
173            <intent-filter>
173-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:108:13-111:29
174                <action android:name="android.intent.action.BOOT_COMPLETED" />
174-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:109:17-79
174-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:109:25-76
175                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
175-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:110:17-82
175-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:110:25-79
176            </intent-filter>
177        </receiver>
178        <receiver
178-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:113:9-119:20
179            android:name="com.onesignal.UpgradeReceiver"
179-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:114:13-57
180            android:exported="true" >
180-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:115:13-36
181            <intent-filter>
181-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:116:13-118:29
182                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
182-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:117:17-84
182-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:117:25-81
183            </intent-filter>
184        </receiver>
185
186        <activity
186-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:121:9-127:75
187            android:name="com.onesignal.NotificationOpenedReceiver"
187-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:122:13-68
188            android:excludeFromRecents="true"
188-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:123:13-46
189            android:exported="true"
189-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:124:13-36
190            android:noHistory="true"
190-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:125:13-37
191            android:taskAffinity=""
191-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:126:13-36
192            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
192-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:127:13-72
193        <activity
193-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:128:9-133:75
194            android:name="com.onesignal.NotificationOpenedReceiverAndroid22AndOlder"
194-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:129:13-85
195            android:excludeFromRecents="true"
195-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:130:13-46
196            android:exported="true"
196-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:131:13-36
197            android:noHistory="true"
197-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:132:13-37
198            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
198-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:133:13-72
199
200        <service
200-->[com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:28:9-34:19
201            android:name="com.google.firebase.components.ComponentDiscoveryService"
201-->[com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:29:13-84
202            android:directBootAware="true"
202-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:34:13-43
203            android:exported="false" >
203-->[com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:30:13-37
204            <meta-data
204-->[com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:31:13-33:85
205                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
205-->[com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:32:17-109
206                android:value="com.google.firebase.components.ComponentRegistrar" />
206-->[com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:33:17-82
207            <meta-data
207-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:39:13-41:85
208                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
208-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:40:17-119
209                android:value="com.google.firebase.components.ComponentRegistrar" />
209-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:41:17-82
210            <meta-data
210-->[com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:32:13-34:85
211                android:name="com.google.firebase.components:com.google.firebase.iid.Registrar"
211-->[com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:33:17-96
212                android:value="com.google.firebase.components.ComponentRegistrar" />
212-->[com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:34:17-82
213            <meta-data
213-->[com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:30:13-32:85
214                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
214-->[com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:31:17-139
215                android:value="com.google.firebase.components.ComponentRegistrar" />
215-->[com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:32:17-82
216            <meta-data
216-->[com.google.firebase:firebase-datatransport:17.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\b723391e8f48268e968bf5f15f4f7103\transformed\jetified-firebase-datatransport-17.0.10\AndroidManifest.xml:28:13-30:85
217                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
217-->[com.google.firebase:firebase-datatransport:17.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\b723391e8f48268e968bf5f15f4f7103\transformed\jetified-firebase-datatransport-17.0.10\AndroidManifest.xml:29:17-115
218                android:value="com.google.firebase.components.ComponentRegistrar" />
218-->[com.google.firebase:firebase-datatransport:17.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\b723391e8f48268e968bf5f15f4f7103\transformed\jetified-firebase-datatransport-17.0.10\AndroidManifest.xml:30:17-82
219            <meta-data
219-->[com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\75afd29f29a59c314933f9e5db0ea997\transformed\jetified-firebase-installations-16.3.5\AndroidManifest.xml:18:13-20:85
220                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
220-->[com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\75afd29f29a59c314933f9e5db0ea997\transformed\jetified-firebase-installations-16.3.5\AndroidManifest.xml:19:17-127
221                android:value="com.google.firebase.components.ComponentRegistrar" />
221-->[com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\75afd29f29a59c314933f9e5db0ea997\transformed\jetified-firebase-installations-16.3.5\AndroidManifest.xml:20:17-82
222        </service>
223
224        <activity
224-->[com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:23:9-27:75
225            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
225-->[com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:24:13-93
226            android:excludeFromRecents="true"
226-->[com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:25:13-46
227            android:exported="false"
227-->[com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:26:13-37
228            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
228-->[com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:27:13-72
229        <!--
230            Service handling Google Sign-In user revocation. For apps that do not integrate with
231            Google Sign-In, this service will never be started.
232        -->
233        <service
233-->[com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:33:9-37:51
234            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
234-->[com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:34:13-89
235            android:exported="true"
235-->[com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:35:13-36
236            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
236-->[com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:36:13-107
237            android:visibleToInstantApps="true" />
237-->[com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:37:13-48
238
239        <provider
239-->[com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\transforms-3\214cedbbf2c701b1abf04d9c644dc4b3\transformed\jetified-picasso-2.71828\AndroidManifest.xml:8:9-11:40
240            android:name="com.squareup.picasso.PicassoProvider"
240-->[com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\transforms-3\214cedbbf2c701b1abf04d9c644dc4b3\transformed\jetified-picasso-2.71828\AndroidManifest.xml:9:13-64
241            android:authorities="com.bigwheel.winners.com.squareup.picasso"
241-->[com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\transforms-3\214cedbbf2c701b1abf04d9c644dc4b3\transformed\jetified-picasso-2.71828\AndroidManifest.xml:10:13-72
242            android:exported="false" />
242-->[com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\transforms-3\214cedbbf2c701b1abf04d9c644dc4b3\transformed\jetified-picasso-2.71828\AndroidManifest.xml:11:13-37
243        <!--
244             FirebaseMessagingService performs security checks at runtime,
245             but set to not exported to explicitly avoid allowing another app to call it.
246        -->
247        <service
247-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:28:9-35:19
248            android:name="com.google.firebase.messaging.FirebaseMessagingService"
248-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:29:13-82
249            android:directBootAware="true"
249-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:30:13-43
250            android:exported="false" >
250-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:31:13-37
251            <intent-filter android:priority="-500" >
251-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:32:13-34:29
251-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:32:28-51
252                <action android:name="com.google.firebase.MESSAGING_EVENT" />
252-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:33:17-78
252-->[com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:33:25-75
253            </intent-filter>
254        </service>
255
256        <receiver
256-->[com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:37:9-44:20
257            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
257-->[com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:38:13-78
258            android:exported="true"
258-->[com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:39:13-36
259            android:permission="com.google.android.c2dm.permission.SEND" >
259-->[com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:40:13-73
260            <intent-filter>
260-->[com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:41:13-43:29
261                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
261-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:57:17-81
261-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:57:25-78
262            </intent-filter>
263        </receiver>
264
265        <activity
265-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\02436aca31e270f81bb7f76ee8fb72ca\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
266            android:name="com.google.android.gms.common.api.GoogleApiActivity"
266-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\02436aca31e270f81bb7f76ee8fb72ca\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
267            android:exported="false"
267-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\02436aca31e270f81bb7f76ee8fb72ca\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
268            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
268-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\02436aca31e270f81bb7f76ee8fb72ca\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
269
270        <provider
270-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:25:9-30:39
271            android:name="com.google.firebase.provider.FirebaseInitProvider"
271-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:26:13-77
272            android:authorities="com.bigwheel.winners.firebaseinitprovider"
272-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:27:13-72
273            android:directBootAware="true"
273-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:28:13-43
274            android:exported="false"
274-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:29:13-37
275            android:initOrder="100" />
275-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:30:13-36
276
277        <receiver
277-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:29:9-33:20
278            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
278-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:30:13-85
279            android:enabled="true"
279-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:31:13-35
280            android:exported="false" >
280-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:32:13-37
281        </receiver>
282
283        <service
283-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:35:9-38:40
284            android:name="com.google.android.gms.measurement.AppMeasurementService"
284-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:36:13-84
285            android:enabled="true"
285-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:37:13-35
286            android:exported="false" />
286-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:38:13-37
287        <service
287-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:39:9-43:72
288            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
288-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:40:13-87
289            android:enabled="true"
289-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:41:13-35
290            android:exported="false"
290-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:42:13-37
291            android:permission="android.permission.BIND_JOB_SERVICE" />
291-->[com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:43:13-69
292
293        <meta-data
293-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b84e8a3931c74ff232fa18e9c8d1ffe2\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
294            android:name="com.google.android.gms.version"
294-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b84e8a3931c74ff232fa18e9c8d1ffe2\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
295            android:value="@integer/google_play_services_version" />
295-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b84e8a3931c74ff232fa18e9c8d1ffe2\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
296
297        <service
297-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
298            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
298-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
299            android:exported="false" >
299-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
300            <meta-data
300-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
301                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
301-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
302                android:value="cct" />
302-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
303        </service>
304
305        <provider
305-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
306            android:name="androidx.startup.InitializationProvider"
306-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
307            android:authorities="com.bigwheel.winners.androidx-startup"
307-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
308            android:exported="false" >
308-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
309            <meta-data
309-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
310                android:name="androidx.emoji2.text.EmojiCompatInitializer"
310-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
311                android:value="androidx.startup" />
311-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
312            <meta-data
312-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
313                android:name="androidx.work.WorkManagerInitializer"
313-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
314                android:value="androidx.startup" />
314-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
315            <meta-data
315-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ba820f391a6b9833f8808c735357937\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
316                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
316-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ba820f391a6b9833f8808c735357937\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
317                android:value="androidx.startup" />
317-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ba820f391a6b9833f8808c735357937\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
318        </provider>
319
320        <service
320-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
321            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
321-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
322            android:directBootAware="false"
322-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
323            android:enabled="@bool/enable_system_alarm_service_default"
323-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
324            android:exported="false" />
324-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
325        <service
325-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
326            android:name="androidx.work.impl.background.systemjob.SystemJobService"
326-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
327            android:directBootAware="false"
327-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
328            android:enabled="@bool/enable_system_job_service_default"
328-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
329            android:exported="true"
329-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
330            android:permission="android.permission.BIND_JOB_SERVICE" />
330-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
331        <service
331-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
332            android:name="androidx.work.impl.foreground.SystemForegroundService"
332-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
333            android:directBootAware="false"
333-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
334            android:enabled="@bool/enable_system_foreground_service_default"
334-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
335            android:exported="false" />
335-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
336
337        <receiver
337-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
338            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
338-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
339            android:directBootAware="false"
339-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
340            android:enabled="true"
340-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
341            android:exported="false" />
341-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
342        <receiver
342-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
343            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
343-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
344            android:directBootAware="false"
344-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
345            android:enabled="false"
345-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
346            android:exported="false" >
346-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
347            <intent-filter>
347-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
348                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
348-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
348-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
349                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
349-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
349-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
350            </intent-filter>
351        </receiver>
352        <receiver
352-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
353            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
353-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
354            android:directBootAware="false"
354-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
355            android:enabled="false"
355-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
356            android:exported="false" >
356-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
357            <intent-filter>
357-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
358                <action android:name="android.intent.action.BATTERY_OKAY" />
358-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
358-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
359                <action android:name="android.intent.action.BATTERY_LOW" />
359-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
359-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
360            </intent-filter>
361        </receiver>
362        <receiver
362-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
363            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
363-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
364            android:directBootAware="false"
364-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
365            android:enabled="false"
365-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
366            android:exported="false" >
366-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
367            <intent-filter>
367-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
368                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
368-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
368-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
369                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
369-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
369-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
370            </intent-filter>
371        </receiver>
372        <receiver
372-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
373            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
373-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
374            android:directBootAware="false"
374-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
375            android:enabled="false"
375-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
376            android:exported="false" >
376-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
377            <intent-filter>
377-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
378                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
378-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
378-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
379            </intent-filter>
380        </receiver>
381        <receiver
381-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
382            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
382-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
383            android:directBootAware="false"
383-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
384            android:enabled="false"
384-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
385            android:exported="false" >
385-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
386            <intent-filter>
386-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
387                <action android:name="android.intent.action.BOOT_COMPLETED" />
387-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:109:17-79
387-->[com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:109:25-76
388                <action android:name="android.intent.action.TIME_SET" />
388-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
388-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
389                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
389-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
389-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
390            </intent-filter>
391        </receiver>
392        <receiver
392-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
393            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
393-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
394            android:directBootAware="false"
394-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
395            android:enabled="@bool/enable_system_alarm_service_default"
395-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
396            android:exported="false" >
396-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
397            <intent-filter>
397-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
398                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
398-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
398-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
399            </intent-filter>
400        </receiver>
401        <receiver
401-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
402            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
402-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
403            android:directBootAware="false"
403-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
404            android:enabled="true"
404-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
405            android:exported="true"
405-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
406            android:permission="android.permission.DUMP" >
406-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
407            <intent-filter>
407-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
408                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
408-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
408-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
409            </intent-filter>
410        </receiver>
411
412        <service
412-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\4dc13d69c529875d6c5460d4ad7e3591\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
413            android:name="androidx.room.MultiInstanceInvalidationService"
413-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\4dc13d69c529875d6c5460d4ad7e3591\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
414            android:directBootAware="true"
414-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\4dc13d69c529875d6c5460d4ad7e3591\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
415            android:exported="false" />
415-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\4dc13d69c529875d6c5460d4ad7e3591\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
416        <service
416-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:26:9-30:19
417            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
417-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:27:13-117
418            android:exported="false"
418-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:28:13-37
419            android:permission="android.permission.BIND_JOB_SERVICE" >
419-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:29:13-69
420        </service>
421
422        <receiver
422-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:32:9-34:40
423            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
423-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:33:13-132
424            android:exported="false" />
424-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:34:13-37
425    </application>
426
427</manifest>
