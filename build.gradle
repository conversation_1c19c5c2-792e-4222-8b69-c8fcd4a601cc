// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext.kotlin_version = "1.6.0"
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        jcenter()
        /*maven {
            url 'https://maven.google.com'
        }*/
    }
    dependencies {
        classpath "com.android.tools.build:gradle:4.2.2"
        classpath 'com.google.gms:google-services:4.3.10'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'gradle.plugin.com.onesignal:onesignal-gradle-plugin:[0.12.10, 0.99.99]'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id 'com.android.application' version '7.1.1' apply false
    id 'com.android.library' version '7.1.1' apply false
}

task clean(type: Delete) {
    delete rootProject.buildDir
}