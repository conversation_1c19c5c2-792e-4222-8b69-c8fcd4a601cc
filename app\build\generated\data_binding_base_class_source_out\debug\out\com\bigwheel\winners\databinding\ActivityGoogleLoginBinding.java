// Generated by view binder compiler. Do not edit!
package com.bigwheel.winners.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bigwheel.winners.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityGoogleLoginBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ConstraintLayout btnSignIn;

  @NonNull
  public final TextView btnSignUp;

  @NonNull
  public final CheckBox checkboxTerms;

  @NonNull
  public final ConstraintLayout constraintLayout2;

  @NonNull
  public final EditText etNumber;

  @NonNull
  public final EditText etPassword;

  @NonNull
  public final View hr;

  @NonNull
  public final LinearLayout ivGooglelogin;

  @NonNull
  public final TextView textView12;

  @NonNull
  public final TextView textView13;

  @NonNull
  public final TextView textView2;

  @NonNull
  public final TextView textView3;

  @NonNull
  public final TextView textView4;

  @NonNull
  public final TextView textView5;

  @NonNull
  public final TextView textView6;

  @NonNull
  public final TextView tvForgotPassword;

  @NonNull
  public final TextView tvOr;

  @NonNull
  public final TextView tvPrivacy;

  @NonNull
  public final TextView tvTerms;

  private ActivityGoogleLoginBinding(@NonNull ConstraintLayout rootView,
      @NonNull ConstraintLayout btnSignIn, @NonNull TextView btnSignUp,
      @NonNull CheckBox checkboxTerms, @NonNull ConstraintLayout constraintLayout2,
      @NonNull EditText etNumber, @NonNull EditText etPassword, @NonNull View hr,
      @NonNull LinearLayout ivGooglelogin, @NonNull TextView textView12,
      @NonNull TextView textView13, @NonNull TextView textView2, @NonNull TextView textView3,
      @NonNull TextView textView4, @NonNull TextView textView5, @NonNull TextView textView6,
      @NonNull TextView tvForgotPassword, @NonNull TextView tvOr, @NonNull TextView tvPrivacy,
      @NonNull TextView tvTerms) {
    this.rootView = rootView;
    this.btnSignIn = btnSignIn;
    this.btnSignUp = btnSignUp;
    this.checkboxTerms = checkboxTerms;
    this.constraintLayout2 = constraintLayout2;
    this.etNumber = etNumber;
    this.etPassword = etPassword;
    this.hr = hr;
    this.ivGooglelogin = ivGooglelogin;
    this.textView12 = textView12;
    this.textView13 = textView13;
    this.textView2 = textView2;
    this.textView3 = textView3;
    this.textView4 = textView4;
    this.textView5 = textView5;
    this.textView6 = textView6;
    this.tvForgotPassword = tvForgotPassword;
    this.tvOr = tvOr;
    this.tvPrivacy = tvPrivacy;
    this.tvTerms = tvTerms;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityGoogleLoginBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityGoogleLoginBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_google_login, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityGoogleLoginBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnSignIn;
      ConstraintLayout btnSignIn = ViewBindings.findChildViewById(rootView, id);
      if (btnSignIn == null) {
        break missingId;
      }

      id = R.id.btnSignUp;
      TextView btnSignUp = ViewBindings.findChildViewById(rootView, id);
      if (btnSignUp == null) {
        break missingId;
      }

      id = R.id.checkbox_terms;
      CheckBox checkboxTerms = ViewBindings.findChildViewById(rootView, id);
      if (checkboxTerms == null) {
        break missingId;
      }

      id = R.id.constraintLayout2;
      ConstraintLayout constraintLayout2 = ViewBindings.findChildViewById(rootView, id);
      if (constraintLayout2 == null) {
        break missingId;
      }

      id = R.id.etNumber;
      EditText etNumber = ViewBindings.findChildViewById(rootView, id);
      if (etNumber == null) {
        break missingId;
      }

      id = R.id.etPassword;
      EditText etPassword = ViewBindings.findChildViewById(rootView, id);
      if (etPassword == null) {
        break missingId;
      }

      id = R.id.hr;
      View hr = ViewBindings.findChildViewById(rootView, id);
      if (hr == null) {
        break missingId;
      }

      id = R.id.iv_googlelogin;
      LinearLayout ivGooglelogin = ViewBindings.findChildViewById(rootView, id);
      if (ivGooglelogin == null) {
        break missingId;
      }

      id = R.id.textView12;
      TextView textView12 = ViewBindings.findChildViewById(rootView, id);
      if (textView12 == null) {
        break missingId;
      }

      id = R.id.textView13;
      TextView textView13 = ViewBindings.findChildViewById(rootView, id);
      if (textView13 == null) {
        break missingId;
      }

      id = R.id.textView2;
      TextView textView2 = ViewBindings.findChildViewById(rootView, id);
      if (textView2 == null) {
        break missingId;
      }

      id = R.id.textView3;
      TextView textView3 = ViewBindings.findChildViewById(rootView, id);
      if (textView3 == null) {
        break missingId;
      }

      id = R.id.textView4;
      TextView textView4 = ViewBindings.findChildViewById(rootView, id);
      if (textView4 == null) {
        break missingId;
      }

      id = R.id.textView5;
      TextView textView5 = ViewBindings.findChildViewById(rootView, id);
      if (textView5 == null) {
        break missingId;
      }

      id = R.id.textView6;
      TextView textView6 = ViewBindings.findChildViewById(rootView, id);
      if (textView6 == null) {
        break missingId;
      }

      id = R.id.tvForgotPassword;
      TextView tvForgotPassword = ViewBindings.findChildViewById(rootView, id);
      if (tvForgotPassword == null) {
        break missingId;
      }

      id = R.id.tv_or;
      TextView tvOr = ViewBindings.findChildViewById(rootView, id);
      if (tvOr == null) {
        break missingId;
      }

      id = R.id.tv_privacy;
      TextView tvPrivacy = ViewBindings.findChildViewById(rootView, id);
      if (tvPrivacy == null) {
        break missingId;
      }

      id = R.id.tv_terms;
      TextView tvTerms = ViewBindings.findChildViewById(rootView, id);
      if (tvTerms == null) {
        break missingId;
      }

      return new ActivityGoogleLoginBinding((ConstraintLayout) rootView, btnSignIn, btnSignUp,
          checkboxTerms, constraintLayout2, etNumber, etPassword, hr, ivGooglelogin, textView12,
          textView13, textView2, textView3, textView4, textView5, textView6, tvForgotPassword, tvOr,
          tvPrivacy, tvTerms);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
