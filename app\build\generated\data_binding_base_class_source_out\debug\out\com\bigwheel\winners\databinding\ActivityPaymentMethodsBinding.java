// Generated by view binder compiler. Do not edit!
package com.bigwheel.winners.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bigwheel.winners.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityPaymentMethodsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout buttonPay;

  @NonNull
  public final TextView buyPriceAmout;

  @NonNull
  public final RadioButton gpayRadio;

  @NonNull
  public final RadioButton paytmRadio;

  @NonNull
  public final RadioButton phonepeRadio;

  @NonNull
  public final RadioGroup radioAppChoice;

  @NonNull
  public final TextView termstxtpay;

  @NonNull
  public final TextView tutorial;

  @NonNull
  public final TextView txtContactus;

  private ActivityPaymentMethodsBinding(@NonNull LinearLayout rootView,
      @NonNull LinearLayout buttonPay, @NonNull TextView buyPriceAmout,
      @NonNull RadioButton gpayRadio, @NonNull RadioButton paytmRadio,
      @NonNull RadioButton phonepeRadio, @NonNull RadioGroup radioAppChoice,
      @NonNull TextView termstxtpay, @NonNull TextView tutorial, @NonNull TextView txtContactus) {
    this.rootView = rootView;
    this.buttonPay = buttonPay;
    this.buyPriceAmout = buyPriceAmout;
    this.gpayRadio = gpayRadio;
    this.paytmRadio = paytmRadio;
    this.phonepeRadio = phonepeRadio;
    this.radioAppChoice = radioAppChoice;
    this.termstxtpay = termstxtpay;
    this.tutorial = tutorial;
    this.txtContactus = txtContactus;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityPaymentMethodsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityPaymentMethodsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_payment_methods, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityPaymentMethodsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_pay;
      LinearLayout buttonPay = ViewBindings.findChildViewById(rootView, id);
      if (buttonPay == null) {
        break missingId;
      }

      id = R.id.buy_price_amout;
      TextView buyPriceAmout = ViewBindings.findChildViewById(rootView, id);
      if (buyPriceAmout == null) {
        break missingId;
      }

      id = R.id.gpay_radio;
      RadioButton gpayRadio = ViewBindings.findChildViewById(rootView, id);
      if (gpayRadio == null) {
        break missingId;
      }

      id = R.id.paytm_radio;
      RadioButton paytmRadio = ViewBindings.findChildViewById(rootView, id);
      if (paytmRadio == null) {
        break missingId;
      }

      id = R.id.phonepe_radio;
      RadioButton phonepeRadio = ViewBindings.findChildViewById(rootView, id);
      if (phonepeRadio == null) {
        break missingId;
      }

      id = R.id.radioAppChoice;
      RadioGroup radioAppChoice = ViewBindings.findChildViewById(rootView, id);
      if (radioAppChoice == null) {
        break missingId;
      }

      id = R.id.termstxtpay;
      TextView termstxtpay = ViewBindings.findChildViewById(rootView, id);
      if (termstxtpay == null) {
        break missingId;
      }

      id = R.id.tutorial;
      TextView tutorial = ViewBindings.findChildViewById(rootView, id);
      if (tutorial == null) {
        break missingId;
      }

      id = R.id.txt_contactus;
      TextView txtContactus = ViewBindings.findChildViewById(rootView, id);
      if (txtContactus == null) {
        break missingId;
      }

      return new ActivityPaymentMethodsBinding((LinearLayout) rootView, buttonPay, buyPriceAmout,
          gpayRadio, paytmRadio, phonepeRadio, radioAppChoice, termstxtpay, tutorial, txtContactus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
