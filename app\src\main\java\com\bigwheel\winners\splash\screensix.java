package com.bigwheel.winners.splash;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;

import androidx.appcompat.app.AppCompatActivity;

import com.bigwheel.winners.R;

//import com.hipro.spin.win.R;

public class screensix extends AppCompatActivity {

    Button agreebtn;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_screensix);
        getSupportActionBar().hide();
        agreebtn = findViewById(R.id.agreebtn);

        agreebtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                Intent i=new Intent(privcypol.this,malefemale.class);
//                startActivity(i);

                Intent i=new Intent(screensix.this, screenfour.class);
                startActivity(i);
                finish();
            }


        });
    }
}