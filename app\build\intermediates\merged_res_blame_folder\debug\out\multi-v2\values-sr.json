{"logs": [{"outputFile": "D:\\android project\\new big 22-06\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-sr\\values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f556f30d56993798d7ef134459fae338\\transformed\\core-1.8.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "112", "startColumns": "4", "startOffsets": "10177", "endColumns": "100", "endOffsets": "10273"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\02436aca31e270f81bb7f76ee8fb72ca\\transformed\\jetified-play-services-base-18.0.1\\res\\values-sr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,447,569,675,825,948,1056,1154,1299,1402,1558,1681,1826,1964,2028,2089", "endColumns": "101,151,121,105,149,122,107,97,144,102,155,122,144,137,63,60,75", "endOffsets": "294,446,568,674,824,947,1055,1153,1298,1401,1557,1680,1825,1963,2027,2088,2164"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3440,3546,3702,3828,3938,4092,4219,4331,4563,4712,4819,4979,5106,5255,5397,5465,5530", "endColumns": "105,155,125,109,153,126,111,101,148,106,159,126,148,141,67,64,79", "endOffsets": "3541,3697,3823,3933,4087,4214,4326,4428,4707,4814,4974,5101,5250,5392,5460,5525,5605"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84e8a3931c74ff232fa18e9c8d1ffe2\\transformed\\jetified-play-services-basement-18.1.0\\res\\values-sr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4433", "endColumns": "129", "endOffsets": "4558"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8849a6fbe2ebfe5f57a82d8370a3dd43\\transformed\\jetified-firebase-messaging-21.0.1\\res\\values-sr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "207", "endColumns": "99", "endOffsets": "306"}, "to": {"startLines": "62", "startColumns": "4", "startOffsets": "6190", "endColumns": "103", "endOffsets": "6289"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f2636cb968702bde5e4e1d445b2113c3\\transformed\\browser-1.3.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,266,379", "endColumns": "110,99,112,97", "endOffsets": "161,261,374,472"}, "to": {"startLines": "56,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "5610,5879,5979,6092", "endColumns": "110,99,112,97", "endOffsets": "5716,5974,6087,6185"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\929536aae40a590fbb05df20b2f1b3b8\\transformed\\material-1.6.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,276,356,450,581,662,728,820,888,951,1054,1120,1176,1247,1307,1361,1473,1530,1591,1645,1721,1846,1932,2015,2123,2204,2287,2375,2442,2508,2582,2660,2749,2824,2900,2975,3046,3136,3209,3301,3397,3469,3545,3641,3694,3761,3848,3935,3997,4061,4124,4229,4333,4429,4536", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,79,93,130,80,65,91,67,62,102,65,55,70,59,53,111,56,60,53,75,124,85,82,107,80,82,87,66,65,73,77,88,74,75,74,70,89,72,91,95,71,75,95,52,66,86,86,61,63,62,104,103,95,106,79", "endOffsets": "271,351,445,576,657,723,815,883,946,1049,1115,1171,1242,1302,1356,1468,1525,1586,1640,1716,1841,1927,2010,2118,2199,2282,2370,2437,2503,2577,2655,2744,2819,2895,2970,3041,3131,3204,3296,3392,3464,3540,3636,3689,3756,3843,3930,3992,4056,4119,4224,4328,4424,4531,4611"}, "to": {"startLines": "2,34,35,36,37,57,58,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3054,3134,3228,3359,5721,5787,6294,6362,6425,6528,6594,6650,6721,6781,6835,6947,7004,7065,7119,7195,7320,7406,7489,7597,7678,7761,7849,7916,7982,8056,8134,8223,8298,8374,8449,8520,8610,8683,8775,8871,8943,9019,9115,9168,9235,9322,9409,9471,9535,9598,9703,9807,9903,10010", "endLines": "6,34,35,36,37,57,58,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "endColumns": "12,79,93,130,80,65,91,67,62,102,65,55,70,59,53,111,56,60,53,75,124,85,82,107,80,82,87,66,65,73,77,88,74,75,74,70,89,72,91,95,71,75,95,52,66,86,86,61,63,62,104,103,95,106,79", "endOffsets": "321,3129,3223,3354,3435,5782,5874,6357,6420,6523,6589,6645,6716,6776,6830,6942,6999,7060,7114,7190,7315,7401,7484,7592,7673,7756,7844,7911,7977,8051,8129,8218,8293,8369,8444,8515,8605,8678,8770,8866,8938,9014,9110,9163,9230,9317,9404,9466,9530,9593,9698,9802,9898,10005,10085"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5a6f99c51ca9894dfbe548454cc2e836\\transformed\\appcompat-1.5.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "326,433,534,640,726,830,952,1036,1117,1208,1301,1396,1490,1590,1683,1778,1883,1974,2065,2151,2256,2362,2465,2571,2680,2787,2957,10090", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "428,529,635,721,825,947,1031,1112,1203,1296,1391,1485,1585,1678,1773,1878,1969,2060,2146,2251,2357,2460,2566,2675,2782,2952,3049,10172"}}]}]}