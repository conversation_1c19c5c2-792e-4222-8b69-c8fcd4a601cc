package com.bigwheel.winners.pay;

import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;

import com.airbnb.lottie.LottieAnimationView;
import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;

import com.bigwheel.winners.MainActivity;
import com.bigwheel.winners.R;

import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import cn.pedant.SweetAlert.SweetAlertDialog;

public class PaymentResultPayuMoney extends AppCompatActivity {

    String data;
    LottieAnimationView animview;
    TextView t, datetxt, infotxt, tranidtxt, tranamutxt, traemail;
    Button okbtn;

    SharedPreferences pref;
    SharedPreferences.Editor editor;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_payment_result_payu_money);
        t = findViewById(R.id.finaldata);
        datetxt = findViewById(R.id.date);
        infotxt = findViewById(R.id.textinfo);
        tranidtxt = findViewById(R.id.tranID);
        tranamutxt = findViewById(R.id.tranAmu);
        traemail = findViewById(R.id.tranemail);
        okbtn = findViewById(R.id.okbtn);
        animview = findViewById(R.id.animationView);
        getSupportActionBar().hide();
        pref = getSharedPreferences("mypref", MODE_PRIVATE);
        editor = pref.edit();
        okbtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                startActivity(new Intent(PaymentResultPayuMoney.this, MainActivity.class));
                finish();
            }
        });

        data = getIntent().getStringExtra("data");

        Toast.makeText(this, "da="+data, Toast.LENGTH_SHORT).show();


        String currentDate = new SimpleDateFormat("EEE, d MMM yyyy HH:mm:ss", Locale.getDefault()).format(new Date());
        datetxt.setText(currentDate);

        if (data == null)
            return;

        if (data.equals("fail")) {
            t.setText("Payment Cancelled!");
            t.setTextColor(Color.RED);
            animview.setAnimation(R.raw.fail);
            infotxt.setText("Oops! Something went wrong\nPlease try again!");
            tranidtxt.setText("TXN"+System.currentTimeMillis());
            tranamutxt.setText(pref.getString("tranAmu","99.00"));
            traemail.setText(pref.getString("uemail", "<EMAIL>"));
            okbtn.setBackgroundColor(Color.RED);
        } else {


            try {
                JSONObject jobj = new JSONObject(data);
                Log.d("myjosn=",jobj.toString());
                String myyy = jobj.getString("status");
                String ot = jobj.getString("unmappedstatus");
                tranidtxt.setText(jobj.getString("txnid"));
                tranamutxt.setText(jobj.getString("amount"));
                traemail.setText(jobj.getString("email"));



                if (ot.equalsIgnoreCase("captured") && myyy.equalsIgnoreCase("success")) {
                    t.setText("Payment Success!");
                    t.setTextColor(Color.GREEN);
                    Log.d("jb==","fghjm");
                    animview.setAnimation(R.raw.sucesso);
                    infotxt.setText("Great! Now you can Earn More\\n Good Luck!");
                    okbtn.setBackgroundColor(Color.GREEN);
                } else {
                    t.setText("Fail");
                    t.setTextColor(Color.RED);
                    animview.setAnimation(R.raw.fail);
                    infotxt.setText("Oops! Something went wrong\nPlease try again!");
                    okbtn.setBackgroundColor(Color.RED);
                }
                AddDataToDatabase(jobj.getString("txnid"),jobj.getString("amount"),jobj.getString("status"));

            } catch (JSONException e) {
                e.printStackTrace();
                Log.d("jb==",e.getMessage());
            }

        }





    }

    @Override
    public void onBackPressed() {

    }

    public void AddDataToDatabase(String tid, String Amu, String status) {

        SweetAlertDialog pDialog;
        SweetAlertDialog sweetAlertDialog = new SweetAlertDialog(PaymentResultPayuMoney.this, 5);
        pDialog = sweetAlertDialog;
        sweetAlertDialog.getProgressHelper().setBarColor(Color.parseColor("#FFB800"));
        pDialog.setTitleText("Please wait...");
        pDialog.setCancelable(false);
        pDialog.show();


        RequestQueue queue = Volley.newRequestQueue(this);
        StringRequest stringRequest = new StringRequest(Request.Method.POST, pref.getString("baseurl", "") + "payment.php",
                new Response.Listener<String>() {
                    @Override
                    public void onResponse(String response) {
                        pDialog.dismiss();


                        if (response.equals("0")) {
                            new AlertDialog.Builder(PaymentResultPayuMoney.this)
                                    .setTitle("Error!")
                                    .setCancelable(false)
                                    .setMessage("Something went wrong!")
                                    .setPositiveButton("Retry", new DialogInterface.OnClickListener() {
                                        public void onClick(DialogInterface dialog, int which) {
                                            // Continue with delete operation
                                            Intent intent = new Intent(PaymentResultPayuMoney.this, PaymentResultPayuMoney.class);
                                            startActivity(intent);
                                            finish();
                                        }
                                    }).setNegativeButton("Exit", new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialogInterface, int i) {
                                    finish();
                                }
                            }).setIcon(android.R.drawable.ic_dialog_alert)
                                    .show();
                        }
                    }
                }, new Response.ErrorListener() {
            @Override
            public void onErrorResponse(VolleyError error) {
                pDialog.dismiss();
                new AlertDialog.Builder(PaymentResultPayuMoney.this)
                        .setTitle("Warning!")
                        .setMessage("Check Your Internet Connection")
                        .setCancelable(false)
                        .setPositiveButton("Retry", new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int which) {
                                // Continue with delete operation
                                Intent intent = new Intent(PaymentResultPayuMoney.this, PaymentResultPayuMoney.class);
                                startActivity(intent);
                                finish();
                            }
                        })

                        // A null listener allows the button to dismiss the dialog and take no further action.
                        .setNegativeButton("Exit", new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialogInterface, int i) {
                                finish();
                            }
                        })
                        .setIcon(android.R.drawable.ic_dialog_alert)
                        .show();
            }
        }) {
            @Override
            protected Map<String, String> getParams() {
                Map<String, String> params = new HashMap<String, String>();
                params.put("email", pref.getString("uemail", "<EMAIL>"));
                params.put("tid", tid);
                params.put("amu", Amu);
                params.put("status", status);
                return params;
            }
        };
        queue.add(stringRequest);

    }
}