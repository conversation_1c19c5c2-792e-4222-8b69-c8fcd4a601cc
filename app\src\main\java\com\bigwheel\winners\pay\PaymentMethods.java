package com.bigwheel.winners.pay;

import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.bigwheel.winners.MainActivity;
import com.bigwheel.winners.R;

//import com.hipro.spin.win.MainActivity;
//import com.hipro.spin.win.R;

public class PaymentMethods extends AppCompatActivity {

    TextView termstxt,buy_price_amout_txt,txt_contactus,tutorialtxt;
    LinearLayout button_payNow;
    RadioButton upi_radio,wallet_radio;
    String amount="50";
    SharedPreferences pref;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_payment_methods);
        getSupportActionBar().hide();
        termstxt = findViewById(R.id.termstxtpay);
        button_payNow = findViewById(R.id.button_pay);
        upi_radio = findViewById(R.id.upi_radio);
        wallet_radio = findViewById(R.id.payumoney_radio);
        buy_price_amout_txt = findViewById(R.id.buy_price_amout);
        txt_contactus = findViewById(R.id.txt_contactus);
        tutorialtxt=findViewById(R.id.tutorial);
        tutorialtxt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(PaymentMethods.this,PaymentTutorial.class);

                startActivity(intent);

            }
        });

        amount = getIntent().getStringExtra("amount");
        buy_price_amout_txt.setText("₹"+amount+" INR");
        pref=getSharedPreferences("mypref",MODE_PRIVATE);

        termstxt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                try {
                    Intent myIntent = new Intent(Intent.ACTION_VIEW, Uri.parse("https://sites.google.com/view/spintowin-term-condition/home"));
                    startActivity(myIntent);
                } catch (ActivityNotFoundException e) {
                    Toast.makeText(PaymentMethods.this, "No application can handle this request."
                            + " Please install a web browser", Toast.LENGTH_LONG).show();
                    e.printStackTrace();
                }
            }
        });
        txt_contactus.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(Intent.ACTION_SENDTO); // it's not ACTION_SEND
                intent.putExtra(Intent.EXTRA_SUBJECT, "Pdev Technology");
                intent.putExtra(Intent.EXTRA_TEXT, "Hello, We are here to help you, Please inform us what can we do for you?\n");
                intent.setData(Uri.parse("mailto:<EMAIL>")); // or just "mailto:" for blank
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK); // this will make such that when user returns to your app, your app is displayed, instead of the email app.
                startActivity(intent);
            }
        });
        button_payNow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                  //  int i = Integer.parseInt(pref.getString("payu","payuno"));
                if(upi_radio.isChecked())
                {
                   // Toast.makeText(PaymentMethods.this, "No UPI app found, please install one to continue", Toast.LENGTH_SHORT).show();
                    Intent intent = new Intent(PaymentMethods.this, PaymentViaUPI.class);
                    intent.putExtra("amount", amount);
                    startActivity(intent);
                    finish();

                }else
                {
//                    if(i == 1)
//                    {
//                        Intent intent = new Intent(PaymentMethods.this, MainActivitys.class);
//                        intent.putExtra("amount", amount);
//                        startActivity(intent);
//                        finish();
//                    }
//                    else
                    {
                        Intent intent = new Intent(PaymentMethods.this, PaymentViaUPI.class);
                        intent.putExtra("amount", amount);
                        startActivity(intent);
                        finish();
                    }
                }

            }
        });

    }


    @Override
    public void onBackPressed() {
        super.onBackPressed();
        startActivity(new Intent(PaymentMethods.this, MainActivity.class));
        finish();
    }


}