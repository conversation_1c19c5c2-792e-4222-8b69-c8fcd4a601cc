package com.bigwheel.winners.pay;

import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.bigwheel.winners.MainActivity;
import com.bigwheel.winners.R;

import java.util.ArrayList;
import java.util.UUID;

//import com.hipro.spin.win.MainActivity;
//import com.hipro.spin.win.R;

public class PaymentMethods extends AppCompatActivity {

    TextView termstxt,buy_price_amout_txt,txt_contactus,tutorialtxt;
    LinearLayout button_payNow;
    RadioButton gpay_radio, phonepe_radio, paytm_radio;
    String amount="50";
    SharedPreferences pref;
    private static final int UPI_PAYMENT_REQUEST_CODE = 124;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_payment_methods);
        getSupportActionBar().hide();
        termstxt = findViewById(R.id.termstxtpay);
        button_payNow = findViewById(R.id.button_pay);
        gpay_radio = findViewById(R.id.gpay_radio);
        phonepe_radio = findViewById(R.id.phonepe_radio);
        paytm_radio = findViewById(R.id.paytm_radio);
        buy_price_amout_txt = findViewById(R.id.buy_price_amout);
        txt_contactus = findViewById(R.id.txt_contactus);
        tutorialtxt=findViewById(R.id.tutorial);
        tutorialtxt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(PaymentMethods.this,PaymentTutorial.class);

                startActivity(intent);

            }
        });

        amount = getIntent().getStringExtra("amount");
        buy_price_amout_txt.setText("₹"+amount+" INR");
        pref=getSharedPreferences("mypref",MODE_PRIVATE);

        termstxt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                try {
                    Intent myIntent = new Intent(Intent.ACTION_VIEW, Uri.parse("https://sites.google.com/view/spintowin-term-condition/home"));
                    startActivity(myIntent);
                } catch (ActivityNotFoundException e) {
                    Toast.makeText(PaymentMethods.this, "No application can handle this request."
                            + " Please install a web browser", Toast.LENGTH_LONG).show();
                    e.printStackTrace();
                }
            }
        });
        txt_contactus.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(Intent.ACTION_SENDTO); // it's not ACTION_SEND
                intent.putExtra(Intent.EXTRA_SUBJECT, "Pdev Technology");
                intent.putExtra(Intent.EXTRA_TEXT, "Hello, We are here to help you, Please inform us what can we do for you?\n");
                intent.setData(Uri.parse("mailto:<EMAIL>")); // or just "mailto:" for blank
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK); // this will make such that when user returns to your app, your app is displayed, instead of the email app.
                startActivity(intent);
            }
        });
        button_payNow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // Check internet connection
                if (!isConnectionAvailable(PaymentMethods.this)) {
                    Toast.makeText(PaymentMethods.this, "No internet connection. Please check and try again.", Toast.LENGTH_LONG).show();
                    return;
                }

                // Determine which UPI app is selected and initiate direct payment
                String selectedApp = "";
                if (gpay_radio.isChecked()) {
                    selectedApp = "gpay";
                } else if (phonepe_radio.isChecked()) {
                    selectedApp = "phonepe";
                } else if (paytm_radio.isChecked()) {
                    selectedApp = "paytm";
                } else {
                    Toast.makeText(PaymentMethods.this, "Please select a payment method", Toast.LENGTH_SHORT).show();
                    return;
                }

                // Directly initiate UPI payment without opening another screen
                initiateDirectUPIPayment(selectedApp);
            }
        });

    }


    private void initiateDirectUPIPayment(String paymentApp) {
        String upiId = "";
        String packageName = "";

        // Get specific UPI ID and package for each app
        switch (paymentApp) {
            case "gpay":
                upiId = pref.getString("gpay_upi", "");
                if (upiId.isEmpty()) {
                    upiId = pref.getString("upiid", ""); // fallback
                }
                packageName = "com.google.android.apps.nbu.paisa.user";
                break;

            case "phonepe":
                upiId = pref.getString("phonepe_upi", "");
                if (upiId.isEmpty()) {
                    upiId = pref.getString("upiid", ""); // fallback
                }
                packageName = "com.phonepe.app";
                break;

            case "paytm":
                upiId = pref.getString("paytm_upi", "");
                if (upiId.isEmpty()) {
                    upiId = pref.getString("upiid", ""); // fallback
                }
                packageName = "net.one97.paytm";
                break;

            default:
                upiId = pref.getString("upiid", "");
                break;
        }

        if (upiId.isEmpty()) {
            Toast.makeText(this, "UPI ID not configured for " + getAppName(paymentApp), Toast.LENGTH_LONG).show();
            return;
        }

        // Create UPI payment URI with specific details
        String upiUri = "upi://pay?ver=01&mode=19" +
                "&pa=" + upiId +
                "&pn=" + pref.getString("uname", "Spin Win") +
                "&tr=" + UUID.randomUUID().toString().replace("-", "") +
                "&cu=INR" +
                "&mc=" +
                "&qrMedium=04" +
                "&tn=Payment for Spin Win Game" +
                "&am=" + amount;

        Intent upiPayIntent = new Intent(Intent.ACTION_VIEW);
        upiPayIntent.setData(Uri.parse(upiUri));
        upiPayIntent.setPackage(packageName); // Set specific package

        Intent chooser = Intent.createChooser(upiPayIntent, "Pay with " + getAppName(paymentApp));

        if (chooser.resolveActivity(getPackageManager()) != null) {
            startActivityForResult(chooser, UPI_PAYMENT_REQUEST_CODE);
        } else {
            Toast.makeText(this, getAppName(paymentApp) + " is not installed", Toast.LENGTH_SHORT).show();
        }
    }

    private String getAppName(String paymentApp) {
        switch (paymentApp) {
            case "gpay": return "Google Pay";
            case "phonepe": return "PhonePe";
            case "paytm": return "Paytm";
            default: return "UPI App";
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == UPI_PAYMENT_REQUEST_CODE) {
            if ((RESULT_OK == resultCode) || (resultCode == 11)) {
                if (data != null) {
                    String trxt = data.getStringExtra("response");
                    Log.d("UPI", "onActivityResult: " + trxt);
                    ArrayList<String> dataList = new ArrayList<>();
                    dataList.add(trxt);
                    upiPaymentDataOperation(dataList);
                } else {
                    Log.d("UPI", "onActivityResult: " + "Return data is null");
                    ArrayList<String> dataList = new ArrayList<>();
                    dataList.add("nothing");
                    upiPaymentDataOperation(dataList);
                }
            } else {
                Log.d("UPI", "onActivityResult: " + "Return data is null"); //when user simply back without payment
                ArrayList<String> dataList = new ArrayList<>();
                dataList.add("nothing");
                upiPaymentDataOperation(dataList);
            }
        }
    }

    private void upiPaymentDataOperation(ArrayList<String> data) {
        if (isConnectionAvailable(PaymentMethods.this)) {
            String str = data.get(0);
            Log.d("UPIPAY", "upiPaymentDataOperation: " + str);
            String paymentCancel = "";
            if (str == null) str = "discard";
            String status = "";
            String approvalRefNo = "";
            String response[] = str.split("&");
            for (int i = 0; i < response.length; i++) {
                String equalStr[] = response[i].split("=");
                if (equalStr.length >= 2) {
                    if (equalStr[0].toLowerCase().equals("Status".toLowerCase())) {
                        status = equalStr[1].toLowerCase();
                    } else if (equalStr[0].toLowerCase().equals("ApprovalRefNo".toLowerCase()) || equalStr[0].toLowerCase().equals("txnRef".toLowerCase())) {
                        approvalRefNo = equalStr[1];
                    }
                } else {
                    paymentCancel = "Payment cancelled by user.";
                }
            }

            if (status.equals("success")) {
                Toast.makeText(PaymentMethods.this, "Transaction successful.", Toast.LENGTH_SHORT).show();
                Intent intent = new Intent(PaymentMethods.this, PaymentResult.class);
                intent.putExtra("status", "success");
                intent.putExtra("myamount", amount);
                startActivity(intent);
                finish();
            } else if ("Payment cancelled by user.".equals(paymentCancel)) {
                Toast.makeText(PaymentMethods.this, "Payment cancelled by user.", Toast.LENGTH_SHORT).show();
                Intent intent = new Intent(PaymentMethods.this, PaymentResult.class);
                intent.putExtra("status", "cancel");
                intent.putExtra("myamount", amount);
                startActivity(intent);
                finish();
            } else {
                Toast.makeText(PaymentMethods.this, "Transaction failed. Please try again", Toast.LENGTH_SHORT).show();
                Intent intent = new Intent(PaymentMethods.this, PaymentResult.class);
                intent.putExtra("status", "fail");
                intent.putExtra("myamount", amount);
                startActivity(intent);
                finish();
            }
        } else {
            Toast.makeText(PaymentMethods.this, "Internet connection is not available. Please check and try again", Toast.LENGTH_SHORT).show();
        }
    }

    public static boolean isConnectionAvailable(PaymentMethods context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo netInfo = connectivityManager.getActiveNetworkInfo();
            if (netInfo != null && netInfo.isConnected()
                    && netInfo.isConnectedOrConnecting()
                    && netInfo.isAvailable()) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        startActivity(new Intent(PaymentMethods.this, MainActivity.class));
        finish();
    }
}