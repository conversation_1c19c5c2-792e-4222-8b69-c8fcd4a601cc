<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:background="@color/themeColor"
    android:padding="20dp"
    tools:context=".pay.PaymentViaUPI">

    <!-- Loading Animation -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="💳"
        android:textSize="80sp"
        android:layout_marginBottom="20dp" />

    <!-- Title -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Secure Payment"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:layout_marginBottom="10dp" />

    <!-- Subtitle -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Initializing payment gateway..."
        android:textSize="16sp"
        android:textColor="@color/white"
        android:alpha="0.8"
        android:layout_marginBottom="30dp" />

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyle"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:indeterminateTint="@color/premium_gold" />

    <!-- Info Text -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Please wait while we prepare your payment options..."
        android:textSize="14sp"
        android:textColor="@color/white"
        android:alpha="0.7"
        android:gravity="center"
        android:layout_marginTop="20dp" />

</LinearLayout>