// Generated by view binder compiler. Do not edit!
package com.bigwheel.winners.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bigwheel.winners.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityScreenthreeBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button nextbtn;

  @NonNull
  public final RadioButton radioButton11;

  @NonNull
  public final RadioButton radioButton12;

  @NonNull
  public final RadioButton radioButton13;

  @NonNull
  public final RadioButton radioButton14;

  @NonNull
  public final TextView textView2;

  private ActivityScreenthreeBinding(@NonNull LinearLayout rootView, @NonNull Button nextbtn,
      @NonNull RadioButton radioButton11, @NonNull RadioButton radioButton12,
      @NonNull RadioButton radioButton13, @NonNull RadioButton radioButton14,
      @NonNull TextView textView2) {
    this.rootView = rootView;
    this.nextbtn = nextbtn;
    this.radioButton11 = radioButton11;
    this.radioButton12 = radioButton12;
    this.radioButton13 = radioButton13;
    this.radioButton14 = radioButton14;
    this.textView2 = textView2;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityScreenthreeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityScreenthreeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_screenthree, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityScreenthreeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.nextbtn;
      Button nextbtn = ViewBindings.findChildViewById(rootView, id);
      if (nextbtn == null) {
        break missingId;
      }

      id = R.id.radioButton11;
      RadioButton radioButton11 = ViewBindings.findChildViewById(rootView, id);
      if (radioButton11 == null) {
        break missingId;
      }

      id = R.id.radioButton12;
      RadioButton radioButton12 = ViewBindings.findChildViewById(rootView, id);
      if (radioButton12 == null) {
        break missingId;
      }

      id = R.id.radioButton13;
      RadioButton radioButton13 = ViewBindings.findChildViewById(rootView, id);
      if (radioButton13 == null) {
        break missingId;
      }

      id = R.id.radioButton14;
      RadioButton radioButton14 = ViewBindings.findChildViewById(rootView, id);
      if (radioButton14 == null) {
        break missingId;
      }

      id = R.id.textView2;
      TextView textView2 = ViewBindings.findChildViewById(rootView, id);
      if (textView2 == null) {
        break missingId;
      }

      return new ActivityScreenthreeBinding((LinearLayout) rootView, nextbtn, radioButton11,
          radioButton12, radioButton13, radioButton14, textView2);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
