// Generated by view binder compiler. Do not edit!
package com.bigwheel.winners.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bigwheel.winners.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ReviewOrderRowLayoutBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final EditText etReviewOrderKey;

  @NonNull
  public final EditText etReviewOrderValue;

  @NonNull
  public final ImageView ivDeleteOrderItem;

  private ReviewOrderRowLayoutBinding(@NonNull RelativeLayout rootView,
      @NonNull EditText etReviewOrderKey, @NonNull EditText etReviewOrderValue,
      @NonNull ImageView ivDeleteOrderItem) {
    this.rootView = rootView;
    this.etReviewOrderKey = etReviewOrderKey;
    this.etReviewOrderValue = etReviewOrderValue;
    this.ivDeleteOrderItem = ivDeleteOrderItem;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ReviewOrderRowLayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ReviewOrderRowLayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.review_order_row_layout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ReviewOrderRowLayoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.etReviewOrderKey;
      EditText etReviewOrderKey = ViewBindings.findChildViewById(rootView, id);
      if (etReviewOrderKey == null) {
        break missingId;
      }

      id = R.id.etReviewOrderValue;
      EditText etReviewOrderValue = ViewBindings.findChildViewById(rootView, id);
      if (etReviewOrderValue == null) {
        break missingId;
      }

      id = R.id.ivDeleteOrderItem;
      ImageView ivDeleteOrderItem = ViewBindings.findChildViewById(rootView, id);
      if (ivDeleteOrderItem == null) {
        break missingId;
      }

      return new ReviewOrderRowLayoutBinding((RelativeLayout) rootView, etReviewOrderKey,
          etReviewOrderValue, ivDeleteOrderItem);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
