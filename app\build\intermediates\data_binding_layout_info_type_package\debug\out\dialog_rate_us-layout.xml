<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_rate_us" modulePackage="com.bigwheel.winners" filePath="app\src\main\res\layout\dialog_rate_us.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/dialog_rate_us_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="139" endOffset="35"/></Target><Target id="@+id/winner" view="com.airbnb.lottie.LottieAnimationView"><Expressions/><location startLine="21" startOffset="12" endLine="28" endOffset="87"/></Target><Target id="@+id/loss" view="com.airbnb.lottie.LottieAnimationView"><Expressions/><location startLine="30" startOffset="12" endLine="36" endOffset="92"/></Target><Target id="@+id/tvTitle" view="TextView"><Expressions/><location startLine="47" startOffset="12" endLine="57" endOffset="42"/></Target><Target id="@+id/winamount" view="TextView"><Expressions/><location startLine="59" startOffset="12" endLine="67" endOffset="42"/></Target><Target id="@+id/tvDescription" view="TextView"><Expressions/><location startLine="73" startOffset="8" endLine="83" endOffset="37"/></Target><Target id="@+id/btnLater" view="LinearLayout"><Expressions/><location startLine="85" startOffset="8" endLine="134" endOffset="22"/></Target><Target id="@+id/btnRateNow" view="LinearLayout"><Expressions/><location startLine="112" startOffset="12" endLine="131" endOffset="26"/></Target></Targets></Layout>