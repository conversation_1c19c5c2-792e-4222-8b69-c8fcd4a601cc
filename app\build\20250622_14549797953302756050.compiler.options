"-Xallow-no-source-files" "-classpath" "D:\\android project\\new big 22-06\\app\\build\\intermediates\\compile_and_runtime_not_namespaced_r_class_jar\\debug\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5226b4631510ff32bf2fccbd96d0d3a8\\transformed\\jetified-viewbinding-7.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b9916a13f12c0c461e16abc34fcf27a6\\transformed\\material-1.6.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2a617fa54d3707a7224ebf50eb013af6\\transformed\\jetified-OneSignal-4.8.12-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7f2d47462c4c610c8f03bd9c420efce1\\transformed\\appcompat-1.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fc6f9d0d18a5b79ebfe18e2ce4641699\\transformed\\jetified-firebase-database-20.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d350fa885d57542cffd8c02d9f57fb4\\transformed\\jetified-picasso-2.71828-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c3a57692aa0ff931dd43daa443ca6e17\\transformed\\jetified-play-services-auth-20.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\abc9f7e1907397d2c30348e8e3e61757\\transformed\\jetified-viewpager2-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\573549bc27009975d58b5ab6a78bb936\\transformed\\legacy-support-v4-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\be352d0639bff21848ad4ff50d503123\\transformed\\jetified-firebase-appcheck-interop-16.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4b17dea56e33b6206e47c94681e99eec\\transformed\\jetified-firebase-database-collection-18.0.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\71ba885353b78f71c86dd76dfc0c02ba\\transformed\\jetified-play-services-auth-api-phone-18.0.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\00eda1ecd684532d5087669395a7d114\\transformed\\jetified-play-services-auth-base-18.0.4-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\845a4f52d0efbfbc1dc088eeac358df6\\transformed\\jetified-play-services-fido-19.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\890d07697bf0038790fd2da83dc09219\\transformed\\jetified-firebase-messaging-21.0.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3396b207c511bbcf96f7784ed4fc9083\\transformed\\jetified-firebase-iid-21.0.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\95d9b3144188c249937e010ea427ebb9\\transformed\\jetified-firebase-iid-interop-17.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0621e121df38e3f430c66846ed3f3811\\transformed\\jetified-play-services-base-18.0.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ba96492eaeafd9400a8355745148ce70\\transformed\\jetified-firebase-analytics-18.0.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ff41857b4ee3aa9bea037b914349e3fd\\transformed\\jetified-play-services-measurement-18.0.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7c55d551f9bf1489f891cf3ff0dc74f\\transformed\\jetified-play-services-measurement-api-18.0.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e66456cd3b6957c9f729b57f562e735a\\transformed\\jetified-play-services-measurement-sdk-18.0.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8742389e4edf337e4c05bc7e0343f871\\transformed\\jetified-firebase-auth-interop-19.0.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c3c16bf252eadddbdbb7c7d9eb2a373\\transformed\\jetified-firebase-installations-16.3.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc9bb9600f60ad9e308eb735aa036d49\\transformed\\jetified-firebase-datatransport-17.0.10-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7bfdf79339ee55319603782f3ae847d4\\transformed\\jetified-firebase-common-20.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7a18c335e66e9b49674adc8213d6494b\\transformed\\jetified-play-services-cloud-messaging-16.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e33377e681c2b92ddf993c92a3f392de\\transformed\\jetified-firebase-installations-interop-16.0.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1737d5df1c5601ee6ff8102ec914c94a\\transformed\\jetified-play-services-tasks-18.0.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d39a61ac328474b469e197c69c49ccf\\transformed\\jetified-play-services-measurement-impl-18.0.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cab963f0bf13d9c71a951c4ee93c4bdd\\transformed\\jetified-play-services-stats-17.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\586c4bbcd0dd1ffde37cc5664872ac84\\transformed\\jetified-firebase-measurement-connector-18.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\78f512274746e57eb8e5b22648b3261a\\transformed\\jetified-play-services-ads-identifier-17.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1ef00cf262dd74e2c1dc5ca8191614a1\\transformed\\jetified-play-services-measurement-sdk-api-18.0.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f0c47092ded41986932331ebd2edaabd\\transformed\\jetified-play-services-measurement-base-18.0.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9d4500648410c4c758127aee34d249e6\\transformed\\jetified-play-services-basement-18.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7013edf4f3cdc6ddd29b174d48983b08\\transformed\\fragment-1.3.6-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7b6bf4e67b02e111122d2e70cb3d90f9\\transformed\\jetified-activity-1.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e56bd4fb6df2cce6c16239b6ab1be2d6\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6eeac30351161f4240c8a888f4af8594\\transformed\\jetified-core-ktx-1.8.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\951c4a5f233249d3b5cbbd2cb710bb06\\transformed\\cardview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8d9fe94ab7fb98dee71db115e5be568f\\transformed\\jetified-appcompat-resources-1.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\12c92012f30d11558dfbd3224535811f\\transformed\\legacy-support-core-ui-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5d3023114139332a533140bce604bff5\\transformed\\drawerlayout-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bcb76d50f2b4850dd19cbf6c372b35ad\\transformed\\coordinatorlayout-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\26142e205b60d23d371f7de2ea0898e5\\transformed\\dynamicanimation-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0b13bcf24787562433ad8bf4630ba213\\transformed\\recyclerview-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\abde8050332b6c7a4eb755fc3c266e8c\\transformed\\transition-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\74c1ca6025df59c27d33b89ea5a28ad4\\transformed\\vectordrawable-animated-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d824853a41b34d7214e83a1438143810\\transformed\\vectordrawable-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f2ab843896958eac2784ffa2db3a8b43\\transformed\\browser-1.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b914b4da2e64f3384d90d372ed1d9718\\transformed\\legacy-support-core-utils-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3a634df4b8c4ff10ea703bc0988a2420\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a154d7aefd5c2d149425e2eb6a8838e\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4ca1dbfdc52becb8d945f24651f7dcdf\\transformed\\slidingpanelayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\70f195a2806a3d407a732af4b9c9f6d7\\transformed\\customview-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\72ae76bb0c5281eab7c32a7fe01dc3a5\\transformed\\media-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ae52ba5e0faf77aa674b80e157708d7c\\transformed\\swiperefreshlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\15a7272b05a843781dc0d8e03b92e3e1\\transformed\\asynclayoutinflater-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e4b2e70d89d80a84a33bc8971fa7b3fe\\transformed\\core-1.8.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0f5347cff528b37a7aaec77f0de14fe6\\transformed\\cursoradapter-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8237e12fbe08f7abf37b91e17ebcaa3f\\transformed\\jetified-savedstate-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8898fdd9526d1acf0e3bd686675aa621\\transformed\\lifecycle-runtime-2.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1fa200e9e9eef6fe284dcce0727f9429\\transformed\\jetified-firebase-components-17.0.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b436ec8904c87b0a8c90414137e1c9b9\\transformed\\exifinterface-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\90578bf6f6400ed738f9359f44ecb870\\transformed\\lifecycle-viewmodel-2.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6c6004a035039daf7bde7f24c0fbfea5\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd41a212e3bad0cc38e6c6370d641116\\transformed\\work-runtime-2.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e2b5f19fc55da7ff79e4e80232f47033\\transformed\\lifecycle-livedata-2.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cadf8f8cba1b1dd4d752406f8932d118\\transformed\\core-runtime-2.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.1.0\\b3152fc64428c9354344bd89848ecddc09b6f07e\\core-common-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\de71124652ff0d468b22aeb513b1f2b1\\transformed\\lifecycle-livedata-core-2.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.5.0\\1fdb7349701e9cf2f0a69fc10642b6fef6bb3e12\\lifecycle-common-2.5.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\955aeb70bbbd0e3fc9239c550d56e968\\transformed\\jetified-transport-backend-cct-2.3.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e2f0944fc161d84e907021558db41829\\transformed\\jetified-firebase-encoders-json-17.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\160cd7b77dea93d3fd9948623111b133\\transformed\\jetified-firebase-encoders-16.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\852c700bab3df53450b0d290ab8eb6d4\\transformed\\jetified-transport-runtime-2.2.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d81399a380e77ba0a4fd6a399bb36264\\transformed\\jetified-transport-api-2.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f9998c8ba5b6ccc01bd46b8f5674d3ef\\transformed\\interpolator-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1ffb9e3da4151f36be247d0e3a755abf\\transformed\\documentfile-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0b33b58f447c1454410130047149708c\\transformed\\localbroadcastmanager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\af16899c565332c61e9630caf62dca05\\transformed\\print-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.annotation\\annotation\\1.3.0\\21f49f5f9b85fc49de712539f79123119740595\\annotation-1.3.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ab31023df53cab6b523a44a60a6b4d2\\transformed\\jetified-annotation-experimental-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\296056627bf018036432fec7b85fb876\\transformed\\jetified-listenablefuture-1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fa1e67d3c0189ed4e59d3b1b8768428f\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.6.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7f7720ae5f70cb3530d445ae4fcaa556\\transformed\\jetified-kotlinx-coroutines-android-1.6.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bbe67c3d20a9df22e37074ab1f5fac7d\\transformed\\jetified-kotlin-stdlib-jdk8-1.6.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9ca43b7490bcd0203eed13b49ddd1980\\transformed\\jetified-kotlin-stdlib-jdk7-1.6.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0be188bff4fe42f08eb6dc793de12645\\transformed\\jetified-kotlin-stdlib-1.7.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a84d9773690f5bb02e13061e56f3772f\\transformed\\jetified-kotlin-stdlib-common-1.7.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5091394e8a8c9ecd25a86e68d32684cd\\transformed\\jetified-annotations-13.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9b92db1a657184ee856c442291c7261f\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7b55eb1a5818194a76e18bbd39b44430\\transformed\\constraintlayout-2.1.4-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\20921ee29c78210d7c1310119b9ee3ad\\transformed\\jetified-javax.inject-1.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c93104eac3db9fe6429b7af0d855f97b\\transformed\\jetified-error_prone_annotations-2.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7e417e268e9ed9694541c707a53a871d\\transformed\\jetified-firebase-annotations-16.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ba81750490e1d4419a7cded7e6f84317\\transformed\\jetified-library-1.6.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\859e5ccb4eba528928eab9cea6a9fecc\\transformed\\jetified-materialish-progress-1.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4e9ac303e0f01261544bbbf1a60f2112\\transformed\\jetified-lottie-5.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\88c0663017182f8d718764f97cebd81e\\transformed\\jetified-okhttp-3.10.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a46f48dc86197c62bcf51fbf222238b8\\transformed\\jetified-okio-1.17.4.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ed21d513415a1661ce97d9313b2dd867\\transformed\\jetified-volley-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7b8deb0e3634d8f67679e02784bf5605\\transformed\\jetified-circleimageview-3.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5022940267a3b0f6057554b698d2c432\\transformed\\jetified-sdp-android-1.0.6-api.jar;D:\\android project\\new big 22-06\\luckyWheel\\build\\intermediates\\compile_library_classes_jar\\debug\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platforms\\android-32\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\build-tools\\30.0.3\\core-lambda-stubs.jar" "-d" "D:\\android project\\new big 22-06\\app\\build\\tmp\\kotlin-classes\\debug" "-Xjava-source-roots=D:\\android project\\new big 22-06\\app\\src\\main\\java,D:\\android project\\new big 22-06\\app\\build\\generated\\source\\buildConfig\\debug,D:\\android project\\new big 22-06\\app\\build\\generated\\data_binding_base_class_source_out\\debug\\out" "-module-name" "app_debug" "-no-jdk" "-no-stdlib" "-Xcommon-sources=D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\GoogleLogin.java,D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\MainActivity.java,D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\pay\\HashGenerationUtils.kt,D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\pay\\MainActivitys.java,D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\pay\\MainActivitys.kt,D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\pay\\PaymentMethods.java,D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\pay\\PaymentResult.java,D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\pay\\PaymentResultPayuMoney.java,D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\pay\\PaymentTutorial.java,D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\pay\\PaymentViaUPI.java,D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\pay\\ReviewOrderRecyclerViewAdapter.kt,D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\splash\\screenfive.java,D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\splash\\screenfour.java,D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\splash\\screenOne.java,D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\splash\\screensix.java,D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\splash\\screenthree.java,D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\splash\\screentwo.java,D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\splash\\splash.java,D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\SplashScreen.java" "D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\pay\\HashGenerationUtils.kt" "D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\pay\\MainActivitys.kt" "D:\\android project\\new big 22-06\\app\\src\\main\\java\\com\\bigwheel\\winners\\pay\\ReviewOrderRecyclerViewAdapter.kt" "-jvm-target" "1.8"