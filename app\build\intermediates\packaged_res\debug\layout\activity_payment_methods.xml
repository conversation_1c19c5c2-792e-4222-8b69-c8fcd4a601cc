<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical"
    tools:context="pay.PaymentMethods">

    <LinearLayout
        android:layout_marginTop="@dimen/_20sdp"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="0dp"
            android:gravity="center_horizontal"
            android:text="Payment Methods"
            android:textAlignment="center"
            android:textColor="@color/black"
            android:textStyle="bold"
            android:textSize="25sp" />

    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:orientation="vertical">


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Recharge Amount:"
                android:textColor="@color/black"
                android:textSize="15sp"
                android:textStyle="normal" />

            <TextView
                android:id="@+id/buy_price_amout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5sdp"
                android:padding="@dimen/_10sdp"
                android:text="₹199 INR"
                android:textColor="@color/black"
                android:textSize="20dp"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#E1E1E1"
            android:layout_marginTop="@dimen/_20sdp">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="left"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:padding="5dp"
                    android:scaleType="fitCenter"
                    android:src="@drawable/all_upi" />

                <ImageView
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:scaleType="fitCenter"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:src="@drawable/all_wallets" />

            </LinearLayout>

            <RadioGroup
                android:id="@+id/radioAppChoice"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <RadioButton
                    android:id="@+id/upi_radio"
                    android:layout_width="match_parent"
                    android:layout_height="70dp"
                    android:checked="true"
                    android:text="Direct UPI Payout"
                    android:textColor="@color/black"
                    android:textSize="20sp"
                    android:textStyle="bold" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="-20dp"
                    android:text="Paying Via UPI"
                    android:textColor="#818080"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <RadioButton
                    android:id="@+id/payumoney_radio"
                    android:layout_width="match_parent"
                    android:layout_height="70dp"
                    android:layout_marginTop="3dp"
                    android:text="All Wallet"
                    android:textColor="@color/black"
                    android:textSize="20sp"
                    android:textStyle="bold" />


                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30dp"
                    android:layout_marginTop="-20dp"
                    android:text="Paying Via All Wallet"
                    android:textColor="#818080"
                    android:textSize="12sp"
                    android:textStyle="bold" />

            </RadioGroup>

        </LinearLayout>

        <Space
            android:layout_width="match_parent"
            android:layout_height="@dimen/_10sdp" />

        <TextView
            android:id="@+id/tutorial"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Learn how to pay using all wallet option?"
            android:textColor="#0C0075"
            android:textSize="@dimen/_15sdp"
            android:textStyle="bold" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/button_pay"
        android:layout_width="@dimen/_100sdp"
        android:layout_height="@dimen/_35sdp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/_20sdp"
        android:background="#980000">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:text="Pay Now"
            android:textColor="#FFFFFF"
            android:textSize="@dimen/_15sdp"
            android:textStyle="bold" />

    </LinearLayout>


    <TextView
        android:id="@+id/txt_contactus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginBottom="10dp"
        android:layout_marginTop="20dp"
        android:text="If you have any problam about recharging, \n please contact us."
        android:textAlignment="center"
        android:textColor="@color/black"
        android:textSize="12sp" />

    <TextView
        android:id="@+id/termstxtpay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginBottom="10dp"
        android:layout_marginTop="20dp"
        android:text="I have read and accept its terms &amp; conditions"
        android:textAlignment="center"
        android:textColor="#000F97"
        android:textSize="12sp" />
</LinearLayout>
