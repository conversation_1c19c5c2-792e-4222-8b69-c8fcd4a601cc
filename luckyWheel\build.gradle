apply plugin: 'com.android.library'

android {
    compileSdkVersion 31
    buildToolsVersion '30.0.3'

    defaultConfig {
        minSdkVersion 16
        targetSdkVersion 31
        versionCode 4
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    androidTestImplementation('androidx.test.espresso:espresso-core:3.2.0', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })
    implementation 'androidx.appcompat:appcompat:1.1.0'
    testImplementation 'junit:junit:4.13'
}
