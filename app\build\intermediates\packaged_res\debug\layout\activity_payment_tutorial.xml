<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="pay.PaymentTutorial">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/colorPrimary"
        android:minHeight="?attr/actionBarSize"
        android:theme="?attr/actionBarTheme"
        />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center">

        <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
            android:background="#ffff"
        android:gravity="center">


        <TextView
            android:layout_marginTop="10dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Step 1."
            android:textStyle="bold"
            android:textColor="@color/black"
            android:textSize="20dp"
            />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="First Select Payment Option"
            android:textColor="@color/black"
            android:textSize="18dp"
            />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="(पहले भुगतान विकल्प चुनें)"
            android:textColor="@color/black"
            android:textSize="18dp"
            />
        <RelativeLayout
            android:layout_marginTop="@dimen/_10sdp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/red_btn_bg_color"
            android:padding="@dimen/_10sdp"
            >
            <ImageView

                android:layout_width="match_parent"
                android:layout_height="@dimen/_450sdp"
              android:padding="10dp"
                app:srcCompat="@drawable/step1" />
        </RelativeLayout>


    </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="#ffff"
            android:gravity="center">


            <TextView
                android:layout_marginTop="10dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Step 2."
                android:textStyle="bold"
                android:textColor="@color/black"
                android:textSize="20dp"
                />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="If you want to pay with wallets than\nChoose your payment mode as wallet."
                android:textColor="@color/black"
                android:textAlignment="center"
                android:textSize="18dp"
                />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="(यदि आप वॉलेट के साथ भुगतान करना चाहते हैं तो \nवॉलेट के रूप में अपना भुगतान मोड चुनें।)"
                android:textColor="@color/black"
                android:textAlignment="center"
                android:textSize="18dp"
                />
            <RelativeLayout
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/red_btn_bg_color"
                android:padding="@dimen/_10sdp"
                >
                <ImageView

                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_450sdp"
                    android:padding="10dp"
                    app:srcCompat="@drawable/step2" />
            </RelativeLayout>

        </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="#ffff"
                android:gravity="center">


                <TextView
                    android:layout_marginTop="10dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Step 3."
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:textSize="20dp"
                    />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="First Select Wallet for payment.\n Tap on Proceed &amp; Pay"
                    android:textColor="@color/black"
                    android:textAlignment="center"
                    android:textSize="18dp"
                    />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="(भुगतान के लिए पहले वॉलेट चुनें। Proceed &amp; Pay पर टैप करें)"
                    android:textColor="@color/black"
                    android:textAlignment="center"
                    android:textSize="18dp"
                    />
                <RelativeLayout
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/red_btn_bg_color"
                    android:padding="@dimen/_10sdp"
                    >
                    <ImageView

                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_450sdp"
                        android:padding="10dp"
                        app:srcCompat="@drawable/step3" />
                </RelativeLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="#ffff"
                android:gravity="center">


                <TextView
                    android:layout_marginTop="10dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Step 4."
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:textSize="20dp"
                    />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="After redirecting to your wallet do the process of payment.\n After payment successfully, it automatically\n redirects to the Spin Win app."
                    android:textColor="@color/black"
                    android:textAlignment="center"
                    android:textSize="18dp"
                    />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="(अपने वॉलेट में रीडायरेक्ट करने के बाद भुगतान की प्रक्रिया करें।\nभुगतान सफलतापूर्वक करने के बाद, यह स्वचालित रूप से\n स्पिन विन ऐप पर रीडायरेक्ट करता है।)"
                    android:textColor="@color/black"
                    android:textAlignment="center"
                    android:textSize="18dp"
                    />
                <RelativeLayout
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/red_btn_bg_color"
                    android:padding="@dimen/_10sdp"
                    >
                </RelativeLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="#ffff"
                android:gravity="center">


                <TextView
                    android:layout_marginTop="10dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Step 5."
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:textSize="20dp"
                    />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="After payment successfully, You will get Success Popup.\n Click the Ok Button."
                    android:textColor="@color/black"
                    android:textAlignment="center"
                    android:textSize="18dp"
                    />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="(सफलतापूर्वक भुगतान करने के बाद, आपको सफलता पॉपअप मिलेगा।\nओके बटन पर क्लिक करें।)"
                    android:textColor="@color/black"
                    android:textAlignment="center"
                    android:textSize="18dp"
                    />
                <RelativeLayout
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/red_btn_bg_color"
                    android:padding="@dimen/_10sdp"
                    >
                    <ImageView

                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_450sdp"
                        android:padding="10dp"
                        app:srcCompat="@drawable/step4" />
                </RelativeLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="#ffff"
                android:gravity="center">


                <TextView
                    android:layout_marginTop="10dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Enjoy The Spin and Win Real Cash Daily!"
                    android:textStyle="bold"
                    android:textAlignment="center"
                    android:textColor="#D80000"
                    android:textSize="20dp"
                    />
                <TextView
                    android:layout_marginTop="10dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Thank You!"
                    android:textStyle="bold"
                    android:textAlignment="center"
                    android:textColor="#0012AC"
                    android:textSize="20dp"
                    />
                <RelativeLayout
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/red_btn_bg_color"
                    android:padding="@dimen/_10sdp"
                    >

                </RelativeLayout>

            </LinearLayout>

        </LinearLayout>
    </ScrollView>
</LinearLayout>