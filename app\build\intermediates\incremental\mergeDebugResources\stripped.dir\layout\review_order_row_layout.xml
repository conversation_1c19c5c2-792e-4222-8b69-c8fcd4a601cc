<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_10sdp"
    android:layout_marginLeft="@dimen/_20sdp"
    android:layout_marginRight="@dimen/_20sdp"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/ivDeleteOrderItem"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_marginStart="@dimen/_10sdp"
        android:layout_marginTop="@dimen/_10sdp"
        android:layout_marginEnd="@dimen/_10sdp"
        android:scaleType="fitCenter"
        android:src="@drawable/ic_delete" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_toStartOf="@id/ivDeleteOrderItem"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/etReviewOrderKey"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="key" />

        <EditText
            android:id="@+id/etReviewOrderValue"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10sdp"
            android:layout_weight="1"
            android:hint="Value" />
    </LinearLayout>

</RelativeLayout>
