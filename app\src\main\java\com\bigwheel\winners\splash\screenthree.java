package com.bigwheel.winners.splash;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;

import androidx.appcompat.app.AppCompatActivity;

import com.bigwheel.winners.R;

//import com.hipro.spin.win.R;

public class screenthree extends AppCompatActivity {

    Button nextbtn;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_screenthree);
        getSupportActionBar().hide();
        clickevent();
    }

    void clickevent(){
        nextbtn=findViewById(R.id.nextbtn);

        nextbtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                        Intent intent=new Intent(screenthree.this, screenfour.class);
                        startActivity(intent);
                        finish();
            }
        });
    }
}