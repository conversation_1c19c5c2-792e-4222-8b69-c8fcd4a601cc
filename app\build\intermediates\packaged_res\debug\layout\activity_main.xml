<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:openDrawer="start">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/themeBgColor">

        <!-- Header -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraintLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/themeColor"
            android:padding="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/btn_menu"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_menu"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins"
                android:text="Big Wheel Winners"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tvDepositeAmmount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/poppins"
                    android:text="0"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="/"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:id="@+id/tvWiningeAmmount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/poppins"
                    android:text="0"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Main Content -->
        <LinearLayout
            android:id="@+id/linearLayout4"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/constraintLayout">

            <!-- Premium Add Money Layout with ScrollView -->
            <ScrollView
                android:id="@+id/addMoneyLayout"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="10dp"
                android:layout_marginHorizontal="16dp"
                android:layout_marginBottom="20dp"
                android:background="@drawable/ic_rounded_theme_white"
                android:visibility="visible"
                android:elevation="12dp"
                app:layout_constraintTop_toBottomOf="@+id/wheelLayout"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHeight_max="500dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="20dp">

                <!-- Close Button -->
                <ImageView
                    android:id="@+id/btn_closeaddmoney"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="8dp"
                    android:src="@drawable/ic_baseline_close_24"
                    android:background="@drawable/ic_rounded_theme_yellow"
                    android:padding="8dp"
                    app:tint="@color/white"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <!-- Premium Title -->
                <TextView
                    android:id="@+id/textView9"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:fontFamily="@font/poppins"
                    android:text="🎯 Choose Your Plan"
                    android:textColor="@color/themeColor"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <!-- Compact Discount Information Card -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cvDiscountInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginHorizontal="4dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="3dp"
                    app:cardBackgroundColor="@color/premium_gold_light"
                    app:layout_constraintTop_toBottomOf="@+id/textView9">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="12dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/poppins"
                            android:text="💰 Bonus Offers!"
                            android:textColor="@color/premium_gold_dark"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:layout_gravity="center" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:fontFamily="@font/poppins"
                            android:text="₹100→₹110 • ₹150→₹180 • ₹200→₹260"
                            android:textColor="@color/text_primary"
                            android:textSize="12sp"
                            android:gravity="center" />

                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <!-- Selected Amount Display -->
                <EditText
                    android:id="@+id/etDeposite"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginHorizontal="8dp"
                    android:background="@drawable/ic_rounded_theme_white_black"
                    android:hint="💎 Select Your Plan Amount"
                    android:padding="16dp"
                    android:textAlignment="center"
                    android:editable="false"
                    android:textColor="@color/themeColor"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:layout_constraintTop_toBottomOf="@+id/cvDiscountInfo" />

                <!-- Compact Plans Grid (2x2 Layout) -->
                <LinearLayout
                    android:id="@+id/llPricingPlans"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginHorizontal="4dp"
                    android:orientation="vertical"
                    app:layout_constraintTop_toBottomOf="@+id/etDeposite">

                    <!-- First Row: ₹50 and ₹100 Plans -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="2"
                        android:layout_marginBottom="8dp">

                        <!-- Plan 1: ₹50 Basic -->
                        <androidx.cardview.widget.CardView
                            android:id="@+id/plan50"
                            android:layout_width="0dp"
                            android:layout_height="90dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="4dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="4dp"
                            app:cardBackgroundColor="@color/white"
                            android:clickable="true"
                            android:foreground="?android:attr/selectableItemBackground">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="8dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="🎯"
                                    android:textSize="18sp" />

                                <TextView
                                    android:id="@+id/price1"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="₹50"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/themeColor" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Basic"
                                    android:textSize="10sp"
                                    android:textColor="@color/text_secondary"
                                    android:fontFamily="@font/poppins" />

                            </LinearLayout>
                        </androidx.cardview.widget.CardView>

                        <!-- Plan 2: ₹100 Popular -->
                        <androidx.cardview.widget.CardView
                            android:id="@+id/plan100"
                            android:layout_width="0dp"
                            android:layout_height="90dp"
                            android:layout_weight="1"
                            android:layout_marginStart="4dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="4dp"
                            app:cardBackgroundColor="@color/premium_gold_light"
                            android:clickable="true"
                            android:foreground="?android:attr/selectableItemBackground">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="8dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="🔥"
                                    android:textSize="18sp" />

                                <TextView
                                    android:id="@+id/price2"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="₹100"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/premium_gold_dark" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="POPULAR"
                                    android:textSize="10sp"
                                    android:textColor="@color/premium_gold_dark"
                                    android:textStyle="bold"
                                    android:fontFamily="@font/poppins" />

                            </LinearLayout>
                        </androidx.cardview.widget.CardView>

                    </LinearLayout>

                    <!-- Second Row: ₹150 and ₹200 Plans -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="2">

                        <!-- Plan 3: ₹150 Premium -->
                        <androidx.cardview.widget.CardView
                            android:id="@+id/plan150"
                            android:layout_width="0dp"
                            android:layout_height="90dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="4dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="4dp"
                            app:cardBackgroundColor="@color/themeColor"
                            android:clickable="true"
                            android:foreground="?android:attr/selectableItemBackground">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="8dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="💎"
                                    android:textSize="18sp" />

                                <TextView
                                    android:id="@+id/price3"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="₹150"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/white" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="PREMIUM"
                                    android:textSize="10sp"
                                    android:textColor="@color/white"
                                    android:textStyle="bold"
                                    android:fontFamily="@font/poppins" />

                            </LinearLayout>
                        </androidx.cardview.widget.CardView>

                        <!-- Plan 4: ₹200 VIP -->
                        <androidx.cardview.widget.CardView
                            android:id="@+id/plan200"
                            android:layout_width="0dp"
                            android:layout_height="90dp"
                            android:layout_weight="1"
                            android:layout_marginStart="4dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="4dp"
                            app:cardBackgroundColor="@color/black"
                            android:clickable="true"
                            android:foreground="?android:attr/selectableItemBackground">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="8dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="👑"
                                    android:textSize="18sp" />

                                <TextView
                                    android:id="@+id/price4"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="₹200"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/premium_gold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="VIP"
                                    android:textSize="10sp"
                                    android:textColor="@color/premium_gold"
                                    android:textStyle="bold"
                                    android:fontFamily="@font/poppins" />

                            </LinearLayout>
                        </androidx.cardview.widget.CardView>

                    </LinearLayout>

                </LinearLayout>

                <!-- Compact Deposit Button -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginHorizontal="4dp"
                    android:layout_marginBottom="8dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="6dp"
                    app:cardBackgroundColor="@color/premium_gold"
                    app:layout_constraintTop_toBottomOf="@+id/llPricingPlans">

                    <LinearLayout
                        android:id="@+id/btnDeposite"
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:clickable="true"
                        android:foreground="?android:attr/selectableItemBackground"
                        android:padding="12dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="💳 PROCEED TO PAYMENT 🚀"
                            android:fontFamily="@font/poppins"
                            android:textColor="@color/white"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                </androidx.constraintlayout.widget.ConstraintLayout>
            </ScrollView>

            <!-- Withdraw Layout -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/withdrawMoneyLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@drawable/ic_rounded_theme_white"
                android:visibility="gone"
                android:padding="20dp">

                <ImageView
                    android:id="@+id/btn_closewithdraw"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="5dp"
                    android:src="@drawable/ic_baseline_close_24"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/textView11"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="25dp"
                    android:fontFamily="@font/poppins"
                    android:text="Withdraw Money"
                    android:textSize="18sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <EditText
                    android:id="@+id/etWithdraw"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginTop="22dp"
                    android:layout_marginHorizontal="24dp"
                    android:background="@drawable/ic_rounded_theme_white_black"
                    android:hint="Phone Number"
                    android:inputType="number"
                    android:padding="5dp"
                    android:textColor="@color/black"
                    app:layout_constraintTop_toBottomOf="@+id/textView11" />

                <EditText
                    android:id="@+id/etWithdrawAmmount"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginTop="22dp"
                    android:layout_marginHorizontal="24dp"
                    android:background="@drawable/ic_rounded_theme_white_black"
                    android:hint="Enter Amount"
                    android:inputType="number"
                    android:padding="5dp"
                    android:textColor="@color/black"
                    app:layout_constraintTop_toBottomOf="@+id/etWithdraw" />

                <LinearLayout
                    android:id="@+id/btnWithdrawFinal"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginHorizontal="24dp"
                    android:background="@drawable/cardbackground"
                    android:backgroundTint="@color/themeColor"
                    android:gravity="center"
                    app:layout_constraintTop_toBottomOf="@+id/etWithdrawAmmount">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Withdraw"
                        android:textColor="@color/white"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

        <!-- Wheel Layout -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/wheelLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="15dp"
            app:layout_constraintTop_toBottomOf="@+id/linearLayout4">

            <!-- Lucky Wheel Placeholder -->
            <ImageView
                android:id="@+id/luckyWheel"
                android:layout_width="300dp"
                android:layout_height="300dp"
                android:src="@drawable/wheels"
                android:scaleType="centerCrop"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:orientation="horizontal"
                android:weightSum="2"
                app:layout_constraintTop_toBottomOf="@+id/luckyWheel">

                <!-- Add Money Button -->
                <LinearLayout
                    android:id="@+id/btnAddMoney"
                    android:layout_width="0dp"
                    android:layout_height="45dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:layout_marginStart="44dp"
                    android:background="@drawable/cardbackground"
                    android:backgroundTint="@color/premium_gold"
                    android:gravity="center">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/poppins"
                        android:text="💰 ADD MONEY"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                </LinearLayout>

                <!-- Spin Button -->
                <LinearLayout
                    android:id="@+id/btnSpin"
                    android:layout_width="0dp"
                    android:layout_height="45dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="44dp"
                    android:background="@drawable/roundedbutton"
                    android:gravity="center">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/poppins"
                        android:text="🎯 SPIN"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="end"
        android:background="@color/themeBgColor"
        android:fitsSystemWindows="true"
        app:itemTextColor="@color/white"
        android:theme="@style/NavigationView"
        app:itemIconTint="@color/white"
        app:headerLayout="@layout/nav_header_navigation_menu"
        app:menu="@menu/activity_navigation_menu_drawer" />

</androidx.drawerlayout.widget.DrawerLayout>
