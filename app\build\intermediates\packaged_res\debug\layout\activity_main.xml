<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:openDrawer="start">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/themeBgColor">

        <!-- Header -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraintLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/themeColor"
            android:padding="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/btn_menu"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_menu"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins"
                android:text="Big Wheel Winners"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvCoins"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins"
                android:text="0"
                android:textColor="@color/white"
                android:textSize="20sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Main Content -->
        <LinearLayout
            android:id="@+id/linearLayout4"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/constraintLayout">

            <!-- Add Money Layout -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/addMoneyLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@drawable/ic_rounded_theme_white"
                android:visibility="gone"
                android:padding="20dp">

                <!-- Close Button -->
                <ImageView
                    android:id="@+id/btn_closeaddmoney"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="8dp"
                    android:src="@drawable/ic_baseline_close_24"
                    android:padding="6dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <!-- Header -->
                <TextView
                    android:id="@+id/textView9"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="25dp"
                    android:fontFamily="@font/poppins"
                    android:text="Choose Your Spin Package"
                    android:textColor="@color/black"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <!-- Amount Input -->
                <EditText
                    android:id="@+id/etDeposite"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginLeft="25dp"
                    android:layout_marginTop="22dp"
                    android:layout_marginRight="24dp"
                    android:background="@drawable/ic_rounded_theme_white_black"
                    android:hint="Select Deposit Amount"
                    android:padding="5dp"
                    android:textAlignment="center"
                    android:editable="false"
                    android:textColor="@color/black"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView9" />

                <!-- Plan Options -->
                <LinearLayout
                    android:id="@+id/llPricingPlans"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:orientation="horizontal"
                    android:weightSum="4"
                    app:layout_constraintTop_toBottomOf="@+id/etDeposite">

                    <!-- Plan 1: ₹50 -->
                    <LinearLayout
                        android:id="@+id/plan50"
                        android:layout_width="0dp"
                        android:layout_height="80dp"
                        android:layout_weight="1"
                        android:layout_margin="4dp"
                        android:background="@drawable/ic_rounded_theme_white_black"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:clickable="true">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="₹50"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/black" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Basic"
                            android:textSize="12sp"
                            android:textColor="@color/gray" />

                    </LinearLayout>

                    <!-- Plan 2: ₹100 -->
                    <LinearLayout
                        android:id="@+id/plan100"
                        android:layout_width="0dp"
                        android:layout_height="80dp"
                        android:layout_weight="1"
                        android:layout_margin="4dp"
                        android:background="@drawable/ic_rounded_theme_white_black"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:clickable="true">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="₹100"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/black" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Popular"
                            android:textSize="12sp"
                            android:textColor="@color/gray" />

                    </LinearLayout>

                    <!-- Plan 3: ₹150 -->
                    <LinearLayout
                        android:id="@+id/plan150"
                        android:layout_width="0dp"
                        android:layout_height="80dp"
                        android:layout_weight="1"
                        android:layout_margin="4dp"
                        android:background="@drawable/ic_rounded_theme_white_black"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:clickable="true">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="₹150"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/black" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Premium"
                            android:textSize="12sp"
                            android:textColor="@color/gray" />

                    </LinearLayout>

                    <!-- Plan 4: ₹200 -->
                    <LinearLayout
                        android:id="@+id/plan200"
                        android:layout_width="0dp"
                        android:layout_height="80dp"
                        android:layout_weight="1"
                        android:layout_margin="4dp"
                        android:background="@drawable/ic_rounded_theme_white_black"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:clickable="true">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="₹200"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/black" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="VIP"
                            android:textSize="12sp"
                            android:textColor="@color/gray" />

                    </LinearLayout>

                </LinearLayout>

                <!-- Deposit Button -->
                <LinearLayout
                    android:id="@+id/btnDeposite"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginHorizontal="16dp"
                    android:background="@drawable/cardbackground"
                    android:backgroundTint="@color/themeColor"
                    android:gravity="center"
                    android:orientation="horizontal"
                    app:layout_constraintTop_toBottomOf="@+id/llPricingPlans">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="PROCEED TO PAYMENT"
                        android:fontFamily="@font/poppins"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Withdraw Layout -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/withdrawMoneyLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@drawable/ic_rounded_theme_white"
                android:visibility="gone"
                android:padding="20dp">

                <ImageView
                    android:id="@+id/btn_closewithdraw"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="5dp"
                    android:src="@drawable/ic_baseline_close_24"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/textView11"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="25dp"
                    android:fontFamily="@font/poppins"
                    android:text="Withdraw Money"
                    android:textSize="18sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <EditText
                    android:id="@+id/etWithdraw"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginTop="22dp"
                    android:layout_marginHorizontal="24dp"
                    android:background="@drawable/ic_rounded_theme_white_black"
                    android:hint="Phone Number"
                    android:inputType="number"
                    android:padding="5dp"
                    android:textColor="@color/black"
                    app:layout_constraintTop_toBottomOf="@+id/textView11" />

                <EditText
                    android:id="@+id/etWithdrawAmmount"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginTop="22dp"
                    android:layout_marginHorizontal="24dp"
                    android:background="@drawable/ic_rounded_theme_white_black"
                    android:hint="Enter Amount"
                    android:inputType="number"
                    android:padding="5dp"
                    android:textColor="@color/black"
                    app:layout_constraintTop_toBottomOf="@+id/etWithdraw" />

                <LinearLayout
                    android:id="@+id/btnWithdrawFinal"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginHorizontal="24dp"
                    android:background="@drawable/cardbackground"
                    android:backgroundTint="@color/themeColor"
                    android:gravity="center"
                    app:layout_constraintTop_toBottomOf="@+id/etWithdrawAmmount">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Withdraw"
                        android:textColor="@color/white"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

        <!-- Spin Button -->
        <LinearLayout
            android:id="@+id/linearLayout6"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="15dp"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@+id/linearLayout4">

            <LinearLayout
                android:id="@+id/btnSpin"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:layout_marginHorizontal="44dp"
                android:background="@drawable/roundedbutton"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/poppins"
                    android:text="SPIN"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:background="@color/themeBgColor"
        android:fitsSystemWindows="true"
        app:itemTextColor="@color/white"
        android:theme="@style/NavigationView"
        app:itemIconTint="@color/white"
        app:headerLayout="@layout/nav_header_navigation_menu"
        app:menu="@menu/activity_navigation_menu_drawer" />

</androidx.drawerlayout.widget.DrawerLayout>
