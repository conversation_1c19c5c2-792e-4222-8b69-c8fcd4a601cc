<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drwa"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/themeBgColor"
    android:orientation="vertical"
    tools:context=".MainActivity">

    <!--    Details Layout-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/linearLayout5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"

            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:paddingBottom="5dp"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toTopOf="@+id/linearLayout2"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/menu"
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="10dp"
                    android:src="@drawable/ic_menu"
                    app:tint="@color/newWhitr"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/textView7"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:fontFamily="@font/poppins_bold"
                        android:includeFontPadding="false"
                        android:text="Winner"
                        android:textColor="@color/newWhitr"
                        android:textSize="20dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/textView8"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:fontFamily="@font/poppins"
                        android:includeFontPadding="false"
                        android:text="Wallet Balance"
                        android:textColor="@color/white"
                        android:textSize="14dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/textView7" />
                </LinearLayout>
            </RelativeLayout>


            <LinearLayout
                android:id="@+id/linearLayout2"
                android:layout_width="match_parent"
                android:layout_height="70dp"
                android:orientation="horizontal"
                android:weightSum="2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:paddingLeft="19dp"
                    android:paddingRight="8.5dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        >

                        <TextView
                            android:id="@+id/tvDepositeAmmount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:fontFamily="@font/poppins_bold"
                            android:includeFontPadding="false"
                            android:text="0"
                            android:textColor="@color/white"
                            android:textSize="20dp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/poppins"
                            android:includeFontPadding="false"
                            android:text="Deposits"
                            android:textColor="@color/white"
                            android:textSize="14dp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tvDepositeAmmount" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:layout_weight="0.01"
                    android:background="@color/white"></LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:paddingLeft="8.5dp"
                    android:paddingRight="19dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        >

                        <TextView
                            android:id="@+id/tvWiningeAmmount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:fontFamily="@font/poppins_bold"
                            android:includeFontPadding="false"
                            android:text="0"
                            android:textColor="@color/white"
                            android:textSize="20dp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/poppins"
                            android:includeFontPadding="false"
                            android:text="Winnings"
                            android:textColor="@color/white"
                            android:textSize="14dp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tvWiningeAmmount" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linearLayout2">

                <LinearLayout
                    android:id="@+id/btnAddMoney"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="40dp"
                    android:layout_weight="1"
                    android:background="@drawable/cardbackground"
                    app:layout_constraintTop_toBottomOf="@+id/linearLayout2">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:backgroundTint="@color/tran"
                        android:gravity="center"
                        android:includeFontPadding="false"
                        android:text="Add Spin"
                        android:textColor="@color/themeColor"
                        android:textSize="16dp"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/btnWithdraw"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="40dp"
                    android:layout_weight="1"
                    android:background="@drawable/cardbackground"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/btnAddMoney">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:backgroundTint="@color/tran"
                        android:gravity="center"
                        android:includeFontPadding="false"
                        android:text="Withdraw"
                        android:textColor="@color/themeColor"
                        android:textSize="16dp"
                        android:textStyle="bold"></TextView>

                </LinearLayout>


            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone">

                <Switch
                    android:id="@+id/swith"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:gravity="center"
                    android:text="Sound"
                    android:textSize="20dp"
                    android:textStyle="bold"></Switch>
            </RelativeLayout>
        </LinearLayout>


        <TextView
            android:id="@+id/scroller"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="24dp"
            android:background="@drawable/ic_rounded_theme_yellow"
            android:ellipsize="marquee"
            android:padding="1dp"
            android:singleLine="true"
            android:text="@string/winlist"
            android:textAppearance="?android:attr/textAppearanceLarge"
            android:textColor="@color/white"
            android:textSize="15sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/linearLayout5" />

        <LinearLayout
            android:id="@+id/linearLayout4"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintTop_toBottomOf="@+id/scroller">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/wheelLayout"
                android:layout_width="300dp"
                android:layout_height="300dp"
                app:layout_constraintBottom_toTopOf="@+id/btnSpin"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/constraintLayout">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="22dp"
                        android:src="@drawable/wheels">

                    </ImageView>

                    <ImageView
                        android:id="@+id/cursorView"
                        android:layout_width="47dp"
                        android:layout_height="47dp"
                        android:src="@drawable/teer2"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="15dp" />

                    <ImageView
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_gravity="center"
                        android:layout_marginTop="10dp"
                        android:src="@drawable/wheels"
                        android:visibility="gone"></ImageView>
                </FrameLayout>

                <rubikstudio.library.LuckyWheelView
                    android:id="@+id/luckyWheel"
                    android:layout_width="300dp"
                    android:layout_height="300dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0"
                    app:lkwBackgroundColor="@android:color/transparent"
                    app:lkwCenterImage="@drawable/wheels"
                    app:lkwCursor="@drawable/teer2" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <!--        Premium Add Money Layout-->

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/addMoneyLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="16dp"
                android:background="@drawable/bg_premium_popup"
                android:visibility="visible"
                android:elevation="12dp"
                android:padding="20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/constraintLayout">

                <!-- Close Button -->
                <ImageView
                    android:id="@+id/btn_closeaddmoney"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="8dp"
                    android:src="@drawable/ic_baseline_close_24"
                    android:background="@drawable/bg_discount_badge"
                    android:padding="6dp"
                    app:tint="@color/white"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <!-- Premium Header -->
                <TextView
                    android:id="@+id/tvPremiumTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:fontFamily="@font/poppins_bold"
                    android:text="🎯 Choose Your Spin Package"
                    android:textColor="@color/white"
                    android:textSize="22sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvPremiumSubtitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:fontFamily="@font/poppins"
                    android:text="💰 Get More Spins with Better Plans!"
                    android:textColor="@color/premium_gold_light"
                    android:textSize="16sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvPremiumTitle" />

                <!-- Selected Amount Display -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cvSelectedAmount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginHorizontal="16dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="8dp"
                    app:cardBackgroundColor="@color/white"
                    app:layout_constraintTop_toBottomOf="@+id/tvPremiumSubtitle">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:fontFamily="@font/poppins"
                            android:text="Selected Amount:"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />

                        <EditText
                            android:id="@+id/etDeposite"
                            android:layout_width="120dp"
                            android:layout_height="40dp"
                            android:background="@drawable/bg_plan_basic"
                            android:hint="₹0"
                            android:textAlignment="center"
                            android:editable="false"
                            android:fontFamily="@font/poppins_bold"
                            android:textColor="@color/text_primary"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <!-- Premium Pricing Plans Grid -->
                <LinearLayout
                    android:id="@+id/llPricingPlans"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:orientation="vertical"
                    app:layout_constraintTop_toBottomOf="@+id/cvSelectedAmount">

                    <!-- First Row: Basic & Popular Plans -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <!-- Basic Plan (₹50) -->
                        <androidx.cardview.widget.CardView
                            android:id="@+id/deposite_50"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="6dp"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="6dp"
                            app:cardBackgroundColor="@color/white">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:padding="16dp"
                                android:gravity="center"
                                android:background="@drawable/bg_plan_basic">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="BASIC"
                                    android:fontFamily="@font/poppins_bold"
                                    android:textColor="@color/plan_basic_border"
                                    android:textSize="12sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/price1"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:fontFamily="@font/poppins_bold"
                                    android:text="₹50"
                                    android:textColor="@color/text_primary"
                                    android:textSize="20sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="2dp"
                                    android:text="No Discount"
                                    android:fontFamily="@font/poppins"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="10sp" />

                            </LinearLayout>
                        </androidx.cardview.widget.CardView>

                        <!-- Popular Plan (₹100) -->
                        <androidx.cardview.widget.CardView
                            android:id="@+id/deposite_100"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="6dp"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="8dp"
                            app:cardBackgroundColor="@color/white">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:padding="16dp"
                                android:gravity="center"
                                android:background="@drawable/bg_plan_popular">

                                <!-- Popular Badge -->
                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="🔥 POPULAR"
                                    android:fontFamily="@font/poppins_bold"
                                    android:textColor="@color/plan_popular_border"
                                    android:textSize="12sp"
                                    android:textStyle="bold" />

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:orientation="horizontal"
                                    android:gravity="center_vertical">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="₹110"
                                        android:fontFamily="@font/poppins"
                                        android:textColor="@color/text_secondary"
                                        android:textSize="14sp"
                                        android:layout_marginEnd="4dp"
                                        android:background="@drawable/bg_discount_badge"
                                        android:padding="4dp"
                                        android:textStyle="normal" />

                                    <TextView
                                        android:id="@+id/price2"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:fontFamily="@font/poppins_bold"
                                        android:text="₹100"
                                        android:textColor="@color/text_primary"
                                        android:textSize="20sp"
                                        android:textStyle="bold" />

                                </LinearLayout>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="2dp"
                                    android:text="10% OFF"
                                    android:fontFamily="@font/poppins_bold"
                                    android:textColor="@color/success_green"
                                    android:textSize="10sp"
                                    android:textStyle="bold" />

                            </LinearLayout>
                        </androidx.cardview.widget.CardView>

                    </LinearLayout>

                    <!-- Second Row: Premium & VIP Plans -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <!-- Premium Plan (₹150) -->
                        <androidx.cardview.widget.CardView
                            android:id="@+id/deposite_150"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="6dp"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="8dp"
                            app:cardBackgroundColor="@color/white">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:padding="16dp"
                                android:gravity="center"
                                android:background="@drawable/bg_plan_premium">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="💎 PREMIUM"
                                    android:fontFamily="@font/poppins_bold"
                                    android:textColor="@color/plan_premium_border"
                                    android:textSize="12sp"
                                    android:textStyle="bold" />

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:orientation="horizontal"
                                    android:gravity="center_vertical">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="₹180"
                                        android:fontFamily="@font/poppins"
                                        android:textColor="@color/text_secondary"
                                        android:textSize="14sp"
                                        android:layout_marginEnd="4dp"
                                        android:background="@drawable/bg_discount_badge"
                                        android:padding="4dp"
                                        android:textStyle="normal" />

                                    <TextView
                                        android:id="@+id/price3"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:fontFamily="@font/poppins_bold"
                                        android:text="₹150"
                                        android:textColor="@color/text_primary"
                                        android:textSize="20sp"
                                        android:textStyle="bold" />

                                </LinearLayout>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="2dp"
                                    android:text="20% OFF"
                                    android:fontFamily="@font/poppins_bold"
                                    android:textColor="@color/success_green"
                                    android:textSize="10sp"
                                    android:textStyle="bold" />

                            </LinearLayout>
                        </androidx.cardview.widget.CardView>

                        <!-- VIP Plan (₹200) -->
                        <androidx.cardview.widget.CardView
                            android:id="@+id/deposite_200"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="6dp"
                            app:cardCornerRadius="16dp"
                            app:cardElevation="10dp"
                            app:cardBackgroundColor="@color/white">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:padding="16dp"
                                android:gravity="center"
                                android:background="@drawable/bg_plan_vip">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="👑 VIP"
                                    android:fontFamily="@font/poppins_bold"
                                    android:textColor="@color/premium_gold"
                                    android:textSize="12sp"
                                    android:textStyle="bold" />

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:orientation="horizontal"
                                    android:gravity="center_vertical">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="₹260"
                                        android:fontFamily="@font/poppins"
                                        android:textColor="@color/text_secondary"
                                        android:textSize="14sp"
                                        android:layout_marginEnd="4dp"
                                        android:background="@drawable/bg_discount_badge"
                                        android:padding="4dp"
                                        android:textStyle="normal" />

                                    <TextView
                                        android:id="@+id/price4"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:fontFamily="@font/poppins_bold"
                                        android:text="₹200"
                                        android:textColor="@color/text_primary"
                                        android:textSize="20sp"
                                        android:textStyle="bold" />

                                </LinearLayout>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="2dp"
                                    android:text="30% OFF"
                                    android:fontFamily="@font/poppins_bold"
                                    android:textColor="@color/premium_gold"
                                    android:textSize="10sp"
                                    android:textStyle="bold" />

                            </LinearLayout>
                        </androidx.cardview.widget.CardView>

                    </LinearLayout>

                </LinearLayout>

                <!-- Premium Deposit Button -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/btnDeposite"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:layout_marginHorizontal="16dp"
                    app:cardCornerRadius="25dp"
                    app:cardElevation="8dp"
                    app:cardBackgroundColor="@color/white"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/llPricingPlans">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:background="@drawable/bg_premium_button"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🚀 "
                            android:textSize="20sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="PROCEED TO PAYMENT"
                            android:fontFamily="@font/poppins_bold"
                            android:textColor="@color/white"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                    </LinearLayout>
                </androidx.cardview.widget.CardView>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- WithDraw Layout-->

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/withdrawMoneyLayout"
                android:layout_width="match_parent"
                android:layout_height="290dp"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="20dp"
                android:background="@drawable/ic_rounded_theme_white"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/addMoneyLayout">

                <TextView
                    android:id="@+id/textView11"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="25dp"
                    android:fontFamily="@font/poppins"
                    android:text=""
                    android:textSize="18dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/btn_closewithdraw"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="5dp"
                    android:src="@drawable/ic_baseline_close_24"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:layout_width="49dp"
                    android:layout_height="49dp"
                    android:layout_marginRight="1dp"
                    android:src="@drawable/ic_paytm"
                    app:layout_constraintBottom_toBottomOf="@+id/etWithdraw"
                    app:layout_constraintEnd_toStartOf="@+id/etWithdraw"
                    app:layout_constraintTop_toTopOf="@+id/etWithdraw" />

                <EditText
                    android:id="@+id/etWithdraw"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginLeft="75dp"
                    android:layout_marginTop="22dp"
                    android:layout_marginRight="24dp"
                    android:maxLength="10"
                    android:background="@drawable/ic_rounded_theme_white_black"
                    android:hint="@string/phone_number"
                    android:inputType="number"
                    android:padding="5dp"
                    android:textColor="@color/black"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView11" />

                <EditText
                    android:id="@+id/etWithdrawAmmount"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginLeft="25dp"
                    android:layout_marginTop="22dp"
                    android:layout_marginRight="24dp"
                    android:background="@drawable/ic_rounded_theme_white_black"
                    android:hint="Enter Ammount"
                    android:inputType="number"
                    android:padding="5dp"
                    android:textColor="@color/black"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/etWithdraw" />

                <LinearLayout
                    android:id="@+id/btnWithdrawFinal"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@drawable/cardbackground"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    android:layout_marginLeft="24dp"
                    android:backgroundTint="@color/themeColor"
                    android:layout_marginTop="15dp"
                    android:layout_marginRight="24dp"
                    app:layout_constraintTop_toBottomOf="@+id/etWithdrawAmmount">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="Withdraw"
                        android:gravity="center"
                        android:textColor="@color/white"
                        android:textSize="20dp"
                        android:textStyle="bold">

                    </TextView>
                </LinearLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>

        <!--    Spin Layout-->

        <LinearLayout
            android:id="@+id/linearLayout6"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="15dp"
            android:gravity="center"
            android:visibility="visible"
            app:layout_constraintTop_toBottomOf="@+id/linearLayout4"
            tools:layout_editor_absoluteX="15dp">

            <LinearLayout
                android:id="@+id/btnSpin"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:layout_marginLeft="44dp"
                android:layout_marginRight="45dp"
                android:background="@drawable/roundedbutton"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:backgroundTintMode="add"
                    android:fontFamily="@font/poppins"
                    android:gravity="center"
                    android:text="SPIN"
                    android:textColor="@color/white"
                    android:textSize="20dp"
                    android:textStyle="bold"></TextView>

            </LinearLayout>


        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


    <!--<LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:background="@color/themeBgColor">
        <include layout="@layout/drawerlayout"></include>

    </LinearLayout>-->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:background="@color/themeBgColor"
        android:fitsSystemWindows="true"
        app:itemTextColor="@color/white"
        android:theme="@style/NavigationView"
        app:itemIconTint="@color/white"
        app:headerLayout="@layout/nav_header_navigation_menu"
        app:menu="@menu/activity_navigation_menu_drawer" />


</androidx.drawerlayout.widget.DrawerLayout>