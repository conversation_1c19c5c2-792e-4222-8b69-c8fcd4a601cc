// Generated by view binder compiler. Do not edit!
package com.bigwheel.winners.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bigwheel.winners.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class AreyousurdilogBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final LinearLayout btnLater;

  @NonNull
  public final LinearLayout btnRateNow;

  @NonNull
  public final TextView tvDescription;

  @NonNull
  public final TextView tvTitle;

  @NonNull
  public final TextView winamount;

  private AreyousurdilogBinding(@NonNull CardView rootView, @NonNull LinearLayout btnLater,
      @NonNull LinearLayout btnRateNow, @NonNull TextView tvDescription, @NonNull TextView tvTitle,
      @NonNull TextView winamount) {
    this.rootView = rootView;
    this.btnLater = btnLater;
    this.btnRateNow = btnRateNow;
    this.tvDescription = tvDescription;
    this.tvTitle = tvTitle;
    this.winamount = winamount;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static AreyousurdilogBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static AreyousurdilogBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.areyousurdilog, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static AreyousurdilogBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnLater;
      LinearLayout btnLater = ViewBindings.findChildViewById(rootView, id);
      if (btnLater == null) {
        break missingId;
      }

      id = R.id.btnRateNow;
      LinearLayout btnRateNow = ViewBindings.findChildViewById(rootView, id);
      if (btnRateNow == null) {
        break missingId;
      }

      id = R.id.tvDescription;
      TextView tvDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvDescription == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      id = R.id.winamount;
      TextView winamount = ViewBindings.findChildViewById(rootView, id);
      if (winamount == null) {
        break missingId;
      }

      return new AreyousurdilogBinding((CardView) rootView, btnLater, btnRateNow, tvDescription,
          tvTitle, winamount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
