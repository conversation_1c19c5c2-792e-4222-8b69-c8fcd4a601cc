<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#ECECEC"
    android:gravity="center"
    tools:context="pay.PaymentResult">

<!--    <androidx.appcompat.widget.Toolbar-->
<!--        android:id="@+id/toolbartra"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="50dp"-->
<!--        android:background="@color/black" />-->

    <androidx.cardview.widget.CardView xmlns:card_view="http://schemas.android.com/apk/res-auto"
        android:id="@+id/card_view"
        android:layout_width="match_parent"
        android:layout_height="500dp"
        android:layout_gravity="center"
        card_view:cardBackgroundColor="@color/cardview_light_background"
        android:layout_marginTop="20dp"
        card_view:cardCornerRadius="15dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:weightSum="5">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="horizontal">

                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/animationView"
                    android:layout_width="90dp"
                    android:layout_height="90dp"
                    card_view:lottie_rawRes="@raw/sucesso"
                    app:lottie_autoPlay="true"
                    app:lottie_loop="true"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1.6"
                android:orientation="vertical"
                android:gravity="center_horizontal">
                <TextView
                    android:id="@+id/finaldata"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Payment Success!"
                    android:textSize="@dimen/_22sdp"
                    android:layout_marginTop="5dp"
                    android:textAlignment="center"
                    android:textColor="#00B548"
                    android:textStyle="bold"
                    />

                <TextView
                    android:id="@+id/textinfo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Great! Now you can make video call\n with random girls!"
                    android:textSize="15dp"
                    android:layout_marginTop="18dp"
                    android:textAlignment="center"
                    android:textColor="#2C2C2C"
                    android:textStyle="bold"
                    />

                <TextView
                    android:id="@+id/date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="April 18,2021 11:00 AM"
                    android:textSize="15dp"
                    android:layout_marginTop="10dp"
                    android:textAlignment="center"
                    android:textColor="#88000000"
                    android:textStyle="bold"
                    />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="0.4"
                android:orientation="horizontal"
                android:gravity="center_horizontal"
                >
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Transaction Id :  "
                    android:textSize="15dp"
                    android:layout_marginTop="10dp"
                    android:textAlignment="center"
                    android:textColor="#000000"
                    android:textStyle="bold"
                    />
                <TextView
                    android:id="@+id/tranID"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="ODRD151515151515"
                    android:textSize="15dp"
                    android:layout_marginTop="10dp"
                    android:textAlignment="center"
                    android:textColor="#A9000000"
                    android:textStyle="bold"
                    />


            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="0.4"
                android:orientation="horizontal"
                android:gravity="center_horizontal"
                >
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Transaction Amount :  "
                    android:textSize="15dp"
                    android:layout_marginTop="10dp"
                    android:textAlignment="center"
                    android:textColor="#000000"
                    android:textStyle="bold"
                    />
                <TextView
                    android:id="@+id/tranAmu"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="100.00"
                    android:textSize="15dp"
                    android:layout_marginTop="10dp"
                    android:textAlignment="center"
                    android:textColor="#A9000000"
                    android:textStyle="bold"
                    />


            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="0.4"
                android:orientation="horizontal"
                android:gravity="center_horizontal"
                >
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Email :  "
                    android:textSize="15dp"
                    android:layout_marginTop="10dp"
                    android:textAlignment="center"
                    android:textColor="#000000"
                    android:textStyle="bold"
                    />
                <TextView
                    android:id="@+id/tranemail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="<EMAIL>"
                    android:textSize="15dp"
                    android:layout_marginTop="10dp"
                    android:textAlignment="center"
                    android:textColor="#A9000000"
                    android:textStyle="bold"
                    />


            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1.2"
                android:orientation="horizontal"

                android:gravity="center">

                <Button
                    android:id="@+id/okbtn"
                    android:layout_width="@dimen/_200sdp"
                    android:layout_height="@dimen/_50sdp"
                    android:text="Ok"
                    android:textStyle="bold"
                    android:textColor="@color/white"
                    android:textAlignment="center"
                    android:textSize="18sp"

                    />


            </LinearLayout>




        </LinearLayout>


    </androidx.cardview.widget.CardView>


</LinearLayout>