<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Pressed State -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient
                android:angle="45"
                android:startColor="@color/gradient_secondary_start"
                android:endColor="@color/gradient_secondary_end"
                android:type="linear" />
            <corners android:radius="25dp" />
        </shape>
    </item>
    
    <!-- Normal State -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:angle="45"
                android:startColor="@color/gradient_primary_start"
                android:endColor="@color/gradient_primary_end"
                android:type="linear" />
            <corners android:radius="25dp" />
        </shape>
    </item>
    
</selector>
