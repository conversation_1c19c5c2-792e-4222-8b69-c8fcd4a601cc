package com.bigwheel.winners.splash;

import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;

import com.bigwheel.winners.R;
import com.bigwheel.winners.SplashScreen;
import com.google.firebase.database.DataSnapshot;
import com.google.firebase.database.DatabaseError;
import com.google.firebase.database.DatabaseReference;
import com.google.firebase.database.FirebaseDatabase;
import com.google.firebase.database.ValueEventListener;

public class splash extends AppCompatActivity {

    SharedPreferences pref;
    SharedPreferences.Editor editor;
    int vcode;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash);
        pref = getSharedPreferences("mypref", MODE_PRIVATE);
        editor = pref.edit();
        getSupportActionBar().hide();
        if (!isConnectionAvailable(this)) {
            new AlertDialog.Builder(splash.this)
                    .setTitle("Error!")
                    .setMessage("Check Your Internet Connection")
                    .setCancelable(false)
                    .setPositiveButton("Retry", new DialogInterface.OnClickListener() {
                        public void onClick(DialogInterface dialog, int which) {
                            // Continue with delete operation
                            Intent intent = new Intent(splash.this, splash.class);
                            startActivity(intent);
                            finish();
                        }
                    })
                    // A null listener allows the button to dismiss the dialog and take no further action.
                    .setNegativeButton("Exit", new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialogInterface, int i) {
                            finish();
                        }
                    })
                    .setIcon(android.R.drawable.ic_dialog_alert)
                    .show();
            return;
        } else
        {
            FirebaseDatabase database = FirebaseDatabase.getInstance();
            DatabaseReference myRef = database.getReference("mydata");
            Log.d("ghetdata", "onCreate: "+myRef);
            myRef.addValueEventListener(new ValueEventListener() {
                @Override
                public void onDataChange(DataSnapshot dataSnapshot) {
                    // Toast.makeText(splash.this, "vcode", Toast.LENGTH_SHORT).show();
                    // progressDialog.dismiss();
                    // This method is called once with the initial value and again
                    // whenever data at this location is updated.
                    String value = dataSnapshot.child("url").getValue(String.class);
                    editor.putString("baseurl", value);

                    editor.putString("wamu", dataSnapshot.child("wamu").getValue(String.class));
                    editor.putString("damu1", dataSnapshot.child("damu1").getValue(String.class));
                    editor.putString("damu2", dataSnapshot.child("damu2").getValue(String.class));
                    editor.putString("damu3", dataSnapshot.child("damu3").getValue(String.class));
                    editor.putString("upiid", dataSnapshot.child("upiid").getValue(String.class));
                    editor.putString("version", dataSnapshot.child("version").getValue(String.class));
                    editor.putString("splash", dataSnapshot.child("splash").getValue(String.class));
                    editor.apply();
                    vcode = Integer.parseInt(dataSnapshot.child("version").getValue(String.class));
                    Log.d("abcd", "onDataChange: bkjnjkj");
                    splashscreen();
                }

                @Override
                public void onCancelled(DatabaseError error) {
                    // Failed to read value
                    // progressDialog.dismiss();
                    //  pDialog.dismiss();

                    new AlertDialog.Builder(splash.this)
                            .setTitle("Error!")
                            .setMessage("Check Your Internet Connection")
                            .setCancelable(false)
                            .setPositiveButton("Retry", new DialogInterface.OnClickListener() {
                                public void onClick(DialogInterface dialog, int which) {
                                    // Continue with delete operation
                                    Intent intent = new Intent(splash.this, splash.class);
                                    startActivity(intent);
                                    finish();
                                }
                            })
                            // A null listener allows the button to dismiss the dialog and take no further action.
                            .setNegativeButton("Exit", new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialogInterface, int i) {
                                    finish();
                                }
                            })
                            .setIcon(android.R.drawable.ic_dialog_alert)
                            .show();
                    //  btnGetMoney.setVisibility(View.VISIBLE);
                }
            });
        }
//if 0 google splash and 1 game

    }

    void splashscreen()
    {
        int i = Integer.parseInt(pref.getString("splash",""));

        if(i == 0)
        {
            Intent screeone = new Intent(splash.this,screentwo.class);

            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    startActivity(screeone);
                    finish();
                }
            },4000);
        }
        else
        {
            Intent intent = new Intent(splash.this, SplashScreen.class);
            startActivity(intent);
            finish();
        }
    }

    public static boolean isConnectionAvailable (Context context){
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo netInfo = connectivityManager.getActiveNetworkInfo();
            if (netInfo != null && netInfo.isConnected()
                    && netInfo.isConnectedOrConnecting()
                    && netInfo.isAvailable()) {
                return true;
            }
        }
        return false;
    }
}