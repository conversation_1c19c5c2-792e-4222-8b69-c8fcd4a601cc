package com.bigwheel.winners;

import android.app.ProgressDialog;
import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;

import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;
import com.google.android.gms.auth.api.signin.GoogleSignIn;
import com.google.android.gms.auth.api.signin.GoogleSignInAccount;
import com.google.android.gms.auth.api.signin.GoogleSignInClient;
import com.google.android.gms.auth.api.signin.GoogleSignInOptions;
import com.google.android.gms.common.api.ApiException;
import com.google.android.gms.tasks.Task;

import java.util.HashMap;
import java.util.Map;

public class GoogleLogin extends AppCompatActivity {

    GoogleSignInOptions gso;
    GoogleSignInClient mGoogleSignInClient;
    private static int RC_SIGN_IN = 100;
    LinearLayout gloginBtn;
    SharedPreferences pref;
    SharedPreferences.Editor editor;
    CheckBox accept;
    TextView textView12,textView13,tv_terms,tv_privacy;
    String privacyUrl ="https://sites.google.com/view/big-wheel-privacy-policy/home";
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_google_login);
        gloginBtn = findViewById(R.id.iv_googlelogin);
        textView12 = findViewById(R.id.textView12);
        textView13=findViewById(R.id.textView13);
        accept = findViewById(R.id.checkbox_terms);
        tv_terms=findViewById(R.id.tv_terms);
        tv_privacy = findViewById(R.id.tv_privacy);
        pref=getSharedPreferences("mypref",MODE_PRIVATE);
        editor=pref.edit();

        getSupportActionBar().hide();
        gso = new GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN).requestEmail().build();
        mGoogleSignInClient = GoogleSignIn.getClient(this, gso);

        GoogleSignInAccount account = GoogleSignIn.getLastSignedInAccount(this);
        if (account != null) {
            startActivity(new Intent(GoogleLogin.this, MainActivity.class));
            finish();
            return;
        }

        tv_terms.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                try {
                    Intent myIntent = new Intent(Intent.ACTION_VIEW, Uri.parse("https://sites.google.com/view/big-wheel-term-condition/home"));
                    startActivity(myIntent);
                } catch (ActivityNotFoundException e) {
                    Toast.makeText(GoogleLogin.this, "No application can handle this request."
                            + " Please install a webbrowser",  Toast.LENGTH_SHORT).show();
                    e.printStackTrace();
                }
            }
        });
        tv_privacy.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                try {
                    Intent myIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(privacyUrl));
                    startActivity(myIntent);
                } catch (ActivityNotFoundException e) {
                    Toast.makeText(GoogleLogin.this, "No application can handle this request."
                            + " Please install a webbrowser",  Toast.LENGTH_SHORT).show();
                    e.printStackTrace();
                }
            }
        });

        textView12.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if(!accept.isChecked()) {
                    accept.setChecked(true);
                }else
                {
                    accept.setChecked(false);
                }
            }
        });
        textView13.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if(!accept.isChecked()) {
                    accept.setChecked(true);
                }else
                {
                    accept.setChecked(false);
                }
            }
        });

        gloginBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if(!accept.isChecked()) {
                    Toast.makeText(GoogleLogin.this, "Please Accept Terms & Conditions", Toast.LENGTH_SHORT).show();
                }
                else
                {
                    Intent signInIntent = mGoogleSignInClient.getSignInIntent();
                    startActivityForResult(signInIntent, RC_SIGN_IN);

                }
            }
        });
    }
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == RC_SIGN_IN) {
            Task<GoogleSignInAccount> task = GoogleSignIn.getSignedInAccountFromIntent(data);

            handleSignInResult(task);
        }
    }
    private void handleSignInResult(Task<GoogleSignInAccount> completedTask) {
        try {

            GoogleSignInAccount account = completedTask.getResult(ApiException.class);

            GoogleSignInAccount acct = GoogleSignIn.getLastSignedInAccount(GoogleLogin.this);

            if (acct != null) {
                String personName = acct.getDisplayName();
                String personEmail = acct.getEmail();
                String personId = acct.getId();
                Uri personPhoto = acct.getPhotoUrl();
                Log.d("myTag", "This is my message"+personName+personEmail+personId+personPhoto);
                editor.putString("uname",personName);
                editor.putString("uemail",personEmail);
                editor.putString("uid",personId);
                if(personPhoto!=null)
                    editor.putString("upic",personPhoto.toString());
                else
                    editor.putString("upic",null);
                editor.apply();


                ProgressDialog progressDialog = new ProgressDialog(GoogleLogin.this);
                progressDialog.setCancelable(false);
                progressDialog.setMessage("Please wait...");
                progressDialog.show();



                RequestQueue queue = Volley.newRequestQueue(this);

                StringRequest stringRequest = new StringRequest(Request.Method.POST, pref.getString("baseurl","")+"registration.php",
                        new Response.Listener<String>() {
                            @Override
                            public void onResponse(String response) {

                                progressDialog.dismiss();
                                if(response.equals("2"))
                                {

                                    new AlertDialog.Builder(GoogleLogin.this)
                                            .setTitle("Error!")
                                            .setCancelable(false)
                                            .setMessage("Something went wrong!")
                                            .setPositiveButton("Retry", new DialogInterface.OnClickListener() {
                                                public void onClick(DialogInterface dialog, int which) {
                                                    // Continue with delete operation
                                                    Intent intent = new Intent(GoogleLogin.this, GoogleLogin.class);
                                                    startActivity(intent);
                                                    finish();
                                                }
                                            }).setNegativeButton("Exit", new DialogInterface.OnClickListener() {
                                        @Override
                                        public void onClick(DialogInterface dialogInterface, int i) {
                                            finish();
                                        }
                                    }).setIcon(android.R.drawable.ic_dialog_alert)
                                            .show();
                                }else{

                                    if(response.contains("spindex"))
                                    {
                                        String[] strr=response.split("=");
                                        int value = Integer.parseInt(strr[1]);

                                        editor.putInt("spindex",value);
                                        editor.apply();
                                    }else
                                    {
                                        editor.putInt("spindex",0);
                                        editor.apply();
                                    }
                                    Intent intent = new Intent(GoogleLogin.this, MainActivity.class);
                                    startActivity(intent);
                                    finish();

                                }
                            }
                        }, new Response.ErrorListener() {
                    @Override
                    public void onErrorResponse(VolleyError error) {
                        progressDialog.dismiss();

                        new AlertDialog.Builder(GoogleLogin.this)
                                .setTitle("Error!")
                                .setMessage("Check Your Internet Connection")
                                .setCancelable(false)
                                .setPositiveButton("Retry", new DialogInterface.OnClickListener() {
                                    public void onClick(DialogInterface dialog, int which) {
                                        // Continue with delete operation
                                        Intent intent = new Intent(GoogleLogin.this, GoogleLogin.class);
                                        startActivity(intent);
                                        finish();
                                    }
                                })
                                // A null listener allows the button to dismiss the dialog and take no further action.
                                .setNegativeButton("Exit", new DialogInterface.OnClickListener() {
                                    @Override
                                    public void onClick(DialogInterface dialogInterface, int i) {
                                        finish();
                                    }
                                })
                                .setIcon(android.R.drawable.ic_dialog_alert)
                                .show();
                    }
                }) {
                    @Override
                    protected Map<String, String> getParams() {
                        Map<String, String> params = new HashMap<String, String>();
                        params.put("uname", personName);
                        params.put("email", personEmail);
                        params.put("uid", personId);
                        params.put("img", String.valueOf(personPhoto));
                        return params;
                    }
                };

                queue.add(stringRequest);

            }


        } catch (ApiException e) {
            // The ApiException status code indicates the detailed failure reason.
            // Please refer to the GoogleSignInStatusCodes class reference for more information.
            Log.d("err", e.getMessage());

        }
    }

    private boolean isNetworkConnected() {
        ConnectivityManager cm = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);

        return cm.getActiveNetworkInfo() != null && cm.getActiveNetworkInfo().isConnected();
    }
}