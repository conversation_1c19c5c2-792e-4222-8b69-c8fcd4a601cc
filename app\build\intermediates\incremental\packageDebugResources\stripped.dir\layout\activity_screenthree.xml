<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"

    android:gravity="center"
    tools:context="splash.screenthree">


    <TextView
        android:id="@+id/textView2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="Select Language"
        android:layout_weight="1"
        android:textSize="40sp" />




    <RadioButton
        android:id="@+id/radioButton11"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="30dp"
        android:layout_weight="1"
        android:text="Google" />

    <RadioButton
        android:id="@+id/radioButton12"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="48dp"
        android:textSize="30dp"
        android:layout_weight="1"
        android:text="Yahoo" />
    <RadioButton
        android:id="@+id/radioButton14"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="48dp"
        android:textSize="30dp"
        android:layout_weight="1"
        android:text="Wikipedia" />
    <RadioButton
        android:id="@+id/radioButton13"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="48dp"
        android:textSize="30dp"
        android:layout_weight="1"
        android:text="Bing" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="bottom"
        android:orientation="vertical">

        <Button
            android:id="@+id/nextbtn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#000000"
            android:text="Next"
            android:textColor="#FFFFFF"
            android:textSize="24sp" />
    </LinearLayout>

</LinearLayout>

