package com.bigwheel.winners.pay;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.bigwheel.winners.R;

import java.util.ArrayList;

public class PaymentViaUPI extends AppCompatActivity {

//    SharedPreferences pref;
//    String amount="50";
//    final int UPI_PAYMENT = 0;
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.activity_payment_via_u_p_i);
//
//        pref=getSharedPreferences("mypref",MODE_PRIVATE);
//        amount = getIntent().getStringExtra("amount");
////pref.getString("upiid","")
//        Uri uri = Uri.parse("upi://pay").buildUpon()
//                .appendQueryParameter("pa", "BHARATPE09894529428@yesbankltd")
//                .appendQueryParameter("pn", pref.getString("uname","Spin Win"))
//                //.appendQueryParameter("mc", "Payee merchant code")
//                .appendQueryParameter("tr", "" + System.currentTimeMillis())
//                .appendQueryParameter("tn", "Pay for spin win")
//                .appendQueryParameter("am",   "1.00")
//                .appendQueryParameter("cu", "INR").build();
//
//
//        Intent upiPayIntent = new Intent(Intent.ACTION_VIEW);
//        upiPayIntent.setData(uri);
//        // will always show a dialog to user to choose an app
//        Intent chooser = Intent.createChooser(upiPayIntent, "Pay with");
//        // check if intent resolves
//        if (null != chooser.resolveActivity(getPackageManager())) {
//            startActivityForResult(chooser, UPI_PAYMENT);
//        } else {
//            Toast.makeText(PaymentViaUPI.this, "No UPI app found, please install one to continue", Toast.LENGTH_SHORT).show();
//        }
//    }
//    @Override
//    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
//        super.onActivityResult(requestCode, resultCode, data);
//
//        switch (requestCode) {
//            case UPI_PAYMENT:
//                if ((RESULT_OK == resultCode) || (resultCode == 11)) {
//                    if (data != null) {
//                        String trxt = data.getStringExtra("response");
//                        Log.d("UPI", "onActivityResult: " + trxt);
//                        ArrayList<String> dataList = new ArrayList<>();
//                        dataList.add(trxt);
//                        upiPaymentDataOperation(dataList);
//                        Toast.makeText(this, "asdfghjk", Toast.LENGTH_SHORT).show();
//                    } else {
//                        Log.d("UPI", "onActivityResult: " + "Return data is null");
//                        ArrayList<String> dataList = new ArrayList<>();
//                        dataList.add("nothing");
//                        upiPaymentDataOperation(dataList);
//                    }
//                } else {
//                    Log.d("UPI", "onActivityResult: " + "Return data is null"); //when user simply back without payment
//                    ArrayList<String> dataList = new ArrayList<>();
//                    dataList.add("nothing");
//                    upiPaymentDataOperation(dataList);
//                }
//                break;
//        }
//    }
//
//    public static boolean isConnectionAvailable(Context context) {
//        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
//        if (connectivityManager != null) {
//            NetworkInfo netInfo = connectivityManager.getActiveNetworkInfo();
//            if (netInfo != null && netInfo.isConnected()
//                    && netInfo.isConnectedOrConnecting()
//                    && netInfo.isAvailable()) {
//                return true;
//            }
//        }
//        return false;
//    }
//
//    private void upiPaymentDataOperation(ArrayList<String> data) {
//        if (isConnectionAvailable(PaymentViaUPI.this)) {
//            String str = data.get(0);
//            Log.d("UPIPAY", "upiPaymentDataOperation: "+str);
//            String paymentCancel = "";
//            if (str == null) str = "discard";
//            String status = "";
//            String approvalRefNo = "";
//            String response[] = str.split("&");
//            for (int i = 0; i < response.length; i++) {
//                String equalStr[] = response[i].split("=");
//                if (equalStr.length >= 2) {
//                    if (equalStr[0].toLowerCase().equals("Status".toLowerCase())) {
//                        status = equalStr[1].toLowerCase();
//                    } else if (equalStr[0].toLowerCase().equals("ApprovalRefNo".toLowerCase()) || equalStr[0].toLowerCase().equals("txnRef".toLowerCase())) {
//                        approvalRefNo = equalStr[1];
//                    }
//                } else {
//                    paymentCancel = "Payment cancelled by user.";
//                }
//            }
//
//            if (status.equals("success")) {
//                //Code to handle successful transaction here.
//                //Toast.makeText(MainActivity.this, "Transaction successful.", Toast.LENGTH_SHORT).show();
//                // Log.d("UPI", "responseStr: "+approvalRefNo);
//                //Toast.makeText(this, "YOUR ORDER HAS BEEN PLACED\n THANK YOU AND ORDER AGAIN", Toast.LENGTH_LONG).show();
//                Intent intent = new Intent(PaymentViaUPI.this, PaymentResult.class);
//                intent.putExtra("status", "success");
//                intent.putExtra("myamount",amount );
//                startActivity(intent);
//                finish();
//            } else if ("Payment cancelled by user.".equals(paymentCancel)) {
//                 Toast.makeText(PaymentViaUPI.this, "Payment cancelled by user.", Toast.LENGTH_SHORT).show();
//                Intent intent = new Intent(PaymentViaUPI.this, PaymentResult.class);
//                intent.putExtra("status", "cancel");
//                intent.putExtra("myamount",amount );
//                startActivity(intent);
//                finish();
//            } else {
//                Toast.makeText(PaymentViaUPI.this, "Transaction failed.Please try again", Toast.LENGTH_SHORT).show();
//                Intent intent = new Intent(PaymentViaUPI.this, PaymentResult.class);
//                intent.putExtra("status", "fail");
//                intent.putExtra("myamount",amount );
//                startActivity(intent);
//                finish();
//            }
//        } else {
//            Toast.makeText(PaymentViaUPI.this, "Internet connection is not available. Please check and try again", Toast.LENGTH_SHORT).show();
//        }
//    }

    SharedPreferences pref;
    String amount="50";
    final int UPI_PAYMENT = 0;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_payment_via_u_p_i);
        getSupportActionBar().hide();
        pref=getSharedPreferences("mypref",MODE_PRIVATE);
        amount = getIntent().getStringExtra("amount");

        Uri uri = Uri.parse("upi://pay").buildUpon()
                .appendQueryParameter("pa",pref.getString("upiid","") )
                .appendQueryParameter("pn", pref.getString("uname","Spin Win"))
                //.appendQueryParameter("mc", "Payee merchant code")
                .appendQueryParameter("tr", "" + System.currentTimeMillis())
                .appendQueryParameter("tn", "Pay for spin win")
                .appendQueryParameter("am", amount)
                .appendQueryParameter("cu", "INR").build();

        // Log.d("uribyupi", "onCreate: "+uri);
        Intent upiPayIntent = new Intent(Intent.ACTION_VIEW);
        upiPayIntent.setData(uri);
        // will always show a dialog to user to choose an app
        Intent chooser = Intent.createChooser(upiPayIntent, "Pay with");

        // check if intent resolves
        if (null != chooser.resolveActivity(getPackageManager())) {
            startActivityForResult(chooser, UPI_PAYMENT);
        } else {
            Toast.makeText(PaymentViaUPI.this, "No UPI app found, please install one to continue", Toast.LENGTH_SHORT).show();
        }
    }
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        switch (requestCode) {
            case UPI_PAYMENT:
                if ((RESULT_OK == resultCode) || (resultCode == 11)) {
                    if (data != null) {
                        String trxt = data.getStringExtra("response");
                        Log.d("UPI", "onActivityResult: " + trxt);
                        ArrayList<String> dataList = new ArrayList<>();
                        dataList.add(trxt);
                        upiPaymentDataOperation(dataList);
                    } else {
                        //Log.d("UPI", "onActivityResult: " + "Return data is null");
                        ArrayList<String> dataList = new ArrayList<>();
                        dataList.add("nothing");
                        upiPaymentDataOperation(dataList);
                    }
                } else {
                    //Log.d("UPI", "onActivityResult: " + "Return data is null"); //when user simply back without payment
                    ArrayList<String> dataList = new ArrayList<>();
                    dataList.add("nothing");
                    upiPaymentDataOperation(dataList);
                }
                break;
        }
    }

    public static boolean isConnectionAvailable(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo netInfo = connectivityManager.getActiveNetworkInfo();
            if (netInfo != null && netInfo.isConnected()
                    && netInfo.isConnectedOrConnecting()
                    && netInfo.isAvailable()) {
                return true;
            }
        }
        return false;
    }

    private void upiPaymentDataOperation(ArrayList<String> data) {
        if (isConnectionAvailable(PaymentViaUPI.this)) {
            String str = data.get(0);

            Log.d("UPIPAY", "upiPaymentDataOperation: "+str);
            String paymentCancel = "";
            if (str == null) str = "discard";
            String status = "";
            String approvalRefNo = "";
            String response[] = str.split("&");
            for (int i = 0; i < response.length; i++) {
                String equalStr[] = response[i].split("=");
                if (equalStr.length >= 2) {
                    if (equalStr[0].toLowerCase().equals("Status".toLowerCase())) {
                        status = equalStr[1].toLowerCase();
                    } else if (equalStr[0].toLowerCase().equals("ApprovalRefNo".toLowerCase()) || equalStr[0].toLowerCase().equals("txnRef".toLowerCase())) {
                        approvalRefNo = equalStr[1];
                    }
                } else {
                    paymentCancel = "Payment cancelled by user.";
                }
            }

            if (status.equals("success")) {
                //Code to handle successful transaction here.
                Toast.makeText(PaymentViaUPI.this, "Transaction successful.", Toast.LENGTH_SHORT).show();
                // Log.d("UPI", "responseStr: "+approvalRefNo);
                //Toast.makeText(this, "YOUR ORDER HAS BEEN PLACED\n THANK YOU AND ORDER AGAIN", Toast.LENGTH_LONG).show();
                Intent intent = new Intent(PaymentViaUPI.this, PaymentResult.class);
                intent.putExtra("status", "success");
                intent.putExtra("myamount",amount );
                startActivity(intent);
                finish();
            } else if ("Payment cancelled by user.".equals(paymentCancel)) {
                Toast.makeText(PaymentViaUPI.this, "Payment cancelled by user.", Toast.LENGTH_SHORT).show();
                Intent intent = new Intent(PaymentViaUPI.this, PaymentResult.class);
                intent.putExtra("status", "cancel");
                intent.putExtra("myamount",amount );
                startActivity(intent);
                finish();
            } else {
                Toast.makeText(PaymentViaUPI.this, "Transaction failed.Please try again", Toast.LENGTH_SHORT).show();
                Intent intent = new Intent(PaymentViaUPI.this, PaymentResult.class);
                intent.putExtra("status", "fail");
                intent.putExtra("myamount",amount );
                startActivity(intent);
                finish();
            }
        } else {
            Toast.makeText(PaymentViaUPI.this, "Internet connection is not available. Please check and try again", Toast.LENGTH_SHORT).show();
        }
    }



}