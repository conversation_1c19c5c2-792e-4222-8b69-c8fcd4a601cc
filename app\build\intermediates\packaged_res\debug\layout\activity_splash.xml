<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/themeBgColor"
    tools:context=".splash.splash">


    <TextView
        android:id="@+id/linearLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/stardosstencil"
        android:text="BIG WHEEL\nEARN\nDAILY REWARD"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="@dimen/_30sdp"

        app:layout_constraintBottom_toTopOf="@+id/imageView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


    </TextView>


    <ImageView
        android:id="@+id/imageView"
        android:layout_width="280dp"
        android:layout_height="280dp"
        android:src="@drawable/spinbg4"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.72" />



</androidx.constraintlayout.widget.ConstraintLayout>