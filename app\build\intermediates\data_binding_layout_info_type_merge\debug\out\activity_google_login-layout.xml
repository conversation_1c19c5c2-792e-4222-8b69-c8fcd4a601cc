<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_google_login" modulePackage="com.bigwheel.winners" filePath="app\src\main\res\layout\activity_google_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_google_login_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="290" endOffset="51"/></Target><Target id="@+id/checkbox_terms" view="CheckBox"><Expressions/><location startLine="18" startOffset="4" endLine="27" endOffset="69"/></Target><Target id="@+id/textView12" view="TextView"><Expressions/><location startLine="29" startOffset="4" endLine="39" endOffset="66"/></Target><Target id="@+id/tv_privacy" view="TextView"><Expressions/><location startLine="41" startOffset="4" endLine="50" endOffset="62"/></Target><Target id="@+id/textView13" view="TextView"><Expressions/><location startLine="52" startOffset="4" endLine="60" endOffset="65"/></Target><Target id="@+id/tv_terms" view="TextView"><Expressions/><location startLine="62" startOffset="4" endLine="71" endOffset="62"/></Target><Target id="@+id/iv_googlelogin" view="LinearLayout"><Expressions/><location startLine="73" startOffset="4" endLine="101" endOffset="18"/></Target><Target id="@+id/textView3" view="TextView"><Expressions/><location startLine="111" startOffset="4" endLine="124" endOffset="53"/></Target><Target id="@+id/constraintLayout2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="126" startOffset="4" endLine="249" endOffset="55"/></Target><Target id="@+id/textView4" view="TextView"><Expressions/><location startLine="141" startOffset="8" endLine="152" endOffset="57"/></Target><Target id="@+id/etNumber" view="EditText"><Expressions/><location startLine="154" startOffset="8" endLine="168" endOffset="68"/></Target><Target id="@+id/textView5" view="TextView"><Expressions/><location startLine="170" startOffset="8" endLine="181" endOffset="67"/></Target><Target id="@+id/etPassword" view="EditText"><Expressions/><location startLine="183" startOffset="8" endLine="197" endOffset="68"/></Target><Target id="@+id/tvForgotPassword" view="TextView"><Expressions/><location startLine="199" startOffset="8" endLine="211" endOffset="69"/></Target><Target id="@+id/btnSignIn" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="213" startOffset="8" endLine="238" endOffset="59"/></Target><Target id="@+id/textView2" view="TextView"><Expressions/><location startLine="225" startOffset="12" endLine="237" endOffset="61"/></Target><Target id="@+id/tv_or" view="TextView"><Expressions/><location startLine="240" startOffset="8" endLine="248" endOffset="68"/></Target><Target id="@+id/hr" view="View"><Expressions/><location startLine="251" startOffset="4" endLine="261" endOffset="57"/></Target><Target id="@+id/textView6" view="TextView"><Expressions/><location startLine="263" startOffset="4" endLine="275" endOffset="57"/></Target><Target id="@+id/btnSignUp" view="TextView"><Expressions/><location startLine="277" startOffset="4" endLine="289" endOffset="64"/></Target></Targets></Layout>