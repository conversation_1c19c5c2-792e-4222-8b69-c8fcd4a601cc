<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/ic_rounded_theme_white"
    android:orientation="vertical"
    android:padding="30dp"
    android:gravity="center">

    <!-- Success Icon -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="✅"
        android:textSize="60sp"
        android:layout_marginBottom="20dp" />

    <!-- Success Title -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Payment Successful!"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/premium_gold"
        android:layout_marginBottom="15dp" />

    <!-- Success Message -->
    <TextView
        android:id="@+id/success_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Your payment has been processed successfully. Coins will be added to your account shortly."
        android:textSize="16sp"
        android:textColor="@color/text_secondary"
        android:gravity="center"
        android:layout_marginBottom="25dp" />

    <!-- OK Button -->
    <Button
        android:id="@+id/btn_ok"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:text="Continue"
        android:textColor="@color/white"
        android:background="@drawable/bg_premium_button"
        android:textSize="16sp"
        android:textStyle="bold" />

</LinearLayout>
