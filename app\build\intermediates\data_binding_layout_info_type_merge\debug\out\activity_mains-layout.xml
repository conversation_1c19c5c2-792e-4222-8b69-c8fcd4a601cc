<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_mains" modulePackage="com.bigwheel.winners" filePath="app\src\main\res\layout\activity_mains.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout" rootNodeViewId="@+id/clMain"><Targets><Target id="@+id/clMain" tag="layout/activity_mains_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="653" endOffset="53"/></Target><Target tag="binding_1" view="RelativeLayout"><Expressions/><location startLine="557" startOffset="20" endLine="592" endOffset="36"/></Target><Target id="@+id/layout_si_details" tag="binding_1" include="layout_si_details"><Expressions/><location startLine="584" startOffset="24" endLine="591" endOffset="55"/></Target><Target id="@+id/pay_now_button" view="Button"><Expressions/><location startLine="26" startOffset="16" endLine="35" endOffset="44"/></Target><Target id="@+id/tilKey" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="60" startOffset="24" endLine="80" endOffset="79"/></Target><Target id="@+id/etKey" view="EditText"><Expressions/><location startLine="69" startOffset="28" endLine="79" endOffset="57"/></Target><Target id="@+id/tilSalt" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="82" startOffset="24" endLine="102" endOffset="79"/></Target><Target id="@+id/etSalt" view="EditText"><Expressions/><location startLine="91" startOffset="28" endLine="101" endOffset="57"/></Target><Target id="@+id/tilAmount" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="104" startOffset="24" endLine="122" endOffset="79"/></Target><Target id="@+id/etAmount" view="EditText"><Expressions/><location startLine="112" startOffset="28" endLine="121" endOffset="57"/></Target><Target id="@+id/tilMerchantName" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="124" startOffset="24" endLine="144" endOffset="79"/></Target><Target id="@+id/etMerchantName" view="EditText"><Expressions/><location startLine="133" startOffset="28" endLine="143" endOffset="57"/></Target><Target id="@+id/tilPhone" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="146" startOffset="24" endLine="166" endOffset="79"/></Target><Target id="@+id/etPhone" view="EditText"><Expressions/><location startLine="155" startOffset="28" endLine="165" endOffset="57"/></Target><Target id="@+id/tilUserCredential" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="168" startOffset="24" endLine="188" endOffset="79"/></Target><Target id="@+id/etUserCredential" view="EditText"><Expressions/><location startLine="177" startOffset="28" endLine="187" endOffset="57"/></Target><Target id="@+id/tilSurl" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="190" startOffset="24" endLine="207" endOffset="79"/></Target><Target id="@+id/etSurl" view="EditText"><Expressions/><location startLine="198" startOffset="28" endLine="206" endOffset="57"/></Target><Target id="@+id/tilFurl" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="209" startOffset="24" endLine="226" endOffset="79"/></Target><Target id="@+id/etFurl" view="EditText"><Expressions/><location startLine="217" startOffset="28" endLine="225" endOffset="57"/></Target><Target id="@+id/tilSurePayCount" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="228" startOffset="24" endLine="248" endOffset="79"/></Target><Target id="@+id/etSurePayCount" view="EditText"><Expressions/><location startLine="236" startOffset="28" endLine="247" endOffset="57"/></Target><Target id="@+id/switchHideCbToolBar" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="286" startOffset="32" endLine="290" endOffset="69"/></Target><Target id="@+id/switchAutoSelectOtp" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="307" startOffset="32" endLine="311" endOffset="69"/></Target><Target id="@+id/switchAutoApprove" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="328" startOffset="32" endLine="332" endOffset="69"/></Target><Target id="@+id/switchEnableReviewOrder" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="358" startOffset="32" endLine="362" endOffset="69"/></Target><Target id="@+id/switchDiableCBDialog" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="380" startOffset="32" endLine="384" endOffset="69"/></Target><Target id="@+id/switchDiableUiDialog" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="402" startOffset="32" endLine="406" endOffset="69"/></Target><Target id="@+id/switchShowGooglePay" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="436" startOffset="36" endLine="440" endOffset="73"/></Target><Target id="@+id/switchShowPhonePe" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="457" startOffset="36" endLine="461" endOffset="73"/></Target><Target id="@+id/switchShowPaytm" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="478" startOffset="36" endLine="482" endOffset="73"/></Target><Target id="@+id/rlReviewOrder" view="RelativeLayout"><Expressions/><location startLine="496" startOffset="24" endLine="529" endOffset="40"/></Target><Target id="@+id/btnAddItem" view="Button"><Expressions/><location startLine="503" startOffset="28" endLine="513" endOffset="57"/></Target><Target id="@+id/rvReviewOrder" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="523" startOffset="28" endLine="527" endOffset="65"/></Target><Target id="@+id/rl_si_header" view="RelativeLayout"><Expressions/><location startLine="561" startOffset="24" endLine="582" endOffset="40"/></Target><Target id="@+id/tv_pay_via_si" view="TextView"><Expressions/><location startLine="566" startOffset="28" endLine="573" endOffset="58"/></Target><Target id="@+id/switch_si_on_off" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="575" startOffset="28" endLine="580" endOffset="57"/></Target><Target id="@+id/radioGrpEnv" view="RadioGroup"><Expressions/><location startLine="612" startOffset="24" endLine="637" endOffset="36"/></Target><Target id="@+id/radioBtnTest" view="androidx.appcompat.widget.AppCompatRadioButton"><Expressions/><location startLine="622" startOffset="28" endLine="628" endOffset="61"/></Target><Target id="@+id/radioBtnProduction" view="androidx.appcompat.widget.AppCompatRadioButton"><Expressions/><location startLine="630" startOffset="28" endLine="636" endOffset="59"/></Target></Targets></Layout>