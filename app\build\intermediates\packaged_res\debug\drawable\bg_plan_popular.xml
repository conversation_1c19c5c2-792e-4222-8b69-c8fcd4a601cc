<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Selected State -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <gradient
                android:angle="45"
                android:startColor="@color/plan_popular"
                android:endColor="@color/white"
                android:type="linear" />
            <corners android:radius="16dp" />
            <stroke
                android:width="3dp"
                android:color="@color/plan_popular_border" />
            <padding
                android:left="16dp"
                android:top="16dp"
                android:right="16dp"
                android:bottom="16dp" />
        </shape>
    </item>
    
    <!-- Normal State -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/white" />
            <corners android:radius="16dp" />
            <stroke
                android:width="2dp"
                android:color="@color/plan_popular_border" />
            <padding
                android:left="16dp"
                android:top="16dp"
                android:right="16dp"
                android:bottom="16dp" />
        </shape>
    </item>
    
</selector>
