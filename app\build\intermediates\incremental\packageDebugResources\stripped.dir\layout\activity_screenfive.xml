<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="splash.screenfive">



        <TextView
            android:id="@+id/textView3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Privacy Policy"
            android:textAlignment="center"
            android:textColor="#77B7EA"
            android:textSize="34sp" />

        <TextView
            android:id="@+id/textView4"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="        When you use Our applications on Android, This Privacy Policy describes the information collected by us and how we use that information. Your privacy is important to us. Sometimes we need information to provide services that you request.\n\n      I want to inform you that whenever you use my Service, in a case of an error in the app I collect data and information (through third-party products) on your phone called Log Data. This Log Data may include information such as your device Internet Protocol (“IP”) address, device name, operating system version, the configuration of the app when utilizing my Service, the time and date of your use of the Service, and other statistics.\n\n      Full network access: This permission is used  to access the device's network for certain functions including receiving update notifications or accessing app classification labels.  Connect and disconnect from Wi-Fi: This permission is used in settings and notification toolbar in order to connect and disconnect from Wi-Fi."

            android:textAlignment="center"
            android:textSize="16sp" />


        <Button
            android:id="@+id/agreebtn"
            android:layout_width="match_parent"
            android:layout_height="63dp"
            android:background="#000000"
            android:text="Agree And next"
            android:textColor="#FFFFFF" />

    </LinearLayout>
