<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drwa"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/themeBgColor"
    android:orientation="vertical"
    tools:context="splash.screenOne">

    <!--    Details Layout-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

                <LinearLayout
                    android:id="@+id/linearLayout5"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"

                    android:gravity="center_horizontal"
                    android:orientation="vertical"
                    android:paddingBottom="5dp"
                    android:visibility="visible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintBottom_toTopOf="@+id/linearLayout2"
                        app:layout_constraintTop_toTopOf="parent">

<!--                        <ImageView-->
<!--                            android:id="@+id/menu"-->
<!--                            android:layout_width="35dp"-->
<!--                            android:layout_height="35dp"-->
<!--                            android:layout_centerVertical="true"-->
<!--                            android:layout_marginStart="10dp"-->
<!--                            android:src="@drawable/ic_menu"-->
<!--                            app:tint="@color/newWhitr"/>-->

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/textView7"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="5dp"
                                android:fontFamily="@font/poppins_bold"
                                android:includeFontPadding="false"
                                android:text="Entertainment"
                                android:textColor="@color/newWhitr"
                                android:textSize="20dp"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/textView8"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="5dp"
                                android:fontFamily="@font/poppins"
                                android:includeFontPadding="false"
                                android:text="Big Wheel"
                                android:textColor="@color/white"
                                android:textSize="14dp"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/textView7" />
                        </LinearLayout>
                    </RelativeLayout>



                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:visibility="gone">

                        <Switch
                            android:id="@+id/swith"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:gravity="center"
                            android:text="Sound"
                            android:textSize="20dp"
                            android:textStyle="bold"></Switch>
                    </RelativeLayout>
                </LinearLayout>


<!--        <TextView-->
<!--            android:id="@+id/scroller"-->
<!--            android:layout_width="0dp"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginStart="24dp"-->
<!--            android:layout_marginTop="5dp"-->
<!--            android:layout_marginEnd="24dp"-->
<!--            android:background="@drawable/ic_rounded_theme_yellow"-->
<!--            android:ellipsize="marquee"-->
<!--            android:padding="1dp"-->
<!--            android:singleLine="true"-->
<!--            android:text="@string/winlist"-->
<!--            android:textAppearance="?android:attr/textAppearanceLarge"-->
<!--            android:textColor="@color/white"-->
<!--            android:textSize="15sp"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            app:layout_constraintTop_toBottomOf="@+id/linearLayout5" />-->

        <LinearLayout
            android:id="@+id/linearLayout4"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:layout_marginTop="40dp"
            app:layout_constraintTop_toBottomOf="@+id/linearLayout5">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/wheelLayout"
                android:layout_width="300dp"
                android:layout_height="300dp"
                app:layout_constraintBottom_toTopOf="@+id/btnSpin"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/constraintLayout">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="22dp"
                        android:src="@drawable/wheels">

                    </ImageView>

                    <ImageView
                        android:id="@+id/cursorView"
                        android:layout_width="47dp"
                        android:layout_height="47dp"
                        android:src="@drawable/teer2"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="15dp" />

                    <ImageView
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_gravity="center"
                        android:layout_marginTop="10dp"
                        android:src="@drawable/wheelss"
                        android:visibility="gone"></ImageView>

                    <rubikstudio.library.LuckyWheelView
                        android:id="@+id/luckyWheel"
                        android:layout_width="300dp"
                        android:layout_height="300dp"
                        app:lkwBackgroundColor="@android:color/transparent"
                        app:lkwCenterImage="@drawable/wheels"
                        app:lkwCursor="@drawable/teer2" />
                </FrameLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>

            <!--        Add Money Layout-->

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/addMoneyLayout"
                android:layout_width="53dp"
                android:layout_height="13dp"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="20dp"
                android:background="@drawable/ic_rounded_theme_white"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/constraintLayout">

                <ImageView
                    android:id="@+id/btn_closeaddmoney"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="5dp"
                    android:src="@drawable/ic_baseline_close_24"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/textView9"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="25dp"
                    android:fontFamily="@font/poppins"
                    android:text=""
                    android:textSize="18dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <EditText
                    android:id="@+id/etDeposite"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginLeft="25dp"
                    android:layout_marginTop="22dp"
                    android:layout_marginRight="24dp"
                    android:background="@drawable/ic_rounded_theme_white_black"
                    android:editable="false"
                    android:hint="Select Deposite Amount"
                    android:padding="5dp"
                    android:textAlignment="center"
                    android:textColor="@color/black"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView9" />

                <LinearLayout
                    android:id="@+id/linearLayout3"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginLeft="25dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginRight="24dp"
                    android:orientation="horizontal"
                    android:weightSum="3"
                    app:layout_constraintTop_toBottomOf="@+id/etDeposite"
                    tools:layout_editor_absoluteX="25dp">

                    <LinearLayout
                        android:id="@+id/deposite_50"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginRight="11dp"
                        android:layout_weight="1"
                        android:background="@drawable/ic_rounded_theme_white_black"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/price1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/poppins"
                            android:text=""
                            android:textColor="#666666"
                            android:textSize="20dp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/deposite_100"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@drawable/ic_rounded_theme_white_black"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/price2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/poppins"
                            android:text=""
                            android:textColor="#666666"
                            android:textSize="20dp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/deposite_150"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="11dp"
                        android:layout_weight="1"
                        android:background="@drawable/ic_rounded_theme_white_black"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/price3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/poppins"
                            android:text=""
                            android:textColor="#666666"
                            android:textSize="20dp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/deposite_200"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="11dp"
                        android:layout_weight="1"
                        android:background="@drawable/ic_rounded_theme_white_black"
                        android:gravity="center"
                        android:visibility="gone">

                        <TextView
                            android:id="@+id/price4"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/poppins"
                            android:text=""
                            android:textColor="#666666"
                            android:textSize="20dp" />
                    </LinearLayout>


                    <!--    <LinearLayout
                            android:id="@+id/deposite_200"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/ic_rounded_theme_white_black"
                            android:gravity="center"
                            android:layout_weight="1"
                            android:padding="10dp">

                            <TextView
                                android:id="@+id/price4"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:fontFamily="@font/font"
                                android:text=""
                                android:textColor="#666666"
                                android:textSize="20dp" />
                        </LinearLayout>-->
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/btnDeposite"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginLeft="24dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginRight="24dp"
                    android:background="@drawable/cardbackground"
                    android:backgroundTint="@color/themeColor"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/linearLayout3">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:gravity="center"
                        android:text="Deposit"
                        android:textColor="@color/white"
                        android:textSize="20dp"
                        android:textStyle="bold" />
                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

             WithDraw Layout

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/withdrawMoneyLayout"
                android:layout_width="match_parent"
                android:layout_height="290dp"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="20dp"
                android:background="@drawable/ic_rounded_theme_white"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/addMoneyLayout">

                <TextView
                    android:id="@+id/textView11"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="25dp"
                    android:fontFamily="@font/poppins"
                    android:text=""
                    android:textSize="18dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/btn_closewithdraw"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="5dp"
                    android:src="@drawable/ic_baseline_close_24"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:layout_width="49dp"
                    android:layout_height="49dp"
                    android:layout_marginRight="1dp"
                    android:src="@drawable/ic_paytm"
                    app:layout_constraintBottom_toBottomOf="@+id/etWithdraw"
                    app:layout_constraintEnd_toStartOf="@+id/etWithdraw"
                    app:layout_constraintTop_toTopOf="@+id/etWithdraw" />

                <EditText
                    android:id="@+id/etWithdraw"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginLeft="75dp"
                    android:layout_marginTop="22dp"
                    android:layout_marginRight="24dp"
                    android:maxLength="10"
                    android:background="@drawable/ic_rounded_theme_white_black"
                    android:hint="@string/phone_number"
                    android:inputType="number"
                    android:padding="5dp"
                    android:textColor="@color/black"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView11" />

                <EditText
                    android:id="@+id/etWithdrawAmmount"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginLeft="25dp"
                    android:layout_marginTop="22dp"
                    android:layout_marginRight="24dp"
                    android:background="@drawable/ic_rounded_theme_white_black"
                    android:hint="Enter Ammount"
                    android:inputType="number"
                    android:padding="5dp"
                    android:textColor="@color/black"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/etWithdraw" />

                <LinearLayout
                    android:id="@+id/btnWithdrawFinal"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@drawable/cardbackground"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    android:layout_marginLeft="24dp"
                    android:backgroundTint="@color/themeColor"
                    android:layout_marginTop="15dp"
                    android:layout_marginRight="24dp"
                    app:layout_constraintTop_toBottomOf="@+id/etWithdrawAmmount">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="Withdraw"
                        android:gravity="center"
                        android:textColor="@color/white"
                        android:textSize="20dp"
                        android:textStyle="bold">

                    </TextView>
                </LinearLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/linearLayout2"
            android:layout_width="match_parent"
            android:layout_height="70dp"
            android:orientation="horizontal"
            android:layout_marginTop="30dp"
            android:weightSum="2"
            app:layout_constraintBottom_toTopOf="@+id/linearLayout9"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/linearLayout4">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:paddingLeft="19dp"
                android:paddingRight="8.5dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    >

                    <TextView
                        android:id="@+id/tvDepositeAmmount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:fontFamily="@font/poppins_bold"
                        android:includeFontPadding="false"
                        android:text="0"
                        android:textColor="@color/white"
                        android:textSize="20dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:fontFamily="@font/poppins"
                        android:includeFontPadding="false"
                        android:text="Counts"
                        android:textColor="@color/white"
                        android:textSize="14dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvDepositeAmmount" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:layout_weight="0.01"
                android:background="@color/white"></LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:paddingLeft="8.5dp"
                android:paddingRight="19dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    >

                    <TextView
                        android:id="@+id/tvWiningeAmmount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:fontFamily="@font/poppins_bold"
                        android:includeFontPadding="false"
                        android:text="0"
                        android:textColor="@color/white"
                        android:textSize="20dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:fontFamily="@font/poppins"
                        android:includeFontPadding="false"
                        android:text="Winnings"
                        android:textColor="@color/white"
                        android:textSize="14dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvWiningeAmmount" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/linearLayout9"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"

            app:layout_constraintTop_toBottomOf="@+id/linearLayout2">

            <LinearLayout
                android:id="@+id/btnAddMoneys"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="40dp"
                android:layout_weight="1"
                android:background="@drawable/cardbackground"
                app:layout_constraintTop_toBottomOf="@+id/linearLayout2">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:backgroundTint="@color/tran"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="Spin"
                    android:textColor="@color/themeColor"
                    android:textSize="16dp"
                    android:textStyle="bold" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/btnWithdraws"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="40dp"
                android:layout_weight="1"
                android:background="@drawable/cardbackground"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/btnAddMoneys">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:backgroundTint="@color/tran"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="Points"
                    android:textColor="@color/themeColor"
                    android:textSize="16dp"
                    android:textStyle="bold"></TextView>

            </LinearLayout>


        </LinearLayout>

        <!--    Spin Layout-->

        <LinearLayout
            android:id="@+id/linearLayout6"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
             android:layout_marginTop="40dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:gravity="center"
            android:visibility="visible"
            app:layout_constraintTop_toBottomOf="@+id/linearLayout9"
            tools:layout_editor_absoluteX="15dp">

            <LinearLayout
                android:id="@+id/btnSpin"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:layout_marginLeft="44dp"
                android:layout_marginRight="45dp"
                android:background="@drawable/roundedbutton"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:backgroundTintMode="add"
                    android:fontFamily="@font/poppins"
                    android:gravity="center"
                    android:text="SPIN"
                    android:textColor="@color/white"
                    android:textSize="20dp"
                    android:textStyle="bold"></TextView>

            </LinearLayout>


        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


    <!--<LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:background="@color/themeBgColor">
        <include layout="@layout/drawerlayout"></include>

    </LinearLayout>-->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:background="@color/themeBgColor"
        android:fitsSystemWindows="true"
        app:itemTextColor="@color/white"
        android:theme="@style/NavigationView"
        app:itemIconTint="@color/white"
        app:headerLayout="@layout/nav_header_navigation_menu"
        app:menu="@menu/activity_navigation_menu_drawer" />


</androidx.drawerlayout.widget.DrawerLayout>