<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.bigwheel.winners" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.drawerlayout.widget.DrawerLayout" rootNodeViewId="@+id/drawer_layout"><Targets><Target id="@+id/drawer_layout" tag="layout/activity_main_0" view="androidx.drawerlayout.widget.DrawerLayout"><Expressions/><location startLine="1" startOffset="0" endLine="655" endOffset="43"/></Target><Target id="@+id/constraintLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="16" startOffset="8" endLine="83" endOffset="59"/></Target><Target id="@+id/btn_menu" view="ImageView"><Expressions/><location startLine="26" startOffset="12" endLine="32" endOffset="59"/></Target><Target id="@+id/textView" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="45" endOffset="59"/></Target><Target id="@+id/tvDepositeAmmount" view="TextView"><Expressions/><location startLine="54" startOffset="16" endLine="62" endOffset="52"/></Target><Target id="@+id/tvWiningeAmmount" view="TextView"><Expressions/><location startLine="72" startOffset="16" endLine="79" endOffset="45"/></Target><Target id="@+id/linearLayout4" view="LinearLayout"><Expressions/><location startLine="86" startOffset="8" endLine="560" endOffset="22"/></Target><Target id="@+id/addMoneyLayout" view="ScrollView"><Expressions/><location startLine="99" startOffset="12" endLine="478" endOffset="24"/></Target><Target id="@+id/btn_closeaddmoney" view="ImageView"><Expressions/><location startLine="119" startOffset="20" endLine="130" endOffset="49"/></Target><Target id="@+id/textView9" view="TextView"><Expressions/><location startLine="133" startOffset="20" endLine="145" endOffset="67"/></Target><Target id="@+id/cvDiscountInfo" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="148" startOffset="20" endLine="206" endOffset="55"/></Target><Target id="@+id/etDeposite" view="EditText"><Expressions/><location startLine="209" startOffset="20" endLine="223" endOffset="83"/></Target><Target id="@+id/llPricingPlans" view="LinearLayout"><Expressions/><location startLine="226" startOffset="20" endLine="441" endOffset="34"/></Target><Target id="@+id/plan50" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="244" startOffset="28" endLine="287" endOffset="63"/></Target><Target id="@+id/price1" view="TextView"><Expressions/><location startLine="269" startOffset="36" endLine="276" endOffset="66"/></Target><Target id="@+id/plan100" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="290" startOffset="28" endLine="334" endOffset="63"/></Target><Target id="@+id/price2" view="TextView"><Expressions/><location startLine="315" startOffset="36" endLine="322" endOffset="66"/></Target><Target id="@+id/plan150" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="346" startOffset="28" endLine="390" endOffset="63"/></Target><Target id="@+id/price3" view="TextView"><Expressions/><location startLine="371" startOffset="36" endLine="378" endOffset="66"/></Target><Target id="@+id/plan200" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="393" startOffset="28" endLine="437" endOffset="63"/></Target><Target id="@+id/price4" view="TextView"><Expressions/><location startLine="418" startOffset="36" endLine="425" endOffset="66"/></Target><Target id="@+id/btnDeposite" view="LinearLayout"><Expressions/><location startLine="455" startOffset="24" endLine="474" endOffset="38"/></Target><Target id="@+id/withdrawMoneyLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="480" startOffset="12" endLine="558" endOffset="63"/></Target><Target id="@+id/btn_closewithdraw" view="ImageView"><Expressions/><location startLine="489" startOffset="16" endLine="497" endOffset="63"/></Target><Target id="@+id/textView11" view="TextView"><Expressions/><location startLine="499" startOffset="16" endLine="509" endOffset="63"/></Target><Target id="@+id/etWithdraw" view="EditText"><Expressions/><location startLine="511" startOffset="16" endLine="522" endOffset="75"/></Target><Target id="@+id/etWithdrawAmmount" view="EditText"><Expressions/><location startLine="524" startOffset="16" endLine="535" endOffset="75"/></Target><Target id="@+id/btnWithdrawFinal" view="LinearLayout"><Expressions/><location startLine="537" startOffset="16" endLine="556" endOffset="30"/></Target><Target id="@+id/wheelLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="563" startOffset="8" endLine="637" endOffset="59"/></Target><Target id="@+id/luckyWheel" view="ImageView"><Expressions/><location startLine="571" startOffset="12" endLine="579" endOffset="59"/></Target><Target id="@+id/btnAddMoney" view="LinearLayout"><Expressions/><location startLine="591" startOffset="16" endLine="611" endOffset="30"/></Target><Target id="@+id/btnSpin" view="LinearLayout"><Expressions/><location startLine="614" startOffset="16" endLine="633" endOffset="30"/></Target><Target id="@+id/nav_view" view="com.google.android.material.navigation.NavigationView"><Expressions/><location startLine="642" startOffset="4" endLine="653" endOffset="58"/></Target></Targets></Layout>