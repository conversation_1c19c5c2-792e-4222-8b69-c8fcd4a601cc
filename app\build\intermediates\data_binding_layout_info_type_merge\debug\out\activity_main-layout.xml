<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.bigwheel.winners" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.drawerlayout.widget.DrawerLayout" rootNodeViewId="@+id/drawer_layout"><Targets><Target id="@+id/drawer_layout" tag="layout/activity_main_0" view="androidx.drawerlayout.widget.DrawerLayout"><Expressions/><location startLine="1" startOffset="0" endLine="670" endOffset="43"/></Target><Target id="@+id/constraintLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="16" startOffset="8" endLine="83" endOffset="59"/></Target><Target id="@+id/btn_menu" view="ImageView"><Expressions/><location startLine="26" startOffset="12" endLine="32" endOffset="59"/></Target><Target id="@+id/textView" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="45" endOffset="59"/></Target><Target id="@+id/tvDepositeAmmount" view="TextView"><Expressions/><location startLine="54" startOffset="16" endLine="62" endOffset="52"/></Target><Target id="@+id/tvWiningeAmmount" view="TextView"><Expressions/><location startLine="72" startOffset="16" endLine="79" endOffset="45"/></Target><Target id="@+id/linearLayout4" view="LinearLayout"><Expressions/><location startLine="86" startOffset="8" endLine="575" endOffset="22"/></Target><Target id="@+id/addMoneyLayout" view="ScrollView"><Expressions/><location startLine="99" startOffset="12" endLine="493" endOffset="24"/></Target><Target id="@+id/btn_closeaddmoney" view="ImageView"><Expressions/><location startLine="122" startOffset="20" endLine="133" endOffset="49"/></Target><Target id="@+id/textView9" view="TextView"><Expressions/><location startLine="136" startOffset="20" endLine="148" endOffset="67"/></Target><Target id="@+id/cvDiscountInfo" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="151" startOffset="20" endLine="209" endOffset="55"/></Target><Target id="@+id/etDeposite" view="EditText"><Expressions/><location startLine="212" startOffset="20" endLine="226" endOffset="83"/></Target><Target id="@+id/llPricingPlans" view="LinearLayout"><Expressions/><location startLine="229" startOffset="20" endLine="456" endOffset="34"/></Target><Target id="@+id/plan50" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="247" startOffset="28" endLine="293" endOffset="63"/></Target><Target id="@+id/price1" view="TextView"><Expressions/><location startLine="275" startOffset="36" endLine="282" endOffset="66"/></Target><Target id="@+id/plan100" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="296" startOffset="28" endLine="343" endOffset="63"/></Target><Target id="@+id/price2" view="TextView"><Expressions/><location startLine="324" startOffset="36" endLine="331" endOffset="66"/></Target><Target id="@+id/plan150" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="355" startOffset="28" endLine="402" endOffset="63"/></Target><Target id="@+id/price3" view="TextView"><Expressions/><location startLine="383" startOffset="36" endLine="390" endOffset="66"/></Target><Target id="@+id/plan200" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="405" startOffset="28" endLine="452" endOffset="63"/></Target><Target id="@+id/price4" view="TextView"><Expressions/><location startLine="433" startOffset="36" endLine="440" endOffset="66"/></Target><Target id="@+id/btnDeposite" view="LinearLayout"><Expressions/><location startLine="470" startOffset="24" endLine="489" endOffset="38"/></Target><Target id="@+id/withdrawMoneyLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="495" startOffset="12" endLine="573" endOffset="63"/></Target><Target id="@+id/btn_closewithdraw" view="ImageView"><Expressions/><location startLine="504" startOffset="16" endLine="512" endOffset="63"/></Target><Target id="@+id/textView11" view="TextView"><Expressions/><location startLine="514" startOffset="16" endLine="524" endOffset="63"/></Target><Target id="@+id/etWithdraw" view="EditText"><Expressions/><location startLine="526" startOffset="16" endLine="537" endOffset="75"/></Target><Target id="@+id/etWithdrawAmmount" view="EditText"><Expressions/><location startLine="539" startOffset="16" endLine="550" endOffset="75"/></Target><Target id="@+id/btnWithdrawFinal" view="LinearLayout"><Expressions/><location startLine="552" startOffset="16" endLine="571" endOffset="30"/></Target><Target id="@+id/wheelLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="578" startOffset="8" endLine="652" endOffset="59"/></Target><Target id="@+id/luckyWheel" view="ImageView"><Expressions/><location startLine="586" startOffset="12" endLine="594" endOffset="59"/></Target><Target id="@+id/btnAddMoney" view="LinearLayout"><Expressions/><location startLine="606" startOffset="16" endLine="626" endOffset="30"/></Target><Target id="@+id/btnSpin" view="LinearLayout"><Expressions/><location startLine="629" startOffset="16" endLine="648" endOffset="30"/></Target><Target id="@+id/nav_view" view="com.google.android.material.navigation.NavigationView"><Expressions/><location startLine="657" startOffset="4" endLine="668" endOffset="58"/></Target></Targets></Layout>