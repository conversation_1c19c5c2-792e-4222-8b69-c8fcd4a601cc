package com.bigwheel.winners;


import android.annotation.SuppressLint;
import android.app.ProgressDialog;
import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.media.MediaPlayer;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;

import com.airbnb.lottie.LottieAnimationView;
import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;

import com.bigwheel.winners.pay.PaymentMethods;
import com.google.android.gms.auth.api.signin.GoogleSignIn;
import com.google.android.gms.auth.api.signin.GoogleSignInClient;
import com.google.android.gms.auth.api.signin.GoogleSignInOptions;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.android.material.navigation.NavigationView;
import com.squareup.picasso.Picasso;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import de.hdodenhof.circleimageview.CircleImageView;
import rubikstudio.library.LuckyWheelView;
import rubikstudio.library.model.LuckyItem;

public class MainActivity extends AppCompatActivity {

    ConstraintLayout addMoneyLayout;
    LinearLayout btnAddMoney;
    LinearLayout btnDeposite;
    LinearLayout btnSpin;
    LinearLayout btnWithdraw;
    LinearLayout btnWithdrawFinal;
    ImageView btn_closeaddmoney;
    ImageView btn_closewithdraw;
    LinearLayout deposite_100;
    LinearLayout deposite_150;
    LinearLayout deposite_200;
    LinearLayout deposite_50;
    EditText etDeposite;
    EditText etWithdraw;
    EditText etWithdrawAmmount;
    LuckyWheelView luckyWheelView;
    DrawerLayout drawerLayout;

    // Premium Plan Variables
    private int selectedPlanIndex = -1;
    private String[] planPrices = {"50", "100", "150", "200"};
    private String[] planDiscounts = {"0", "10", "20", "30"};
    private String[] planOriginalPrices = {"50", "110", "180", "260"};
    private Animation popupEnterAnim, popupExitAnim, planSelectAnim, buttonPressAnim;


    TextView tvDepositeAmmount;
    TextView tvWiningeAmmount;
    TextView tv_headline;
    ConstraintLayout wheelLayout;
    ConstraintLayout withdrawMoneyLayout;
    private TextView price2, price1, price3, price4, textView9, textView11;
    private ImageView drawertoggle;
    NavigationView navigationView;
    DrawerLayout drawer;

    private SharedPreferences pref;
    SharedPreferences.Editor editor;
    public int[] winpos = {1, 3, 9, 11, 5, 6, 0};
    public int[] losspos = {2, 7, 10, 4, 8};
    CircleImageView userimg;
    TextView usernametxt, useremailtext;

    MediaPlayer song, winnig, loss;

    int secondIndex = 0,thirdIndex = 0;
    String myamount = "50";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        getSupportActionBar().hide();
        price1 = findViewById(R.id.price1);
        price2 = findViewById(R.id.price2);
        price3 = findViewById(R.id.price3);
        price4 = findViewById(R.id.price4);
        drawertoggle = findViewById(R.id.btn_menu);
        textView9 = findViewById(R.id.textView9);
        textView11 = findViewById(R.id.textView11);
        drawerLayout = findViewById(R.id.drawer_layout);


        // Note: btnWithdraw and btnAddMoney don't exist in simplified layout
        // btnWithdraw = (LinearLayout) findViewById(R.id.btnWithdraw);
        btnDeposite = (LinearLayout) findViewById(R.id.btnDeposite);
        // btnAddMoney = (LinearLayout) findViewById(R.id.btnAddMoney);
        btnSpin = (LinearLayout) findViewById(R.id.btnSpin);
        // wheelLayout = (ConstraintLayout) findViewById(R.id.wheelLayout);
        // tv_headline = (TextView) findViewById(R.id.scroller);
        addMoneyLayout = (ConstraintLayout) findViewById(R.id.addMoneyLayout);
        btn_closewithdraw = (ImageView) findViewById(R.id.btn_closewithdraw);
        btn_closeaddmoney = (ImageView) findViewById(R.id.btn_closeaddmoney);
        addMoneyLayout.setVisibility(View.GONE);
        ConstraintLayout constraintLayout = (ConstraintLayout) findViewById(R.id.withdrawMoneyLayout);
        withdrawMoneyLayout = constraintLayout;
        constraintLayout.setVisibility(View.GONE);
        // tv_headline.setSelected(true);
        tvDepositeAmmount = (TextView) findViewById(R.id.tvDepositeAmmount);
        tvWiningeAmmount = (TextView) findViewById(R.id.tvWiningeAmmount);
        etDeposite = (EditText) findViewById(R.id.etDeposite);
        etWithdraw = (EditText) findViewById(R.id.etWithdraw);
        etWithdrawAmmount = (EditText) findViewById(R.id.etWithdrawAmmount);
        btnWithdrawFinal = (LinearLayout) findViewById(R.id.btnWithdrawFinal);
        deposite_50 = (LinearLayout) findViewById(R.id.plan50);
        deposite_100 = (LinearLayout) findViewById(R.id.plan100);
        deposite_150 = (LinearLayout) findViewById(R.id.plan150);
        deposite_200 = (LinearLayout) findViewById(R.id.plan200);
/*        SweetAlertDialog sweetAlertDialog = new SweetAlertDialog(this, 5);
        pDialog = sweetAlertDialog;
        sweetAlertDialog.getProgressHelper().setBarColor(Color.parseColor("#FFB800"));
        pDialog.setTitleText("Please wait...");
        pDialog.setCancelable(false);*/

        pref = getSharedPreferences("mypref", MODE_PRIVATE);
        editor = pref.edit();

        GoogleSignInOptions gso = new GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
                .requestEmail()
                .build();

        GoogleSignInClient mGoogleSignInClient = GoogleSignIn.getClient(this, gso);


        navigationView = findViewById(R.id.nav_view);
        drawer = findViewById(R.id.drawer_layout);
        View header = navigationView.getHeaderView(0);
        userimg = (CircleImageView) header.findViewById(R.id.userimg);
        usernametxt = (TextView) header.findViewById(R.id.username);
        useremailtext = (TextView) header.findViewById(R.id.useremail);

        usernametxt.setText(pref.getString("uname", "Jaimin Patel"));
        useremailtext.setText(pref.getString("uemail", "<EMAIL>"));
        String imgurl = pref.getString("upic", "");


        if (imgurl != "") {
            Picasso.get().load(imgurl).into(userimg);
        }

        GetDataFromServer();


        drawertoggle.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                drawer.openDrawer(GravityCompat.START);

            }
        });

        navigationView.setNavigationItemSelectedListener(new NavigationView.OnNavigationItemSelectedListener() {
            @SuppressLint("WrongConstant")
            @Override
            public boolean onNavigationItemSelected(@NonNull MenuItem menuItem) {
                int id = menuItem.getItemId();
                //it's possible to do more actions on several items, if there is a large amount of items I prefer switch(){case} instead of if()
                if (id == R.id.nav_home) {

                }


                if (id == R.id.nav_privacy) {
                    try {
                        Intent myIntent = new Intent(Intent.ACTION_VIEW, Uri.parse("https://sites.google.com/view/big-wheel-privacy-policy/home"));
                        startActivity(myIntent);
                    } catch (ActivityNotFoundException e) {
                        Toast.makeText(MainActivity.this, "No application can handle this request."
                                + " Please install a web browser", Toast.LENGTH_LONG).show();
                        e.printStackTrace();
                    }
                }
                if (id == R.id.nav_tc) {
                    try {
                        Intent myIntent = new Intent(Intent.ACTION_VIEW, Uri.parse("https://sites.google.com/view/big-wheel-term-condition/home"));
                        startActivity(myIntent);
                    } catch (ActivityNotFoundException e) {
                        Toast.makeText(MainActivity.this, "No application can handle this request."
                                + " Please install a web browser", Toast.LENGTH_LONG).show();
                        e.printStackTrace();
                    }
                }
                if (id == R.id.nav_contactus) {
                    Intent intent = new Intent(Intent.ACTION_SENDTO); // it's not ACTION_SEND
                    intent.putExtra(Intent.EXTRA_SUBJECT, "Krunal Technology");
                    intent.putExtra(Intent.EXTRA_TEXT, "Hello, We are here to help you, Please inform us what can we do for you?\n");
                    intent.setData(Uri.parse("mailto:<EMAIL>")); // or just "mailto:" for blank
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK); // this will make such that when user returns to your app, your app is displayed, instead of the email app.
                    startActivity(intent);
                }
                if (id == R.id.nav_moreapps) {
                    Intent i = new Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/developer?id=devbuddy"));
                    startActivity(i);
                }

                if (id == R.id.nav_share) {
                    Intent sendIntent = new Intent();
                    sendIntent.setAction(Intent.ACTION_SEND);
                    sendIntent.putExtra(Intent.EXTRA_TEXT,
                            "Hello!\nThis is one of the best Daily Earn Application.\nHere you can do work from home.\n Here you can Spin & Win Real Cash On single tap. \nDownload Now:\n https://play.google.com/store/apps/details?id=" + getPackageName());
                    sendIntent.setType("text/plain");
                    startActivity(sendIntent);
                }
                if (id == R.id.nav_logout) {
                    mGoogleSignInClient.signOut()
                            .addOnCompleteListener(MainActivity.this, new OnCompleteListener<Void>() {
                                @Override
                                public void onComplete(@NonNull Task<Void> task) {
                                    startActivity(new Intent(MainActivity.this, SplashScreen.class));
                                    finish();
                                }
                            });
                }
                drawer.closeDrawer(GravityCompat.START);
                return true;
            }
        });


        // Initialize Premium Pricing System
        String FirstAmout = pref.getString("damu1", "50");
        String SecondAmout = pref.getString("damu2", "100");
        String TherdAmout = pref.getString("damu3", "150");
        String FourthAmout = pref.getString("damu4", "200");

        // Update plan prices with new 4-plan system
        planPrices[0] = FirstAmout;
        planPrices[1] = SecondAmout;
        planPrices[2] = TherdAmout;
        planPrices[3] = FourthAmout;

        String line = "🎯 Choose Your Perfect Spin Package";
        String wline = "Minimum Withdraw Amount " + pref.getString("wamu", "300");

        song = MediaPlayer.create(this, R.raw.wheelsong);
        winnig = MediaPlayer.create(this, R.raw.winningsong);
        loss = MediaPlayer.create(this, R.raw.failmusic);

        // Initialize animations
        popupEnterAnim = AnimationUtils.loadAnimation(this, R.anim.popup_enter);
        popupExitAnim = AnimationUtils.loadAnimation(this, R.anim.popup_exit);
        planSelectAnim = AnimationUtils.loadAnimation(this, R.anim.plan_select);
        buttonPressAnim = AnimationUtils.loadAnimation(this, R.anim.button_press);

        // Set premium pricing display
        price1.setText("₹" + planPrices[0]);
        price2.setText("₹" + planPrices[1]);
        price3.setText("₹" + planPrices[2]);
        price4.setText("₹" + planPrices[3]);
        textView9.setText(line);
        textView11.setText(wline);


        luckyWheelView = (LuckyWheelView) findViewById(R.id.luckyWheel);

        List<LuckyItem> data = new ArrayList<>();
        LuckyItem luckyItem1 = new LuckyItem();
        luckyItem1.text = "90";
        luckyItem1.color = Color.parseColor("#FFA800");
        data.add(luckyItem1);
        LuckyItem luckyItem2 = new LuckyItem();
        luckyItem2.text = "10";
        luckyItem2.color = Color.parseColor("#02BE5F");
        data.add(luckyItem2);
        LuckyItem luckyItem3 = new LuckyItem();
        luckyItem3.text = "-10";
        luckyItem3.color = Color.parseColor("#00ABCB");
        data.add(luckyItem3);
        LuckyItem luckyItem4 = new LuckyItem();
        luckyItem4.text = "20";
        luckyItem4.color = Color.parseColor("#DF3684");
        data.add(luckyItem4);
        LuckyItem luckyItem5 = new LuckyItem();
        luckyItem5.text = "-40";
        luckyItem5.color = Color.parseColor("#FFA800");
        data.add(luckyItem5);
        LuckyItem luckyItem6 = new LuckyItem();
        luckyItem6.text = "50";
        luckyItem6.color = Color.parseColor("#02BE5F");
        data.add(luckyItem6);
        LuckyItem luckyItem7 = new LuckyItem();
        luckyItem7.text = "60";
        luckyItem7.color = Color.parseColor("#00ABCB");
        data.add(luckyItem7);
        LuckyItem luckyItem8 = new LuckyItem();
        luckyItem8.text = "-20";
        luckyItem8.color = Color.parseColor("#DF3684");
        data.add(luckyItem8);
        LuckyItem luckyItem9 = new LuckyItem();
        luckyItem9.text = "-50";
        luckyItem9.color = Color.parseColor("#FFA800");
        data.add(luckyItem9);
        LuckyItem luckyItem10 = new LuckyItem();
        luckyItem10.text = "30";
        luckyItem10.color = Color.parseColor("#02BE5F");
        data.add(luckyItem10);
        LuckyItem luckyItem11 = new LuckyItem();
        luckyItem11.text = "-30";
        luckyItem11.color = Color.parseColor("#00ABCB");
        data.add(luckyItem11);
        LuckyItem luckyItem12 = new LuckyItem();
        luckyItem12.text = "40";
        luckyItem12.color = Color.parseColor("#DF3684");
        data.add(luckyItem12);


        luckyWheelView.setLuckyWheelTextColor(Color.parseColor("#FFFFFF"));
        luckyWheelView.setData(data);
        luckyWheelView.setRound(10);
        luckyWheelView.setLuckyRoundItemSelectedListener(new LuckyWheelView.LuckyRoundItemSelectedListener() {
            public void LuckyRoundItemSelected(int i) {

                if (i != 0) {
                    i--;
                }

                String realdata = data.get(i).text;

                UpdateDataOnServer(data.get(i).text);


              /*  StringBuilder sb = new StringBuilder();
                sb.append("");
                sb.append(Integer.parseInt(CurrentUser.depositeAmmount) - 10);
              //  user.depositeAmmount = sb.toString();
              //  updateData();
                CurrentUser.withdrawAmmount = "" + (Integer.parseInt(CurrentUser.withdrawAmmount) + Integer.parseInt(
                        data.get(i).text));
                updateDataWithdraw(data.get(i).text);*/
            }
        });

        // btnAddMoney click listener - commented out as button doesn't exist in simplified layout
        /*btnAddMoney.setOnClickListener(new View.OnClickListener() {
            public void onClick(View view) {
                view.startAnimation(buttonPressAnim);
                if (addMoneyLayout.getVisibility() == View.VISIBLE) {
                    // Close with animation
                    addMoneyLayout.startAnimation(popupExitAnim);
                    addMoneyLayout.setVisibility(View.GONE);
                    btnSpin.setVisibility(View.VISIBLE);
                    // wheelLayout.setVisibility(View.VISIBLE);
                    withdrawMoneyLayout.setVisibility(View.GONE);
                    return;
                }
                // Open with animation
                addMoneyLayout.setVisibility(View.VISIBLE);
                addMoneyLayout.startAnimation(popupEnterAnim);
                btnSpin.setVisibility(View.GONE);
                // wheelLayout.setVisibility(View.GONE);
                withdrawMoneyLayout.setVisibility(View.GONE);
                resetPlanSelection();
            }
        });*/
        btn_closeaddmoney.setOnClickListener(new View.OnClickListener() {
            public void onClick(View view) {
                view.startAnimation(buttonPressAnim);
                if (addMoneyLayout.getVisibility() == View.VISIBLE) {
                    addMoneyLayout.startAnimation(popupExitAnim);
                    addMoneyLayout.setVisibility(View.GONE);
                    btnSpin.setVisibility(View.VISIBLE);
                    // wheelLayout.setVisibility(View.VISIBLE);
                    withdrawMoneyLayout.setVisibility(View.GONE);
                }
            }
        });
        // btnWithdraw click listener - commented out as button doesn't exist in simplified layout
        /*btnWithdraw.setOnClickListener(new View.OnClickListener() {
            public void onClick(View view) {
                if (withdrawMoneyLayout.getVisibility() == View.VISIBLE) {
                    withdrawMoneyLayout.setVisibility(View.GONE);
                    btnSpin.setVisibility(View.VISIBLE);
                    // wheelLayout.setVisibility(View.VISIBLE);
                    addMoneyLayout.setVisibility(View.GONE);
                    return;
                }
                withdrawMoneyLayout.setVisibility(View.VISIBLE);
                btnSpin.setVisibility(View.GONE);
                // wheelLayout.setVisibility(View.GONE);
                addMoneyLayout.setVisibility(View.GONE);
            }
        });*/
        btn_closewithdraw.setOnClickListener(new View.OnClickListener() {
            public void onClick(View view) {
                if (withdrawMoneyLayout.getVisibility() == View.VISIBLE) {
                    withdrawMoneyLayout.setVisibility(View.GONE);
                    btnSpin.setVisibility(View.VISIBLE);
                    // wheelLayout.setVisibility(View.VISIBLE);
                    addMoneyLayout.setVisibility(View.GONE);
                }
            }
        });
        btnWithdrawFinal.setOnClickListener(new View.OnClickListener() {
            public void onClick(View view) {

                String obj = etWithdraw.getText().toString();
                String obj2 = etWithdrawAmmount.getText().toString();

                if (obj.trim().length() != 10) {
                    new AlertDialog.Builder(MainActivity.this)
                            .setTitle("Number Verification!")
                            .setCancelable(false)
                            .setMessage("Please enter 10 Digit Number!")
                            .setPositiveButton("Re-Enter", new DialogInterface.OnClickListener() {
                                public void onClick(DialogInterface dialog, int which) {
                                    // Continue with delete operation
                                    dialog.dismiss();
                                }
                            }).setNegativeButton("close", new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialogInterface, int i) {
                            dialogInterface.dismiss();
                        }
                    }).setIcon(android.R.drawable.ic_dialog_alert)
                            .show();
                } else if (obj2.trim().length() == 0) {
                    new AlertDialog.Builder(MainActivity.this)
                            .setTitle("Withdraw Verification!")
                            .setCancelable(false)
                            .setMessage("Please Enter Amount!")
                            .setPositiveButton("Re-Enter", new DialogInterface.OnClickListener() {
                                public void onClick(DialogInterface dialog, int which) {
                                    // Continue with delete operation
                                    dialog.dismiss();
                                }
                            }).setNegativeButton("close", new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialogInterface, int i) {
                            dialogInterface.dismiss();
                        }
                    }).setIcon(android.R.drawable.ic_dialog_alert)
                            .show();
                } else if (Integer.parseInt(obj2) > pref.getInt("wonAmount", 0)) {
                    new AlertDialog.Builder(MainActivity.this)
                            .setTitle("Withdraw Verification!")
                            .setCancelable(false)
                            .setMessage("Please Enter Less Than Your Winning Amount!")
                            .setPositiveButton("Re-Enter", new DialogInterface.OnClickListener() {
                                public void onClick(DialogInterface dialog, int which) {
                                    // Continue with delete operation
                                    dialog.dismiss();
                                }
                            }).setNegativeButton("close", new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialogInterface, int i) {
                            dialogInterface.dismiss();
                        }
                    }).setIcon(android.R.drawable.ic_dialog_alert)
                            .show();
                } else if (Integer.parseInt(obj2) < Integer.parseInt(pref.getString("wamu", "0"))) {
                    new AlertDialog.Builder(MainActivity.this)
                            .setTitle("Withdraw Verification!")
                            .setCancelable(false)
                            .setMessage(wline)
                            .setPositiveButton("Re-Enter", new DialogInterface.OnClickListener() {
                                public void onClick(DialogInterface dialog, int which) {
                                    // Continue with delete operation
                                    dialog.dismiss();
                                }
                            }).setNegativeButton("close", new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialogInterface, int i) {
                            dialogInterface.dismiss();
                        }
                    }).setIcon(android.R.drawable.ic_dialog_alert)
                            .show();
                } else {

                    RequestForWithdrawMoney(obj, obj2);
                }
            }
        });
        deposite_50.setOnClickListener(new View.OnClickListener() {
            public void onClick(View view) {
                selectPlan(0, view);
                etDeposite.setText("₹" + planPrices[0]);
            }
        });
        deposite_100.setOnClickListener(new View.OnClickListener() {
            public void onClick(View view) {
                selectPlan(1, view);
                etDeposite.setText("₹" + planPrices[1]);
            }
        });
        deposite_150.setOnClickListener(new View.OnClickListener() {
            public void onClick(View view) {
                selectPlan(2, view);
                etDeposite.setText("₹" + planPrices[2]);
            }
        });
        deposite_200.setOnClickListener(new View.OnClickListener() {
            public void onClick(View view) {
                selectPlan(3, view);
                etDeposite.setText("₹" + planPrices[3]);
            }
        });


        btnSpin.setOnClickListener(new View.OnClickListener() {
            public void onClick(View view) {
                if (isConnectionAvailable(MainActivity.this)) {
                    //server condition
                    if (pref.getInt("mybalance", 0) < 10) {

                        new AlertDialog.Builder(MainActivity.this)
                                .setTitle("Notice!")
                                .setMessage("You don't have enough points!\nPlease add Money for spin")
                                .setCancelable(false)
                                .setPositiveButton("Add Money", new DialogInterface.OnClickListener() {
                                    public void onClick(DialogInterface dialog, int which) {
                                        // Continue with delete operation
                                        if ((addMoneyLayout.getVisibility()) == 0) {
                                            addMoneyLayout.setVisibility(View.GONE);
                                            btnSpin.setVisibility(View.VISIBLE);
                                            // wheelLayout.setVisibility(View.VISIBLE);
                                            withdrawMoneyLayout.setVisibility(View.GONE);

                                        } else {
                                            addMoneyLayout.setVisibility(View.VISIBLE);
                                            btnSpin.setVisibility(View.GONE);
                                            // wheelLayout.setVisibility(View.GONE);
                                            withdrawMoneyLayout.setVisibility(View.GONE);
                                        }
                                        dialog.dismiss();
                                    }
                                })
                                // A null listener allows the button to dismiss the dialog and take no further action.
                                .setNegativeButton("Cancel", new DialogInterface.OnClickListener() {
                                    @Override
                                    public void onClick(DialogInterface dialogInterface, int i) {
                                        dialogInterface.dismiss();
                                    }
                                })
                                .setIcon(android.R.drawable.ic_dialog_alert)
                                .show();


                    } else {
                        btnSpin.setVisibility(View.GONE);

                        GetSpinData(pref.getInt("wonAmount", 0));

                        song.start();


                    }
                } else {
                    new AlertDialog.Builder(MainActivity.this)
                            .setTitle("Error!")
                            .setMessage("Check Your Internet Connection")
                            .setCancelable(false)
                            .setPositiveButton("Retry", new DialogInterface.OnClickListener() {
                                public void onClick(DialogInterface dialog, int which) {
                                    // Continue with delete operation
                                    Intent intent = new Intent(MainActivity.this,MainActivity.class);
                                    startActivity(intent);
                                    finish();
                                }
                            })
                            // A null listener allows the button to dismiss the dialog and take no further action.
                            .setNegativeButton("Exit", new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialogInterface, int i) {
                                    finish();
                                }
                            })
                            .setIcon(android.R.drawable.ic_dialog_alert)
                            .show();
                }

            }
        });
        btnDeposite.setOnClickListener(new View.OnClickListener() {
            public void onClick(View view) {
                view.startAnimation(buttonPressAnim);
                String trim = etDeposite.getText().toString().trim();

                // Remove ₹ symbol if present
                if (trim.startsWith("₹")) {
                    trim = trim.substring(1);
                }

                final String finalTrim = trim; // Make it final for inner class

                if (selectedPlanIndex == -1) {
                    new AlertDialog.Builder(MainActivity.this)
                            .setTitle("🎯 Plan Selection Required!")
                            .setCancelable(false)
                            .setMessage("Please select a spin package to continue!")
                            .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                public void onClick(DialogInterface dialog, int which) {
                                    dialog.dismiss();
                                }
                            }).setIcon(android.R.drawable.ic_dialog_info)
                            .show();
                } else if (finalTrim.equals("") || !TextUtils.isDigitsOnly(finalTrim)) {
                    new AlertDialog.Builder(MainActivity.this)
                            .setTitle("💰 Amount Verification!")
                            .setCancelable(false)
                            .setMessage("Please select a valid spin package!")
                            .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                public void onClick(DialogInterface dialog, int which) {
                                    dialog.dismiss();
                                }
                            }).setIcon(android.R.drawable.ic_dialog_alert)
                            .show();
                } else {
                    // Show confirmation with plan details
                    final String planName = getPlanName(selectedPlanIndex);
                    final String discount = getPlanDiscount(selectedPlanIndex);

                    new AlertDialog.Builder(MainActivity.this)
                            .setTitle("🚀 Confirm Your Purchase")
                            .setMessage("Plan: " + planName + "\nAmount: ₹" + finalTrim + "\nDiscount: " + discount + "\n\nProceed to payment?")
                            .setPositiveButton("PROCEED", new DialogInterface.OnClickListener() {
                                public void onClick(DialogInterface dialog, int which) {
                                    myamount = finalTrim;
                                    Intent intent = new Intent(MainActivity.this, PaymentMethods.class);
                                    intent.putExtra("amount", finalTrim);
                                    intent.putExtra("planName", planName);
                                    intent.putExtra("discount", discount);
                                    startActivity(intent);
                                    finish();
                                }
                            })
                            .setNegativeButton("CANCEL", new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialogInterface, int i) {
                                    dialogInterface.dismiss();
                                }
                            })
                            .setIcon(android.R.drawable.ic_dialog_info)
                            .show();
                }
            }
        });


    }

    @Override
    public void onBackPressed() {
        retureDilog();
    }

    private void RequestForWithdrawMoney(String phone, String obj2) {


        ProgressDialog progressDialog = new ProgressDialog(MainActivity.this);
        progressDialog.setCancelable(false);
        progressDialog.setMessage("Please wait...");
        progressDialog.show();

        RequestQueue queue = Volley.newRequestQueue(this);
        StringRequest stringRequest = new StringRequest(Request.Method.POST, pref.getString("baseurl", "") + "withdrawreq.php",
                new Response.Listener<String>() {
                    @Override
                    public void onResponse(String response) {
                        progressDialog.dismiss();
                        if (response.equals("0")) {
                            new AlertDialog.Builder(MainActivity.this)
                                    .setTitle("Error!")
                                    .setCancelable(false)
                                    .setMessage("Something went wrong!")
                                    .setPositiveButton("Retry", new DialogInterface.OnClickListener() {
                                        public void onClick(DialogInterface dialog, int which) {
                                            // Continue with delete operation
                                            Intent intent = new Intent(MainActivity.this, MainActivity.class);
                                            startActivity(intent);
                                            finish();
                                        }
                                    }).setNegativeButton("Exit", new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialogInterface, int i) {
                                    finish();
                                }
                            }).setIcon(android.R.drawable.ic_dialog_alert)
                                    .show();
                        } else {

                            withdrawMoneyLayout.setVisibility(View.GONE);
                            btnSpin.setVisibility(View.VISIBLE);
                            // wheelLayout.setVisibility(View.VISIBLE);
                            addMoneyLayout.setVisibility(View.GONE);

                            Toast.makeText(MainActivity.this, "You will get your amount within 72 Working Hours!", Toast.LENGTH_SHORT).show();

                            setUserWinDepositeData(pref.getInt("mybalance", 0), pref.getInt("wonAmount", 0) - Integer.parseInt(obj2));

                        }
                    }
                }, new Response.ErrorListener() {
            @Override
            public void onErrorResponse(VolleyError error) {
                progressDialog.dismiss();

                new AlertDialog.Builder(MainActivity.this)
                        .setTitle("Error!")
                        .setMessage("Check Your Internet Connection")
                        .setCancelable(false)
                        .setPositiveButton("Retry", new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int which) {
                                // Continue with delete operation
                                Intent intent = new Intent(MainActivity.this, MainActivity.class);
                                startActivity(intent);
                                finish();
                            }
                        })
                        // A null listener allows the button to dismiss the dialog and take no further action.
                        .setNegativeButton("Exit", new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialogInterface, int i) {
                                finish();
                            }
                        })
                        .setIcon(android.R.drawable.ic_dialog_alert)
                        .show();
            }
        }) {
            @Override
            protected Map<String, String> getParams() {
                Map<String, String> params = new HashMap<String, String>();
                params.put("email", pref.getString("uemail", "<EMAIL>"));
                params.put("phone", phone);
                params.put("finalamu", String.valueOf(pref.getInt("wonAmount", 0) - Integer.parseInt(obj2)));
                params.put("wamu", obj2);
                return params;
            }
        };
        queue.add(stringRequest);

    }

    private void UpdateDataOnServer(String amount) {


        ProgressDialog progressDialog = new ProgressDialog(MainActivity.this);
        progressDialog.setCancelable(false);
        progressDialog.setMessage("Please wait...");
        progressDialog.show();

        RequestQueue queue = Volley.newRequestQueue(this);
        StringRequest stringRequest = new StringRequest(Request.Method.POST, pref.getString("baseurl", "") + "updateuserdata.php",
                new Response.Listener<String>() {
                    @Override
                    public void onResponse(String response) {
                        progressDialog.dismiss();
                        if (response.equals("0")) {
                            new AlertDialog.Builder(MainActivity.this)
                                    .setTitle("Error!")
                                    .setCancelable(false)
                                    .setMessage("Something went wrong!")
                                    .setPositiveButton("Retry", new DialogInterface.OnClickListener() {
                                        public void onClick(DialogInterface dialog, int which) {
                                            // Continue with delete operation
                                            Intent intent = new Intent(MainActivity.this, MainActivity.class);
                                            startActivity(intent);
                                            finish();
                                        }
                                    }).setNegativeButton("Exit", new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialogInterface, int i) {
                                    finish();
                                }
                            }).setIcon(android.R.drawable.ic_dialog_alert)
                                    .show();
                        } else {

                            if (amount.contains("-")) {
                                btnSpin.setVisibility(View.GONE);
                                loss.start();
                                AlertDialog.Builder dialogBuilder = new AlertDialog.Builder(MainActivity.this);
                                LayoutInflater inflater = MainActivity.this.getLayoutInflater();
                                View dialogView = inflater.inflate(R.layout.dialog_rate_us, null);
                                dialogBuilder.setView(dialogView);
                                dialogBuilder.setCancelable(false);
                                final AlertDialog dialog = dialogBuilder.create();
                                dialog.getWindow().getAttributes().windowAnimations = R.style.MyDialogTheme;
                                dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                                dialog.show();
                                LottieAnimationView lottieAnimationView = dialog.findViewById(R.id.loss);
                                lottieAnimationView.setAnimation(R.raw.lossanim);
                                LinearLayout btnLater = (LinearLayout) dialog.findViewById(R.id.btnLater);
                                LinearLayout btnRateNow = (LinearLayout) dialog.findViewById(R.id.btnRateNow);
                                TextView winamount = (TextView) dialog.findViewById(R.id.winamount);
                                winamount.setText("Try Another Time! You lose " + amount);
                                btnLater.setOnClickListener(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        btnSpin.setVisibility(View.VISIBLE);
                                        dialog.dismiss();
                                    }
                                });

                                btnRateNow.setOnClickListener(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        btnSpin.setVisibility(View.VISIBLE);
                                        dialog.dismiss();
                                    }
                                });
                                //    Toast.makeText(getApplicationContext(), "Try Another Time! You lose" + ((LuckyItem) arrayList.get(i)).text, Toast.LENGTH_SHORT).show();
                            } else {
                                btnSpin.setVisibility(View.GONE);
                                winnig.start();
                                AlertDialog.Builder dialogBuilder = new AlertDialog.Builder(MainActivity.this);
                                LayoutInflater inflater = MainActivity.this.getLayoutInflater();
                                View dialogView = inflater.inflate(R.layout.dialog_rate_us, null);
                                dialogBuilder.setView(dialogView);
                                dialogBuilder.setCancelable(false);
                                final AlertDialog dialog = dialogBuilder.create();
                                dialog.getWindow().getAttributes().windowAnimations = R.style.MyDialogTheme;
                                dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                                dialog.show();

                                LottieAnimationView winner = dialog.findViewById(R.id.winner);
                                winner.setAnimation(R.raw.winner);
                                LinearLayout btnLater = (LinearLayout) dialog.findViewById(R.id.btnLater);
                                LinearLayout btnRateNow = (LinearLayout) dialog.findViewById(R.id.btnRateNow);
                                TextView winamount = (TextView) dialog.findViewById(R.id.winamount);
                                winamount.setText("Congratulations! You Win " + amount);
                                btnLater.setOnClickListener(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        btnSpin.setVisibility(View.VISIBLE);
                                        dialog.dismiss();
                                    }
                                });

                                btnRateNow.setOnClickListener(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        btnSpin.setVisibility(View.VISIBLE);
                                        dialog.dismiss();

                                    }
                                });

                                //new SweetAlertDialog(MainActivity.this, 2).setTitleText("Good job!").setContentText("Congratulations! You Win" + ((LuckyItem) arrayList.get(i)).text).show();
                            }
                            setUserWinDepositeData(pref.getInt("mybalance", 0) - 10, pref.getInt("wonAmount", 0) + Integer.parseInt(amount));

                        }
                    }
                }, new Response.ErrorListener() {
            @Override
            public void onErrorResponse(VolleyError error) {
                progressDialog.dismiss();

                new AlertDialog.Builder(MainActivity.this)
                        .setTitle("Error!")
                        .setMessage("Check Your Internet Connection")
                        .setCancelable(false)
                        .setPositiveButton("Retry", new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int which) {
                                // Continue with delete operation
                                Intent intent = new Intent(MainActivity.this, MainActivity.class);
                                startActivity(intent);
                                finish();
                            }
                        })
                        // A null listener allows the button to dismiss the dialog and take no further action.
                        .setNegativeButton("Exit", new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialogInterface, int i) {
                                finish();
                            }
                        })
                        .setIcon(android.R.drawable.ic_dialog_alert)
                        .show();
            }
        }) {
            @Override
            protected Map<String, String> getParams() {
                Map<String, String> params = new HashMap<String, String>();
                params.put("email", pref.getString("uemail", "<EMAIL>"));
                params.put("wamu", String.valueOf(pref.getInt("wonAmount", 0) + Integer.parseInt(amount)));
                params.put("damu", String.valueOf(pref.getInt("mybalance", 0) - 10));
                params.put("spindex", String.valueOf(pref.getInt("spindex", 0)));
                return params;
            }
        };
        queue.add(stringRequest);
    }

    private void GetDataFromServer() {
        ProgressDialog progressDialog = new ProgressDialog(MainActivity.this);
        progressDialog.setCancelable(false);
        progressDialog.setMessage("Please wait...");
        progressDialog.show();


        RequestQueue queue = Volley.newRequestQueue(this);
        StringRequest stringRequest = new StringRequest(Request.Method.POST, pref.getString("baseurl", "") + "getuserdata.php",
                new Response.Listener<String>() {
                    @Override
                    public void onResponse(String response) {
                        progressDialog.dismiss();
                        if (response.equals("0")) {
                            new AlertDialog.Builder(MainActivity.this)
                                    .setTitle("Error!")
                                    .setCancelable(false)
                                    .setMessage("Something went wrong! server log")
                                    .setPositiveButton("Retry", new DialogInterface.OnClickListener() {
                                        public void onClick(DialogInterface dialog, int which) {
                                            // Continue with delete operation
                                            Intent intent = new Intent(MainActivity.this, MainActivity.class);
                                            startActivity(intent);
                                            finish();
                                        }
                                    }).setNegativeButton("Exit", new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialogInterface, int i) {
                                    finish();
                                }
                            }).setIcon(android.R.drawable.ic_dialog_alert)
                                    .show();
                        } else {

                            try {
                                JSONArray jary = new JSONArray(response);
                                for (int i = 0; i < jary.length(); i++) {
                                    JSONObject jobj = new JSONObject(String.valueOf(jary.getJSONObject(i)));
                                    int depo = Integer.parseInt(jobj.getString("damu"));
                                    int wina = Integer.parseInt(jobj.getString("wamu"));
                                    editor.putInt("spindex", Integer.parseInt(jobj.getString("spindex")));
                                    editor.apply();
                                    setUserWinDepositeData(depo, wina);
                                }

                            } catch (JSONException e) {
                                e.printStackTrace();
                            }


                        }
                    }
                }, new Response.ErrorListener() {
            @Override
            public void onErrorResponse(VolleyError error) {
                progressDialog.dismiss();

                new AlertDialog.Builder(MainActivity.this)
                        .setTitle("Error!")
                        .setMessage("Check Your Internet Connection")
                        .setCancelable(false)
                        .setPositiveButton("Retry", new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int which) {
                                // Continue with delete operation
                                Intent intent = new Intent(MainActivity.this, GoogleLogin.class);
                                startActivity(intent);
                                finish();
                            }
                        })
                        // A null listener allows the button to dismiss the dialog and take no further action.
                        .setNegativeButton("Exit", new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialogInterface, int i) {
                                finish();
                            }
                        })
                        .setIcon(android.R.drawable.ic_dialog_alert)
                        .show();
            }
        }) {
            @Override
            protected Map<String, String> getParams() {
                Map<String, String> params = new HashMap<String, String>();
                params.put("email", pref.getString("uemail", "<EMAIL>"));
                return params;
            }
        };
        queue.add(stringRequest);

    }


    public void GetSpinData(int winAmu) {


        editor.putInt("spindex", pref.getInt("spindex", 0) + 1);
        editor.apply();

        Random rr = new Random();

        // int depoAmu = pref.getInt("mybalance", 0);


        int pos = 0;
      /*  secondIndex = pref.getInt("secondindex", 0);
        thirdIndex  = pref.getInt("thirdindex",0);*/
        // 5 shot remains
        if (pref.getInt("spindex", 0) == 1) {

            //  60 90
            int rno = rr.nextInt(7 - 5) + 5;
            //pos = winpos[6];

            pos = winpos[rno];
            secondIndex = winpos[rno];
            editor.putInt("secondindex",secondIndex);
            editor.apply();

        } else if (pref.getInt("spindex", 0) == 2) {
            //  40 50
            int rno = rr.nextInt(5 - 3) + 3;


            //  pos = winpos[4];

            if(pref.getInt("secondindex", 0) == 6)
            {
                pos = winpos[4];
            }
            else
            {

                pos = winpos[rno];
                thirdIndex = winpos[rno];
                editor.putInt("thirdindex",thirdIndex);
                editor.apply();

                //  Toast.makeText(this, thirdIndex, Toast.LENGTH_SHORT).show();
            }
        } else if (pref.getInt("spindex", 0) == 3) {
            // 50 60
            int rno = rr.nextInt(6 - 4) + 4;
            if(pref.getInt("secondindex", 0) == 6)
            {
                pos = winpos[5];
            }
            else
            {
                if(pref.getInt("thirdindex",0) == 11)
                {
                    pos = winpos[5];
                }
                else
                {
                    pos = winpos[rno];
                }
            }
            // pos = winpos[5];

        } else if (pref.getInt("spindex", 0) == 4) {
            //-10,-20
            int rno = rr.nextInt(2 - 0);

            if(pref.getInt("secondindex", 0) == 6)
            {
                int h = rr.nextInt(3-0);
                pos = winpos[h];
            }
            else
            {
                pos = losspos[rno];
            }

            //pos = losspos[0];

        } else if (pref.getInt("spindex", 0) == 5) {
            // 90
            int rno = rr.nextInt(7 - 5) + 5;
            pos = winpos[6];
            //pos = winpos[rno];

        } else {
            if (winAmu <= 100) {
                // 0% loss
                int rno = rr.nextInt(7 - 4) + 4;
                pos = winpos[rno];
            } else if (winAmu > 100 && winAmu <= 200) {
                // 30% Loss
                int xrno = rr.nextInt(11 - 1) + 1;
                int rno = 0;
                if (xrno <= 3) {
                    // loss
                    rno = rr.nextInt(5 - 0);
                    pos = losspos[rno];
                } else {
                    // win
                    rno = rr.nextInt(7 - 0);
                    pos = winpos[rno];
                }
            } else if (winAmu > 200 && winAmu <= 250) {
                // 50% Loss
                int xrno = rr.nextInt(11 - 1) + 1;
                int rno = 0;
                if (xrno <= 5) {
                    // loss
                    rno = rr.nextInt(5 - 0);
                    pos = losspos[rno];
                } else {
                    // win
                    rno = rr.nextInt(3 - 0);
                    pos = winpos[rno];
                }
            } else if (winAmu > 250 && winAmu <= 280) {
                // 60% Loss
                int xrno = rr.nextInt(11 - 1) + 1;
                int rno = 0;
                if (xrno <= 6) {
                    // loss
                    rno = rr.nextInt(5 - 0);
                    pos = losspos[rno];
                } else {
                    // win
                    // rno = rr.nextInt(1-0);
                    pos = winpos[0];
                }
            } else if (winAmu > 280) {
                // 100% Loss
                int rno = rr.nextInt(5 - 0);
                pos = losspos[rno];
            } else {
                // 100% Loss
                int rno = rr.nextInt(5 - 0);
                pos = losspos[rno];
            }

        }

        luckyWheelView.startLuckyWheelWithTargetIndex(pos + 1);
    }

    public void retureDilog() {
        btnSpin.setVisibility(View.GONE);
        AlertDialog.Builder dialogBuilder = new AlertDialog.Builder(MainActivity.this);
        LayoutInflater inflater = MainActivity.this.getLayoutInflater();
        View dialogView = inflater.inflate(R.layout.areyousurdilog, null);
        dialogBuilder.setView(dialogView);
        dialogBuilder.setCancelable(false);
        final AlertDialog dialog = dialogBuilder.create();
        dialog.getWindow().getAttributes().windowAnimations = R.style.MyDialogTheme;
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        dialog.show();


        LinearLayout btnLater = (LinearLayout) dialog.findViewById(R.id.btnLater);
        LinearLayout btnRateNow = (LinearLayout) dialog.findViewById(R.id.btnRateNow);
        TextView winamount = (TextView) dialog.findViewById(R.id.winamount);
        btnLater.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                btnSpin.setVisibility(View.VISIBLE);
                dialog.dismiss();
            }
        });

        btnRateNow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

    }


    public void setUserWinDepositeData(int depo, int win) {
        TextView textView = tvDepositeAmmount;
        TextView textView2 = tvWiningeAmmount;

        editor.putInt("mybalance", depo);
        editor.putInt("wonAmount", win);
        editor.apply();
        if (depo == 0) {
            textView.setText("0" + depo);
        } else {
            textView.setText("" + depo);
        }
        if (win == 0) {
            textView2.setText("0" + win);
        } else {
            textView2.setText("" + win);
        }
    }


    public static boolean isConnectionAvailable(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo netInfo = connectivityManager.getActiveNetworkInfo();
            if (netInfo != null && netInfo.isConnected()
                    && netInfo.isConnectedOrConnecting()
                    && netInfo.isAvailable()) {
                return true;
            }
        }
        return false;
    }

    // Premium Plan Selection Methods
    private void selectPlan(int planIndex, View selectedView) {
        // Reset all plan selections
        resetPlanSelection();

        // Set selected plan
        selectedPlanIndex = planIndex;
        selectedView.setSelected(true);
        selectedView.startAnimation(planSelectAnim);

        // Update deposit amount with selected plan
        myamount = planPrices[planIndex];

        // Show success feedback
        Toast.makeText(this, "✨ " + getPlanName(planIndex) + " Plan Selected!", Toast.LENGTH_SHORT).show();
    }

    private void resetPlanSelection() {
        deposite_50.setSelected(false);
        deposite_100.setSelected(false);
        deposite_150.setSelected(false);
        deposite_200.setSelected(false);
        selectedPlanIndex = -1;
    }

    private String getPlanName(int planIndex) {
        switch (planIndex) {
            case 0: return "Basic";
            case 1: return "Popular";
            case 2: return "Premium";
            case 3: return "VIP";
            default: return "Unknown";
        }
    }

    private String getPlanDiscount(int planIndex) {
        if (planIndex == 0) return "No Discount";
        return planDiscounts[planIndex] + "% OFF";
    }

}