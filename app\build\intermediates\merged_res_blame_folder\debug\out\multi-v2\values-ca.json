{"logs": [{"outputFile": "D:\\android project\\new big 22-06\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-ca\\values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\02436aca31e270f81bb7f76ee8fb72ca\\transformed\\jetified-play-services-base-18.0.1\\res\\values-ca\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,670,816,939,1058,1162,1329,1434,1589,1716,1876,2030,2091,2155", "endColumns": "100,147,122,104,145,122,118,103,166,104,154,126,159,153,60,63,82", "endOffsets": "293,441,564,669,815,938,1057,1161,1328,1433,1588,1715,1875,2029,2090,2154,2237"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3405,3510,3662,3789,3898,4048,4175,4298,4541,4712,4821,4980,5111,5275,5433,5498,5566", "endColumns": "104,151,126,108,149,126,122,107,170,108,158,130,163,157,64,67,86", "endOffsets": "3505,3657,3784,3893,4043,4170,4293,4401,4707,4816,4975,5106,5270,5428,5493,5561,5648"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84e8a3931c74ff232fa18e9c8d1ffe2\\transformed\\jetified-play-services-basement-18.1.0\\res\\values-ca\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "130", "endOffsets": "325"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4406", "endColumns": "134", "endOffsets": "4536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f2636cb968702bde5e4e1d445b2113c3\\transformed\\browser-1.3.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,271,382", "endColumns": "112,102,110,108", "endOffsets": "163,266,377,486"}, "to": {"startLines": "55,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "5653,5928,6031,6142", "endColumns": "112,102,110,108", "endOffsets": "5761,6026,6137,6246"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8849a6fbe2ebfe5f57a82d8370a3dd43\\transformed\\jetified-firebase-messaging-21.0.1\\res\\values-ca\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "207", "endColumns": "100", "endOffsets": "307"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "6251", "endColumns": "104", "endOffsets": "6351"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5a6f99c51ca9894dfbe548454cc2e836\\transformed\\appcompat-1.5.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "279,402,507,614,697,803,929,1013,1092,1183,1276,1369,1464,1562,1655,1748,1842,1933,2024,2105,2216,2324,2422,2532,2637,2745,2905,10292", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "397,502,609,692,798,924,1008,1087,1178,1271,1364,1459,1557,1650,1743,1837,1928,2019,2100,2211,2319,2417,2527,2632,2740,2900,2999,10369"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\929536aae40a590fbb05df20b2f1b3b8\\transformed\\material-1.6.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,229,317,418,546,630,695,792,872,937,1032,1104,1166,1242,1305,1362,1483,1541,1602,1659,1739,1876,1963,2047,2156,2234,2313,2402,2469,2535,2613,2694,2782,2860,2937,3011,3090,3180,3272,3364,3465,3539,3621,3722,3772,3838,3930,4017,4079,4143,4206,4329,4432,4536,4642", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,87,100,127,83,64,96,79,64,94,71,61,75,62,56,120,57,60,56,79,136,86,83,108,77,78,88,66,65,77,80,87,77,76,73,78,89,91,91,100,73,81,100,49,65,91,86,61,63,62,122,102,103,105,85", "endOffsets": "224,312,413,541,625,690,787,867,932,1027,1099,1161,1237,1300,1357,1478,1536,1597,1654,1734,1871,1958,2042,2151,2229,2308,2397,2464,2530,2608,2689,2777,2855,2932,3006,3085,3175,3267,3359,3460,3534,3616,3717,3767,3833,3925,4012,4074,4138,4201,4324,4427,4531,4637,4723"}, "to": {"startLines": "2,33,34,35,36,56,57,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3004,3092,3193,3321,5766,5831,6356,6436,6501,6596,6668,6730,6806,6869,6926,7047,7105,7166,7223,7303,7440,7527,7611,7720,7798,7877,7966,8033,8099,8177,8258,8346,8424,8501,8575,8654,8744,8836,8928,9029,9103,9185,9286,9336,9402,9494,9581,9643,9707,9770,9893,9996,10100,10206", "endLines": "5,33,34,35,36,56,57,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "endColumns": "12,87,100,127,83,64,96,79,64,94,71,61,75,62,56,120,57,60,56,79,136,86,83,108,77,78,88,66,65,77,80,87,77,76,73,78,89,91,91,100,73,81,100,49,65,91,86,61,63,62,122,102,103,105,85", "endOffsets": "274,3087,3188,3316,3400,5826,5923,6431,6496,6591,6663,6725,6801,6864,6921,7042,7100,7161,7218,7298,7435,7522,7606,7715,7793,7872,7961,8028,8094,8172,8253,8341,8419,8496,8570,8649,8739,8831,8923,9024,9098,9180,9281,9331,9397,9489,9576,9638,9702,9765,9888,9991,10095,10201,10287"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f556f30d56993798d7ef134459fae338\\transformed\\core-1.8.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "111", "startColumns": "4", "startOffsets": "10374", "endColumns": "100", "endOffsets": "10470"}}]}]}