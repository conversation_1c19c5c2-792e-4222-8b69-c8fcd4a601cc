{"logs": [{"outputFile": "D:\\android project\\new big 22-06\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-ur\\values-ur.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84e8a3931c74ff232fa18e9c8d1ffe2\\transformed\\jetified-play-services-basement-18.1.0\\res\\values-ur\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "147", "endOffsets": "342"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4363", "endColumns": "151", "endOffsets": "4510"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8849a6fbe2ebfe5f57a82d8370a3dd43\\transformed\\jetified-firebase-messaging-21.0.1\\res\\values-ur\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "207", "endColumns": "101", "endOffsets": "308"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "6188", "endColumns": "105", "endOffsets": "6289"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\929536aae40a590fbb05df20b2f1b3b8\\transformed\\material-1.6.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,292,392,511,594,659,752,822,881,971,1040,1098,1167,1227,1291,1403,1462,1521,1576,1651,1774,1854,1938,2041,2123,2204,2291,2358,2424,2499,2579,2664,2731,2806,2883,2947,3041,3111,3200,3293,3367,3442,3532,3588,3655,3739,3823,3885,3949,4012,4112,4219,4313,4422", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,77,99,118,82,64,92,69,58,89,68,57,68,59,63,111,58,58,54,74,122,79,83,102,81,80,86,66,65,74,79,84,66,74,76,63,93,69,88,92,73,74,89,55,66,83,83,61,63,62,99,106,93,108,79", "endOffsets": "209,287,387,506,589,654,747,817,876,966,1035,1093,1162,1222,1286,1398,1457,1516,1571,1646,1769,1849,1933,2036,2118,2199,2286,2353,2419,2494,2574,2659,2726,2801,2878,2942,3036,3106,3195,3288,3362,3437,3527,3583,3650,3734,3818,3880,3944,4007,4107,4214,4308,4417,4497"}, "to": {"startLines": "2,33,34,35,36,56,57,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2991,3069,3169,3288,5711,5776,6294,6364,6423,6513,6582,6640,6709,6769,6833,6945,7004,7063,7118,7193,7316,7396,7480,7583,7665,7746,7833,7900,7966,8041,8121,8206,8273,8348,8425,8489,8583,8653,8742,8835,8909,8984,9074,9130,9197,9281,9365,9427,9491,9554,9654,9761,9855,9964", "endLines": "5,33,34,35,36,56,57,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "endColumns": "12,77,99,118,82,64,92,69,58,89,68,57,68,59,63,111,58,58,54,74,122,79,83,102,81,80,86,66,65,74,79,84,66,74,76,63,93,69,88,92,73,74,89,55,66,83,83,61,63,62,99,106,93,108,79", "endOffsets": "259,3064,3164,3283,3366,5771,5864,6359,6418,6508,6577,6635,6704,6764,6828,6940,6999,7058,7113,7188,7311,7391,7475,7578,7660,7741,7828,7895,7961,8036,8116,8201,8268,8343,8420,8484,8578,8648,8737,8830,8904,8979,9069,9125,9192,9276,9360,9422,9486,9549,9649,9756,9850,9959,10039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f556f30d56993798d7ef134459fae338\\transformed\\core-1.8.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "111", "startColumns": "4", "startOffsets": "10130", "endColumns": "100", "endOffsets": "10226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\02436aca31e270f81bb7f76ee8fb72ca\\transformed\\jetified-play-services-base-18.0.1\\res\\values-ur\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,457,579,686,822,945,1053,1153,1301,1407,1575,1699,1841,2006,2065,2128", "endColumns": "103,159,121,106,135,122,107,99,147,105,167,123,141,164,58,62,83", "endOffsets": "296,456,578,685,821,944,1052,1152,1300,1406,1574,1698,1840,2005,2064,2127,2211"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3371,3479,3643,3769,3880,4020,4147,4259,4515,4667,4777,4949,5077,5223,5392,5455,5522", "endColumns": "107,163,125,110,139,126,111,103,151,109,171,127,145,168,62,66,87", "endOffsets": "3474,3638,3764,3875,4015,4142,4254,4358,4662,4772,4944,5072,5218,5387,5450,5517,5605"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f2636cb968702bde5e4e1d445b2113c3\\transformed\\browser-1.3.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,368", "endColumns": "100,100,110,106", "endOffsets": "151,252,363,470"}, "to": {"startLines": "55,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "5610,5869,5970,6081", "endColumns": "100,100,110,106", "endOffsets": "5706,5965,6076,6183"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5a6f99c51ca9894dfbe548454cc2e836\\transformed\\appcompat-1.5.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,896,988,1082,1177,1271,1372,1466,1562,1656,1748,1840,1925,2033,2139,2241,2352,2453,2569,2734,2832", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "214,320,429,515,619,739,816,891,983,1077,1172,1266,1367,1461,1557,1651,1743,1835,1920,2028,2134,2236,2347,2448,2564,2729,2827,2913"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "264,378,484,593,679,783,903,980,1055,1147,1241,1336,1430,1531,1625,1721,1815,1907,1999,2084,2192,2298,2400,2511,2612,2728,2893,10044", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "373,479,588,674,778,898,975,1050,1142,1236,1331,1425,1526,1620,1716,1810,1902,1994,2079,2187,2293,2395,2506,2607,2723,2888,2986,10125"}}]}]}