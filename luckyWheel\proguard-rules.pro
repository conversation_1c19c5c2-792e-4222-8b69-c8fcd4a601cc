# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /Users/<USER>/Library/Android/sdk/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:
-keep class io.plaidapp.data.api.dribbble.model.** { *; }
-keepnames class rubikstudio.library

-keepattributes SourceFile, LineNumberTable

-keepattributes LocalVariableTable, LocalVariableTypeTable

-optimizations !method/removal/parameter

-repackageclasses rubikstudio.library


-dontoptimize
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose

# Preserve some attributes that may be required for reflection.
-keepattributes Annotation,Signature,InnerClasses,EnclosingMethod

-keep class rubikstudio.library.models.** { *; }

-ignorewarnings
# If your project uses WebView with J<PERSON>, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}
