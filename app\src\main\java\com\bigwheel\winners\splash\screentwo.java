 package com.bigwheel.winners.splash;

 import android.app.ProgressDialog;
 import android.content.ActivityNotFoundException;
 import android.content.Context;
 import android.content.DialogInterface;
 import android.content.Intent;
 import android.content.SharedPreferences;
 import android.net.ConnectivityManager;
 import android.net.Uri;
 import android.os.Bundle;
 import android.view.View;
 import android.widget.CheckBox;
 import android.widget.LinearLayout;
 import android.widget.TextView;
 import android.widget.Toast;

 import androidx.appcompat.app.AlertDialog;
 import androidx.appcompat.app.AppCompatActivity;

 import com.android.volley.Request;
 import com.android.volley.RequestQueue;
 import com.android.volley.Response;
 import com.android.volley.VolleyError;
 import com.android.volley.toolbox.StringRequest;
 import com.android.volley.toolbox.Volley;
 import com.bigwheel.winners.R;
 import com.google.android.gms.auth.api.signin.GoogleSignIn;
 import com.google.android.gms.auth.api.signin.GoogleSignInAccount;

 import java.util.HashMap;
 import java.util.Map;

// import com.hipro.spin.win.R;

 public class screentwo extends AppCompatActivity {

//    Button nextbtn;
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.activity_screentwo);
//        getSupportActionBar().hide();
//    clickevent();
//    }
//
//     void clickevent(){
//         nextbtn=findViewById(R.id.nextbtn);
//
//
//         nextbtn.setOnClickListener(new View.OnClickListener() {
//             @Override
//             public void onClick(View v) {
//
//                 Intent intent=new Intent(screentwo.this,screenthree.class);
//                 startActivity(intent);
//                 finish();
//             }
//         });
//     }

//     GoogleSignInOptions gso;
//     GoogleSignInClient mGoogleSignInClient;
     private static int RC_SIGN_IN = 100;
     LinearLayout gloginBtn;
     SharedPreferences pref;
     SharedPreferences.Editor editor;
     CheckBox accept;
     TextView textView12,tv_privacy;
     String privacyUrl ="https://sites.google.com/view/big-wheel-privacy-policy/home";
     @Override
     protected void onCreate(Bundle savedInstanceState) {
         super.onCreate(savedInstanceState);
         setContentView(R.layout.activity_screentwo);
         gloginBtn = findViewById(R.id.iv_googlelogin);
         textView12 = findViewById(R.id.textView12);
       //  textView13=findViewById(R.id.textView13);
         accept = findViewById(R.id.checkbox_terms);
      //   tv_terms=findViewById(R.id.tv_terms);
         tv_privacy = findViewById(R.id.tv_privacy);
         pref=getSharedPreferences("mypref",MODE_PRIVATE);
         editor=pref.edit();

         getSupportActionBar().hide();
//         gso = new GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN).requestEmail().build();
//         mGoogleSignInClient = GoogleSignIn.getClient(this, gso);

         GoogleSignInAccount account = GoogleSignIn.getLastSignedInAccount(this);
         if (account != null) {
             startActivity(new Intent(screentwo.this, screenOne.class));
             finish();
             return;
         }


         tv_privacy.setOnClickListener(new View.OnClickListener() {
             @Override
             public void onClick(View view) {
                 try {
                     Intent myIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(privacyUrl));
                     startActivity(myIntent);
                 } catch (ActivityNotFoundException e) {
                     Toast.makeText(screentwo.this, "No application can handle this request."
                             + " Please install a webbrowser",  Toast.LENGTH_SHORT).show();
                     e.printStackTrace();
                 }
             }
         });

         textView12.setOnClickListener(new View.OnClickListener() {
             @Override
             public void onClick(View view) {
                 if(!accept.isChecked()) {
                     accept.setChecked(true);
                 }else
                 {
                     accept.setChecked(false);
                 }
             }
         });


         gloginBtn.setOnClickListener(new View.OnClickListener() {
             @Override
             public void onClick(View view) {
                 if(!accept.isChecked()) {
                     datafrom();
                    // Toast.makeText(screentwo.this, "Please Accept Terms & Conditions", Toast.LENGTH_SHORT).show();
                 }
                 else
                 {
                     datafrom();


                 }
             }
         });
     }

     public void datafrom(){

         String personName = "abc";
         String personEmail = "<EMAIL>";
         String personId = "115959587085415341294";
         String personPhoto = "https://lh3.googleusercontent.com/a/ALm5wu0ZFCQ1fz9J8anutmFZMDL8zw7Z-IEyjDcL8qTT";

         editor.putString("uname",personName);
         editor.putString("uemail",personEmail);
         editor.putString("uid",personId);
         if(personPhoto!=null)
             editor.putString("upic",personPhoto.toString());
         else
             editor.putString("upic",null);
         editor.apply();


         ProgressDialog progressDialog = new ProgressDialog(screentwo.this);
         progressDialog.setCancelable(false);
         progressDialog.setMessage("Please wait...");
         progressDialog.show();

         RequestQueue queue = Volley.newRequestQueue(this);
         StringRequest stringRequest = new StringRequest(Request.Method.POST, pref.getString("baseurl","")+"registration.php",
                 new Response.Listener<String>() {
                     @Override
                     public void onResponse(String response) {

                         progressDialog.dismiss();
                         if(response.equals("2"))
                         {

                             new AlertDialog.Builder(screentwo.this)
                                     .setTitle("Error!")
                                     .setCancelable(false)
                                     .setMessage("Something went wrong!")
                                     .setPositiveButton("Retry", new DialogInterface.OnClickListener() {
                                         public void onClick(DialogInterface dialog, int which) {
                                             // Continue with delete operation
                                             Intent intent = new Intent(screentwo.this, screentwo.class);
                                             startActivity(intent);
                                             finish();
                                         }
                                     }).setNegativeButton("Exit", new DialogInterface.OnClickListener() {
                                 @Override
                                 public void onClick(DialogInterface dialogInterface, int i) {
                                     finish();
                                 }
                             }).setIcon(android.R.drawable.ic_dialog_alert)
                                     .show();
                         }else{

                             if(response.contains("spindex"))
                             {
                                 String[] strr=response.split("=");
                                 int value = Integer.parseInt(strr[1]);

                                 editor.putInt("spindex",0);
                                 editor.apply();
                             }else
                             {
                                 editor.putInt("spindex",0);
                                 editor.apply();
                             }
                             Intent intent = new Intent(screentwo.this, screenOne.class);
                             startActivity(intent);
                             finish();

                         }
                     }
                 }, new Response.ErrorListener() {
             @Override
             public void onErrorResponse(VolleyError error) {
                 progressDialog.dismiss();

                 new AlertDialog.Builder(screentwo.this)
                         .setTitle("Error!")
                         .setMessage("Check Your Internet Connection")
                         .setCancelable(false)
                         .setPositiveButton("Retry", new DialogInterface.OnClickListener() {
                             public void onClick(DialogInterface dialog, int which) {
                                 // Continue with delete operation
                                 Intent intent = new Intent(screentwo.this, screentwo.class);
                                 startActivity(intent);
                                 finish();
                             }
                         })
                         // A null listener allows the button to dismiss the dialog and take no further action.
                         .setNegativeButton("Exit", new DialogInterface.OnClickListener() {
                             @Override
                             public void onClick(DialogInterface dialogInterface, int i) {
                                 finish();
                             }
                         })
                         .setIcon(android.R.drawable.ic_dialog_alert)
                         .show();
             }
         }) {
             @Override
             protected Map<String, String> getParams() {
                 Map<String, String> params = new HashMap<String, String>();
                 params.put("uname", personName);
                 params.put("email", personEmail);
                 params.put("uid", personId);
                 params.put("img", String.valueOf(personPhoto));
                 return params;
             }
         };

         queue.add(stringRequest);
     }

     private boolean isNetworkConnected() {
         ConnectivityManager cm = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);

         return cm.getActiveNetworkInfo() != null && cm.getActiveNetworkInfo().isConnected();
     }


}