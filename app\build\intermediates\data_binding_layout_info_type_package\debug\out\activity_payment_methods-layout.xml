<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_payment_methods" modulePackage="com.bigwheel.winners" filePath="app\src\main\res\layout\activity_payment_methods.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_payment_methods_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="293" endOffset="14"/></Target><Target id="@+id/buy_price_amout" view="TextView"><Expressions/><location startLine="52" startOffset="12" endLine="61" endOffset="42"/></Target><Target id="@+id/radioAppChoice" view="RadioGroup"><Expressions/><location startLine="84" startOffset="12" endLine="230" endOffset="24"/></Target><Target id="@+id/gpay_radio" view="RadioButton"><Expressions/><location startLine="99" startOffset="20" endLine="103" endOffset="48"/></Target><Target id="@+id/phonepe_radio" view="RadioButton"><Expressions/><location startLine="147" startOffset="20" endLine="150" endOffset="62"/></Target><Target id="@+id/paytm_radio" view="RadioButton"><Expressions/><location startLine="193" startOffset="20" endLine="196" endOffset="62"/></Target><Target id="@+id/tutorial" view="TextView"><Expressions/><location startLine="238" startOffset="8" endLine="245" endOffset="38"/></Target><Target id="@+id/button_pay" view="LinearLayout"><Expressions/><location startLine="249" startOffset="4" endLine="267" endOffset="18"/></Target><Target id="@+id/txt_contactus" view="TextView"><Expressions/><location startLine="270" startOffset="4" endLine="280" endOffset="33"/></Target><Target id="@+id/termstxtpay" view="TextView"><Expressions/><location startLine="282" startOffset="4" endLine="292" endOffset="33"/></Target></Targets></Layout>