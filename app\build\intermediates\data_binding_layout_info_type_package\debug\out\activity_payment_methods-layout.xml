<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_payment_methods" modulePackage="com.bigwheel.winners" filePath="app\src\main\res\layout\activity_payment_methods.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_payment_methods_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="201" endOffset="14"/></Target><Target id="@+id/buy_price_amout" view="TextView"><Expressions/><location startLine="52" startOffset="12" endLine="61" endOffset="42"/></Target><Target id="@+id/radioAppChoice" view="RadioGroup"><Expressions/><location startLine="93" startOffset="12" endLine="138" endOffset="24"/></Target><Target id="@+id/upi_radio" view="RadioButton"><Expressions/><location startLine="98" startOffset="16" endLine="106" endOffset="46"/></Target><Target id="@+id/payumoney_radio" view="RadioButton"><Expressions/><location startLine="117" startOffset="16" endLine="125" endOffset="46"/></Target><Target id="@+id/tutorial" view="TextView"><Expressions/><location startLine="146" startOffset="8" endLine="153" endOffset="38"/></Target><Target id="@+id/button_pay" view="LinearLayout"><Expressions/><location startLine="157" startOffset="4" endLine="175" endOffset="18"/></Target><Target id="@+id/txt_contactus" view="TextView"><Expressions/><location startLine="178" startOffset="4" endLine="188" endOffset="33"/></Target><Target id="@+id/termstxtpay" view="TextView"><Expressions/><location startLine="190" startOffset="4" endLine="200" endOffset="33"/></Target></Targets></Layout>