R_DEF: Internal format may change without notice
local
anim button_press
anim dialog_in
anim dialog_out
anim plan_select
anim popup_enter
anim popup_exit
array com_google_android_gms_fonts_certs
array com_google_android_gms_fonts_certs_dev
array com_google_android_gms_fonts_certs_prod
array preloaded_fonts
color bg_card
color bg_overlay
color bg_primary
color bg_secondary
color black
color blue
color error_red
color gradient_primary_end
color gradient_primary_start
color gradient_secondary_end
color gradient_secondary_start
color gradient_success_end
color gradient_success_start
color gradient_warning_end
color gradient_warning_start
color gray
color info_blue
color light_white
color luxury_bronze
color newWhitr
color payumoney_black
color plan_basic
color plan_basic_border
color plan_popular
color plan_popular_border
color plan_premium
color plan_premium_border
color plan_vip
color plan_vip_border
color premium_gold
color premium_gold_dark
color premium_gold_light
color purple_200
color purple_500
color purple_700
color purple_7001
color red_btn_bg_color
color shadow_dark
color shadow_light
color shadow_medium
color success_green
color teal_200
color teal_700
color text_accent
color text_primary
color text_secondary
color text_white
color themeBgColor
color themeColor
color themeColor2
color themeColor22
color tran
color warning_orange
color white
drawable all_upi
drawable all_wallets
drawable bg_card_basic
drawable bg_card_popular
drawable bg_card_premium
drawable bg_card_selected
drawable bg_card_vip
drawable bg_discount_badge
drawable bg_plan_basic
drawable bg_plan_popular
drawable bg_plan_premium
drawable bg_plan_vip
drawable bg_premium_button
drawable bg_premium_popup
drawable bg_rounded_button
drawable cardbackground
drawable crowns
drawable female
drawable ic_baseline_backspace_24
drawable ic_baseline_close_24
drawable ic_baseline_privacy_tip_24
drawable ic_delete
drawable ic_google_pay
drawable ic_group
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_menu
drawable ic_paytm
drawable ic_phonepe
drawable ic_play
drawable ic_rounded_theme_blue
drawable ic_rounded_theme_white
drawable ic_rounded_theme_white_black
drawable ic_rounded_theme_yellow
drawable ic_rounded_theme_yellow1
drawable ic_splash_text
drawable ic_xml_button_
drawable icgrop
drawable insurance
drawable logo
drawable logout
drawable mail
drawable male
drawable more
drawable roundedbutton
drawable search
drawable share
drawable sp_bg
drawable spinbg
drawable spinbg2
drawable spinbg3
drawable spinbg4
drawable splashbg
drawable step1
drawable step2
drawable step3
drawable step4
drawable step5
drawable step6
drawable strok
drawable teer
drawable teer2
drawable terms
drawable trophy
drawable wheels
drawable wheelss
drawable winner
font font
font poppins
font poppins_bold
font poppins_light
font poppins_medium
font poppins_semibold
font stardos_stencil
font stardosstencil
id addMoneyLayout
id agreebtn
id animationView
id b1
id b2
id btnAddItem
id btnAddMoney
id btnAddMoneys
id btnDeposite
id btnGetMoney
id btnLater
id btnRateNow
id btnSignIn
id btnSignUp
id btnSpin
id btnWithdrawFinal
id btnWithdraws
id btn_cancel_payment
id btn_closeaddmoney
id btn_closewithdraw
id btn_menu
id btn_ok
id button_pay
id buy_price_amout
id card_name_et
id card_view
id checkbox_terms
id clMain
id close
id constraintLayout
id constraintLayout2
id cursorView
id cvDiscountInfo
id date
id deposite_100
id deposite_150
id deposite_200
id deposite_50
id drawer_layout
id drwa
id etAmount
id etDeposite
id etFurl
id etKey
id etMerchantName
id etNumber
id etPassword
id etPhone
id etReviewOrderKey
id etReviewOrderValue
id etSalt
id etSurePayCount
id etSurl
id etUserCredential
id etWithdraw
id etWithdrawAmmount
id et_api_version
id et_api_version_value
id et_billingAmount
id et_billingAmount_value
id et_billingCurrency
id et_billingCurrency_value
id et_billingCycle
id et_billingCycle_value
id et_billingInterval
id et_billingInterval_value
id et_paymentEndDate
id et_paymentEndDate_value
id et_paymentStartDate
id et_paymentStartDate_value
id et_remarks
id et_remarks_value
id et_si
id et_si_value
id finaldata
id g1
id g2
id g3
id gpay_radio
id hr
id imageView
id ivDeleteOrderItem
id iv_googlelogin
id layout_google_pay
id layout_paytm
id layout_phone_pay
id layout_si_details
id linearLayout
id linearLayout2
id linearLayout3
id linearLayout4
id linearLayout5
id linearLayout6
id linearLayout9
id llPricingPlans
id logout
id loss
id luckyWheel
id name
id nav_contactus
id nav_home
id nav_logout
id nav_moreapps
id nav_privacy
id nav_share
id nav_tc
id nav_view
id nextbtn
id okbtn
id pay_now_button
id payment_amount
id paytm_radio
id phonepe_radio
id plan100
id plan150
id plan200
id plan50
id price1
id price2
id price3
id price4
id privacy
id progressBar
id radioAppChoice
id radioBtnProduction
id radioBtnTest
id radioButton11
id radioButton12
id radioButton13
id radioButton14
id radioGrpEnv
id rlReviewOrder
id rl_si_header
id rvReviewOrder
id sp_free_trial
id sp_free_trial_text
id success_message
id switchAutoApprove
id switchAutoSelectOtp
id switchDiableCBDialog
id switchDiableUiDialog
id switchEnableReviewOrder
id switchHideCbToolBar
id switchShowGooglePay
id switchShowPaytm
id switchShowPhonePe
id switch_si_on_off
id swith
id terms
id termstxtpay
id textView
id textView11
id textView12
id textView13
id textView2
id textView3
id textView4
id textView5
id textView6
id textView7
id textView8
id textView9
id textinfo
id tilAmount
id tilFurl
id tilKey
id tilMerchantName
id tilPhone
id tilSalt
id tilSurePayCount
id tilSurl
id tilUserCredential
id toolbar
id tranAmu
id tranID
id tranemail
id tutorial
id tvDepositeAmmount
id tvDescription
id tvForgotPassword
id tvTitle
id tvWiningeAmmount
id tv_or
id tv_pay_via_si
id tv_privacy
id tv_terms
id txt_contactus
id useremail
id userid
id useridname
id userimg
id username
id wheelLayout
id winamount
id winner
id withdrawMoneyLayout
layout activity_google_login
layout activity_main
layout activity_mains
layout activity_payment_methods
layout activity_payment_result
layout activity_payment_result_payu_money
layout activity_payment_tutorial
layout activity_payment_via_u_p_i
layout activity_screen_one
layout activity_screenfive
layout activity_screenfour
layout activity_screensix
layout activity_screenthree
layout activity_screentwo
layout activity_splash
layout activity_splash_screen
layout areyousurdilog
layout dialog_payment_options
layout dialog_payment_success
layout dialog_rate_us
layout drawerlayout
layout layout_si_details
layout nav_header_navigation_menu
layout review_order_row_layout
menu activity_navigation_menu_drawer
mipmap ic_launcher
mipmap ic_launcher_round
raw audio
raw fail
raw failmusic
raw lossanim
raw sucesso
raw wheelsong
raw winner
raw winningsong
string LOADING
string and_i_agree_with_its
string api_version
string app_name
string billingAmount
string billingCurrency
string billingCycle
string billingInterval
string default_web_client_id
string drawer_close
string drawer_open
string firebase_database_url
string free_trial
string gcm_defaultSenderId
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string pay_via_si
string paymentEndDate
string paymentStartDate
string phone_number
string project_id
string remarks
string si
string si_date_hint
string sign_in_with_google
string some_error_occurred
string transaction_cancelled_by_user
style BottomSheetDialogTheme
style BottomSheetStyle
style ButtonStyle
style MyDialogTheme
style NavigationView
style Theme.BigWheel
