
package com.hipro.spin.win.pay

import androidx.appcompat.app.AppCompatActivity


//import android.content.Context
//import android.content.Intent
//import android.content.SharedPreferences
//import android.os.Bundle
//import android.os.SystemClock
//import android.text.TextUtils
//import android.util.Log
//import android.view.View
//import android.webkit.WebView
//import android.widget.ArrayAdapter
//import android.widget.CompoundButton
//import android.widget.RadioGroup
//
//import androidx.recyclerview.widget.LinearLayoutManager
//import com.druk.developers.spin.to.spinandwin.R
//import com.google.android.material.snackbar.Snackbar
//import com.hipro.spin.win.R
//import com.payu.base.models.*
//import com.payu.checkoutpro.PayUCheckoutPro
//import com.payu.checkoutpro.models.PayUCheckoutProConfig
//import com.payu.checkoutpro.utils.PayUCheckoutProConstants
//import com.payu.checkoutpro.utils.PayUCheckoutProConstants.CP_HASH_NAME
//import com.payu.checkoutpro.utils.PayUCheckoutProConstants.CP_HASH_STRING
//import com.payu.ui.model.listeners.PayUCheckoutProListener
//import com.payu.ui.model.listeners.PayUHashGenerationListener
//import kotlinx.android.synthetic.main.activity_mains.*
//import kotlinx.android.synthetic.main.layout_si_details.*
//
class MainActivitys : AppCompatActivity() {
//
//    private var email: String = "<EMAIL>"
//    private val phone = "9999999999"
//    private val merchantName = "IT Services"
//    private val surl = "https://payuresponse.firebaseapp.com/success"
//    private val furl = "https://payuresponse.firebaseapp.com/failure"
//    private var amount = ""
//
//    //Test Key and Salt
//    private val testKey = "nrEh61"
//    private val testSalt = "kqZ5HHMFxq6G6IYEx3rkrnvwmxg67BDU"
//
//    //Prod Key and Salt
//    private val prodKey = "nrEh61"
//    private val prodSalt = "kqZ5HHMFxq6G6IYEx3rkrnvwmxg67BDU"
//
//    //  private lateinit var binding: ActivityMainBinding
//
//    // variable to track event time
//    private var mLastClickTime: Long = 0
//    private var reviewOrderAdapter: ReviewOrderRecyclerViewAdapter? = null
//    private var billingCycle = arrayOf(
//            "DAILY",
//            "WEEKLY",
//            "MONTHLY",
//            "YEARLY",
//            "ONCE",
//            "ADHOC"
//    )
//
//    private val sharedPrefFile = "mypref"
//    private  lateinit var sharedPreferences: SharedPreferences
//    var mytype: String = ""
//    private  lateinit var editor: SharedPreferences.Editor
//
//    override fun onCreate(savedInstanceState: Bundle?) {
//        super.onCreate(savedInstanceState)
//        setContentView(R.layout.activity_mains)
//
//
//        sharedPreferences = this.getSharedPreferences(sharedPrefFile, Context.MODE_PRIVATE)
//        editor = sharedPreferences.edit()
//
//        amount =intent.getStringExtra("amount").toString()
//        Log.d("amountf", "onCreate: sa"+amount);
//
//
//
//        //if (i == 0) {
//        mytype = "Money Added to wallet"
//
//        /*  } else if (i == 1) {
//              mytype = "3 Month Subscription"
//              amount = ""+sharedPreferences.getString("month2","199")+".00"
//
//          } else if (i == 2) {
//              mytype = "6 Month Subscription"
//              amount = ""+sharedPreferences.getString("month1","299")+".00"
//
//          }*/
//
//        editor.putString("tranAmu",amount)
//        editor.apply()
//
//        initializeSIView()
//        setInitalData()
//        initListeners()
//
//
//        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
//            return
//        }
//        mLastClickTime = SystemClock.elapsedRealtime()
//
//        val paymentParams = preparePayUBizParams()
//        initUiSdk(paymentParams)
//
//        finish()
//    }
//
//    private fun initializeSIView() {
//        switch_si_on_off.setOnCheckedChangeListener { buttonView, isChecked ->
//            if (isChecked) {
//                layout_si_details.visibility = View.VISIBLE
//            } else {
//                layout_si_details.visibility = View.GONE
//            }
//        }
//
//        val adapter: ArrayAdapter<*> = ArrayAdapter<Any?>(
//                this,
//                android.R.layout.simple_spinner_item,
//                billingCycle
//        )
//        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
//        et_billingCycle_value.adapter = adapter
//    }
//
//    private fun setInitalData() {
//
//        email = ""+ sharedPreferences.getString("uemail","<EMAIL>")
//
//        updateProdEnvDetails()
//        etSurl.setText(surl)
//        etFurl.setText(furl)
//        etMerchantName.setText(merchantName)
//        etPhone.setText(phone)
//        etAmount.setText(amount)
//        etUserCredential.setText("${etKey.text}:$email")
//        etSurePayCount.setText("0")
//    }
//
//    private fun initListeners() {
//        radioGrpEnv.setOnCheckedChangeListener { radioGroup: RadioGroup, i: Int ->
//            when (i) {
//                R.id.radioBtnTest -> updateTestEnvDetails()
//                R.id.radioBtnProduction -> updateProdEnvDetails()
//                else -> updateTestEnvDetails()
//            }
//        }
//
//        switchEnableReviewOrder.setOnCheckedChangeListener { compoundButton: CompoundButton, b: Boolean ->
//            if (b) showReviewOrderView() else hideReviewOrderView()
//        }
//
//        btnAddItem.setOnClickListener { reviewOrderAdapter?.addRow() }
//    }
//
//    private fun hideReviewOrderView() {
//        rlReviewOrder.visibility = View.GONE
//        reviewOrderAdapter = null
//    }
//
//    private fun showReviewOrderView() {
//        rlReviewOrder.visibility = View.VISIBLE
//        reviewOrderAdapter = ReviewOrderRecyclerViewAdapter()
//        rvReviewOrder.layoutManager = LinearLayoutManager(this)
//        rvReviewOrder.adapter = reviewOrderAdapter
//    }
//
//    private fun updateTestEnvDetails() {
//        //For testing
//        etKey.setText(testKey)
//        etSalt.setText(testSalt)
//    }
//
//    private fun updateProdEnvDetails() {
//        //For Production
//        etKey.setText(prodKey)
//        etSalt.setText(prodSalt)
//    }
//
//    fun startPayment(view: View) {
//
//        // Preventing multiple clicks, using threshold of 1 second
//        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
//            return
//        }
//        mLastClickTime = SystemClock.elapsedRealtime()
//
//        val paymentParams = preparePayUBizParams()
//        initUiSdk(paymentParams)
//
//    }
//
//    fun preparePayUBizParams(): PayUPaymentParams {
//        val vasForMobileSdkHash = HashGenerationUtils.generateHashFromSDK(
//            "${etKey.text}|${PayUCheckoutProConstants.CP_VAS_FOR_MOBILE_SDK}|${PayUCheckoutProConstants.CP_DEFAULT}|",
//            etSalt.text.toString()
//        )
//        val paymenRelatedDetailsHash = HashGenerationUtils.generateHashFromSDK(
//            "${etKey.text}|${PayUCheckoutProConstants.CP_PAYMENT_RELATED_DETAILS_FOR_MOBILE_SDK}|${etUserCredential.text}|",
//            etSalt.text.toString()
//        )
//
//        val additionalParamsMap: HashMap<String, Any?> = HashMap()
//        additionalParamsMap[PayUCheckoutProConstants.CP_UDF1] = "udf1"
//        additionalParamsMap[PayUCheckoutProConstants.CP_UDF2] = "udf2"
//        additionalParamsMap[PayUCheckoutProConstants.CP_UDF3] = "udf3"
//        additionalParamsMap[PayUCheckoutProConstants.CP_UDF4] = "udf4"
//        additionalParamsMap[PayUCheckoutProConstants.CP_UDF5] = "udf5"
//        additionalParamsMap[PayUCheckoutProConstants.CP_VAS_FOR_MOBILE_SDK] = vasForMobileSdkHash
//        additionalParamsMap[PayUCheckoutProConstants.CP_PAYMENT_RELATED_DETAILS_FOR_MOBILE_SDK] =
//                paymenRelatedDetailsHash
//
//
//        /*   var siDetails: PayUSIParams? = null
//           if (switch_si_on_off.isChecked) {
//               siDetails = PayUSIParams.Builder()
//                       .setIsFreeTrial(sp_free_trial.isChecked)
//                       .setBillingAmount(et_billingAmount_value.text.toString())
//                       .setBillingCycle(PayUBillingCycle.valueOf(et_billingCycle_value.selectedItem.toString()))
//                       .setBillingInterval(et_billingInterval_value.text.toString().toInt())
//                       .setPaymentStartDate(et_paymentStartDate_value.text.toString())
//                       .setPaymentEndDate(et_paymentEndDate_value.text.toString())
//                       .setRemarks(et_remarks_value.text.toString())
//                       .build()
//           }*/
//
//        return PayUPaymentParams.Builder().setAmount(etAmount.text.toString())
//                .setIsProduction(true)
//                .setKey(etKey.text.toString())
//                .setProductInfo(mytype)
//                .setPhone(etPhone.text.toString())
//                .setTransactionId("TXN"+System.currentTimeMillis().toString())
//                .setFirstName(sharedPreferences.getString("uname","test"))
//                .setEmail(email)
//                .setSurl(etSurl.text.toString())
//                .setFurl(etFurl.text.toString())
//                .setUserCredential(etUserCredential.text.toString())
//                .setAdditionalParams(additionalParamsMap)
//                //.setPayUSIParams(siDetails)
//                .build()
//    }
//
//    private fun initUiSdk(payUPaymentParams: PayUPaymentParams) {
//        PayUCheckoutPro.open(
//                this,
//                payUPaymentParams,
//                getCheckoutProConfig(),
//                object : PayUCheckoutProListener {
//
//                    override fun onPaymentSuccess(response: Any) {
//                        processResponse(response)
//                    }
//
//                    override fun onPaymentFailure(response: Any) {
//                        processResponse(response)
//                    }
//
//                    override fun onPaymentCancel(isTxnInitiated: Boolean) {
//                        //  showSnackBar(resources.getString(R.string.transaction_cancelled_by_user))
//                        val i = Intent(this@MainActivitys, PaymentResultPayuMoney::class.java).apply {
//                            putExtra("data", "fail")
//                        }
//
//                        startActivity(i)
//                        finish()
//                    }
//
//                    override fun onError(errorResponse: ErrorResponse) {
//
//                        val errorMessage: String
//                        if (errorResponse != null && errorResponse.errorMessage != null && errorResponse.errorMessage!!.isNotEmpty())
//                            errorMessage = errorResponse.errorMessage!!
//                        else
//                            errorMessage = resources.getString(R.string.some_error_occurred)
//                        showSnackBar(errorMessage)
//                    }
//
//                    override fun generateHash(
//                            map: HashMap<String, String?>,
//                            hashGenerationListener: PayUHashGenerationListener
//                    ) {
//                        if (map.containsKey(CP_HASH_STRING)
//                                && map.containsKey(CP_HASH_STRING) != null
//                                && map.containsKey(CP_HASH_NAME)
//                                && map.containsKey(CP_HASH_NAME) != null
//                        ) {
//
//                            val hashData = map[CP_HASH_STRING]
//                            val hashName = map[CP_HASH_NAME]
//
//                            val hash: String? =
//                                HashGenerationUtils.generateHashFromSDK(
//                                    hashData!!,
//                                    etSalt.text.toString()
//                                )
//                            if (!TextUtils.isEmpty(hash)) {
//                                val hashMap: HashMap<String, String?> = HashMap()
//                                hashMap[hashName!!] = hash!!
//                                hashGenerationListener.onHashGenerated(hashMap)
//                            }
//                        }
//                    }
//
//                    override fun setWebViewProperties(webView: WebView?, bank: Any?) {
//                    }
//                })
//    }
//
//    private fun getCheckoutProConfig(): PayUCheckoutProConfig {
//        val checkoutProConfig = PayUCheckoutProConfig()
//        checkoutProConfig.paymentModesOrder = getCheckoutOrderList()
//        //checkoutProConfig.offerDetails = getOfferDetailsList()
//        checkoutProConfig.showCbToolbar = !switchHideCbToolBar.isChecked
//        checkoutProConfig.autoSelectOtp = switchAutoSelectOtp.isChecked
//        checkoutProConfig.autoApprove = switchAutoApprove.isChecked
//        checkoutProConfig.surePayCount = etSurePayCount.text.toString().toInt()
//        checkoutProConfig.cartDetails = reviewOrderAdapter?.getOrderDetailsList()
//        checkoutProConfig.showExitConfirmationOnPaymentScreen =
//                !switchDiableCBDialog.isChecked
//        checkoutProConfig.showExitConfirmationOnCheckoutScreen =
//                !switchDiableUiDialog.isChecked
//        checkoutProConfig.merchantName = "Spin & Win"
//        checkoutProConfig.merchantLogo = R.drawable.crowns
//        return checkoutProConfig
//    }
//
//    private fun getOfferDetailsList(): ArrayList<PayUOfferDetails> {
//        val offerDetails = ArrayList<PayUOfferDetails>()
//        offerDetails.add(PayUOfferDetails().also {
//            it.offerTitle = " Instant discount of Rs.2"
//            it.offerDescription = "Get Instant dicount of Rs.2 on all Credit and Debit card transactions"
//            it.offerKey = "OfferKey@9227"
//            it.offerPaymentTypes = ArrayList<PaymentType>().also {
//                it.add(PaymentType.CARD)
//            }
//        })
//        offerDetails.add(PayUOfferDetails().also {
//            it.offerTitle = " Instant discount of Rs.2"
//            it.offerDescription = "Get Instant dicount of Rs.2 on all NetBanking transactions"
//            it.offerKey = "TestOffer100@9229"
//            it.offerPaymentTypes = ArrayList<PaymentType>().also {
//                it.add(PaymentType.NB)
//            }
//        })
//
//        return offerDetails
//    }
//
//    private fun getCheckoutOrderList(): ArrayList<PaymentMode> {
//        val checkoutOrderList = ArrayList<PaymentMode>()
//
//
//        checkoutOrderList.add(
//                PaymentMode(
//                        PaymentType.WALLET,
//                        PayUCheckoutProConstants.CP_PAYTM
//                )
//        )
//        /*  if (switchShowGooglePay.isChecked) checkoutOrderList.add(
//                  PaymentMode(
//                          PaymentType.UPI,
//                          PayUCheckoutProConstants.CP_GOOGLE_PAY
//                  )
//          )
//          if (switchShowPhonePe.isChecked) checkoutOrderList.add(
//                  PaymentMode(
//                          PaymentType.WALLET,
//                          PayUCheckoutProConstants.CP_PHONEPE
//                  )
//          )
//          if (switchShowPaytm.isChecked) checkoutOrderList.add(
//                  PaymentMode(
//                          PaymentType.WALLET,
//                          PayUCheckoutProConstants.CP_PAYTM
//                  )
//          )*/
//        return checkoutOrderList
//    }
//
//    private fun showSnackBar(message: String) {
//        Snackbar.make(clMain, message, Snackbar.LENGTH_LONG).show()
//    }
//
//    private fun processResponse(response: Any) {
//        response as HashMap<*, *>
//        Log.d(
//                BaseApiLayerConstants.SDK_TAG,
//                "payuResponse ; > " + response[PayUCheckoutProConstants.CP_PAYU_RESPONSE]
//                        + ", merchantResponse : > " + response[PayUCheckoutProConstants.CP_MERCHANT_RESPONSE]
//        )
//
//        val intent = Intent(this, PaymentResultPayuMoney::class.java).apply {
//            putExtra("data", response.get(
//                    PayUCheckoutProConstants.CP_MERCHANT_RESPONSE
//            ).toString())
//        }
//        startActivity(intent)
//        finish()
//
//
//
//
//        /*  AlertDialog.Builder(this, R.style.Theme_AppCompat_Light_Dialog_Alert)
//                  .setCancelable(false)
//                  .setMessage(
//                          "Payu's Data : " + response.get(PayUCheckoutProConstants.CP_PAYU_RESPONSE) + "\n\n\n Merchant's Data: " + response.get(
//                                  PayUCheckoutProConstants.CP_MERCHANT_RESPONSE
//                          )
//                  )
//                  .setPositiveButton(
//                          android.R.string.ok
//                  ) { dialog, cancelButton -> dialog.dismiss() }.show()*/
//    }
}