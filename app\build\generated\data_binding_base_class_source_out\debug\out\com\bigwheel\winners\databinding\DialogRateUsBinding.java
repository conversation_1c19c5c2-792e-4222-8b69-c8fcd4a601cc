// Generated by view binder compiler. Do not edit!
package com.bigwheel.winners.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airbnb.lottie.LottieAnimationView;
import com.bigwheel.winners.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogRateUsBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final LinearLayout btnLater;

  @NonNull
  public final LinearLayout btnRateNow;

  @NonNull
  public final LottieAnimationView loss;

  @NonNull
  public final TextView tvDescription;

  @NonNull
  public final TextView tvTitle;

  @NonNull
  public final TextView winamount;

  @NonNull
  public final LottieAnimationView winner;

  private DialogRateUsBinding(@NonNull CardView rootView, @NonNull LinearLayout btnLater,
      @NonNull LinearLayout btnRateNow, @NonNull LottieAnimationView loss,
      @NonNull TextView tvDescription, @NonNull TextView tvTitle, @NonNull TextView winamount,
      @NonNull LottieAnimationView winner) {
    this.rootView = rootView;
    this.btnLater = btnLater;
    this.btnRateNow = btnRateNow;
    this.loss = loss;
    this.tvDescription = tvDescription;
    this.tvTitle = tvTitle;
    this.winamount = winamount;
    this.winner = winner;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogRateUsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogRateUsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_rate_us, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogRateUsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnLater;
      LinearLayout btnLater = ViewBindings.findChildViewById(rootView, id);
      if (btnLater == null) {
        break missingId;
      }

      id = R.id.btnRateNow;
      LinearLayout btnRateNow = ViewBindings.findChildViewById(rootView, id);
      if (btnRateNow == null) {
        break missingId;
      }

      id = R.id.loss;
      LottieAnimationView loss = ViewBindings.findChildViewById(rootView, id);
      if (loss == null) {
        break missingId;
      }

      id = R.id.tvDescription;
      TextView tvDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvDescription == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      id = R.id.winamount;
      TextView winamount = ViewBindings.findChildViewById(rootView, id);
      if (winamount == null) {
        break missingId;
      }

      id = R.id.winner;
      LottieAnimationView winner = ViewBindings.findChildViewById(rootView, id);
      if (winner == null) {
        break missingId;
      }

      return new DialogRateUsBinding((CardView) rootView, btnLater, btnRateNow, loss, tvDescription,
          tvTitle, winamount, winner);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
