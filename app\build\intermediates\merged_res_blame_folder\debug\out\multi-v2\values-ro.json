{"logs": [{"outputFile": "D:\\android project\\new big 22-06\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-ro\\values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f2636cb968702bde5e4e1d445b2113c3\\transformed\\browser-1.3.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,379", "endColumns": "106,101,114,104", "endOffsets": "157,259,374,479"}, "to": {"startLines": "56,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "5725,5993,6095,6210", "endColumns": "106,101,114,104", "endOffsets": "5827,6090,6205,6310"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8849a6fbe2ebfe5f57a82d8370a3dd43\\transformed\\jetified-firebase-messaging-21.0.1\\res\\values-ro\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "207", "endColumns": "101", "endOffsets": "308"}, "to": {"startLines": "62", "startColumns": "4", "startOffsets": "6315", "endColumns": "105", "endOffsets": "6416"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5a6f99c51ca9894dfbe548454cc2e836\\transformed\\appcompat-1.5.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1855,1938,2050,2158,2258,2372,2478,2584,2748,2851", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "221,325,438,522,626,747,832,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1850,1933,2045,2153,2253,2367,2473,2579,2743,2846,2930"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "326,447,551,664,748,852,973,1058,1138,1229,1322,1417,1511,1611,1704,1799,1893,1984,2076,2159,2271,2379,2479,2593,2699,2805,2969,10287", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "442,546,659,743,847,968,1053,1133,1224,1317,1412,1506,1606,1699,1794,1888,1979,2071,2154,2266,2374,2474,2588,2694,2800,2964,3067,10366"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84e8a3931c74ff232fa18e9c8d1ffe2\\transformed\\jetified-play-services-basement-18.1.0\\res\\values-ro\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4485", "endColumns": "144", "endOffsets": "4625"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\02436aca31e270f81bb7f76ee8fb72ca\\transformed\\jetified-play-services-base-18.0.1\\res\\values-ro\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,452,577,682,829,957,1076,1181,1339,1445,1600,1728,1870,2032,2099,2162", "endColumns": "102,155,124,104,146,127,118,104,157,105,154,127,141,161,66,62,77", "endOffsets": "295,451,576,681,828,956,1075,1180,1338,1444,1599,1727,1869,2031,2098,2161,2239"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3465,3572,3732,3861,3970,4121,4253,4376,4630,4792,4902,5061,5193,5339,5505,5576,5643", "endColumns": "106,159,128,108,150,131,122,108,161,109,158,131,145,165,70,66,81", "endOffsets": "3567,3727,3856,3965,4116,4248,4371,4480,4787,4897,5056,5188,5334,5500,5571,5638,5720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f556f30d56993798d7ef134459fae338\\transformed\\core-1.8.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "112", "startColumns": "4", "startOffsets": "10371", "endColumns": "100", "endOffsets": "10467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\929536aae40a590fbb05df20b2f1b3b8\\transformed\\material-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,276,363,464,585,669,735,830,904,964,1048,1114,1172,1245,1308,1364,1483,1540,1601,1657,1731,1876,1962,2046,2149,2231,2314,2404,2471,2537,2610,2688,2776,2847,2924,2998,3070,3161,3235,3330,3428,3502,3582,3683,3736,3802,3891,3981,4043,4107,4170,4282,4395,4505,4617", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,86,100,120,83,65,94,73,59,83,65,57,72,62,55,118,56,60,55,73,144,85,83,102,81,82,89,66,65,72,77,87,70,76,73,71,90,73,94,97,73,79,100,52,65,88,89,61,63,62,111,112,109,111,78", "endOffsets": "271,358,459,580,664,730,825,899,959,1043,1109,1167,1240,1303,1359,1478,1535,1596,1652,1726,1871,1957,2041,2144,2226,2309,2399,2466,2532,2605,2683,2771,2842,2919,2993,3065,3156,3230,3325,3423,3497,3577,3678,3731,3797,3886,3976,4038,4102,4165,4277,4390,4500,4612,4691"}, "to": {"startLines": "2,34,35,36,37,57,58,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3072,3159,3260,3381,5832,5898,6421,6495,6555,6639,6705,6763,6836,6899,6955,7074,7131,7192,7248,7322,7467,7553,7637,7740,7822,7905,7995,8062,8128,8201,8279,8367,8438,8515,8589,8661,8752,8826,8921,9019,9093,9173,9274,9327,9393,9482,9572,9634,9698,9761,9873,9986,10096,10208", "endLines": "6,34,35,36,37,57,58,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "endColumns": "12,86,100,120,83,65,94,73,59,83,65,57,72,62,55,118,56,60,55,73,144,85,83,102,81,82,89,66,65,72,77,87,70,76,73,71,90,73,94,97,73,79,100,52,65,88,89,61,63,62,111,112,109,111,78", "endOffsets": "321,3154,3255,3376,3460,5893,5988,6490,6550,6634,6700,6758,6831,6894,6950,7069,7126,7187,7243,7317,7462,7548,7632,7735,7817,7900,7990,8057,8123,8196,8274,8362,8433,8510,8584,8656,8747,8821,8916,9014,9088,9168,9269,9322,9388,9477,9567,9629,9693,9756,9868,9981,10091,10203,10282"}}]}]}