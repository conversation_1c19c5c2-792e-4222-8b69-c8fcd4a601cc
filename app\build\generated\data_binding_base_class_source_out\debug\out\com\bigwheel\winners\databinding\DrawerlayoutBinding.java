// Generated by view binder compiler. Do not edit!
package com.bigwheel.winners.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bigwheel.winners.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DrawerlayoutBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView close;

  @NonNull
  public final LinearLayout logout;

  @NonNull
  public final LinearLayout privacy;

  @NonNull
  public final LinearLayout terms;

  @NonNull
  public final TextView userid;

  @NonNull
  public final TextView useridname;

  private DrawerlayoutBinding(@NonNull LinearLayout rootView, @NonNull ImageView close,
      @NonNull LinearLayout logout, @NonNull LinearLayout privacy, @NonNull LinearLayout terms,
      @NonNull TextView userid, @NonNull TextView useridname) {
    this.rootView = rootView;
    this.close = close;
    this.logout = logout;
    this.privacy = privacy;
    this.terms = terms;
    this.userid = userid;
    this.useridname = useridname;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DrawerlayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DrawerlayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.drawerlayout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DrawerlayoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.close;
      ImageView close = ViewBindings.findChildViewById(rootView, id);
      if (close == null) {
        break missingId;
      }

      id = R.id.logout;
      LinearLayout logout = ViewBindings.findChildViewById(rootView, id);
      if (logout == null) {
        break missingId;
      }

      id = R.id.privacy;
      LinearLayout privacy = ViewBindings.findChildViewById(rootView, id);
      if (privacy == null) {
        break missingId;
      }

      id = R.id.terms;
      LinearLayout terms = ViewBindings.findChildViewById(rootView, id);
      if (terms == null) {
        break missingId;
      }

      id = R.id.userid;
      TextView userid = ViewBindings.findChildViewById(rootView, id);
      if (userid == null) {
        break missingId;
      }

      id = R.id.useridname;
      TextView useridname = ViewBindings.findChildViewById(rootView, id);
      if (useridname == null) {
        break missingId;
      }

      return new DrawerlayoutBinding((LinearLayout) rootView, close, logout, privacy, terms, userid,
          useridname);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
