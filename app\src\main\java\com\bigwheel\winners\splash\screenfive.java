package com.bigwheel.winners.splash;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;

import androidx.appcompat.app.AppCompatActivity;

import com.bigwheel.winners.R;



public class screenfive extends AppCompatActivity {

    Button btnagree;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);


        setContentView(R.layout.activity_screenfive);
        getSupportActionBar().hide();
        btnagree = findViewById(R.id.agreebtn);
        btnagree.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                Intent i=new Intent(privcypol.this,malefemale.class);
//                startActivity(i);

                            Intent i=new Intent(screenfive.this, screenfour.class);
                            startActivity(i);
                            finish();
                }


        });

    }


}