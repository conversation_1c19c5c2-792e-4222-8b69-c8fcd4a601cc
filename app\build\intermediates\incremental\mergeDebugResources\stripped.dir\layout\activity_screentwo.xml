<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:sweet="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/themeBgColor">

    <ImageView
        android:layout_width="400dp"
        android:layout_height="400dp"
        android:layout_marginTop="50dp"
        android:src="@drawable/spinbg3"
        sweet:layout_constraintBottom_toTopOf="@+id/textView6"
        sweet:layout_constraintEnd_toEndOf="parent"
        sweet:layout_constraintStart_toStartOf="parent"
        sweet:layout_constraintTop_toTopOf="parent" />

    <CheckBox
        android:id="@+id/checkbox_terms"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="50dp"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="50dp"
        android:visibility="invisible"
        sweet:layout_constraintBottom_toBottomOf="parent"
        sweet:layout_constraintStart_toStartOf="parent"
        sweet:layout_constraintTop_toBottomOf="@+id/iv_googlelogin" />

    <TextView
        android:id="@+id/textView12"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:text="I have read the"
        android:textColor="@color/white"
        android:textSize="16dp"
        android:visibility="invisible"
        sweet:layout_constraintBottom_toBottomOf="@+id/checkbox_terms"
        sweet:layout_constraintStart_toEndOf="@+id/checkbox_terms"
        sweet:layout_constraintTop_toTopOf="@+id/checkbox_terms" />

    <TextView
        android:id="@+id/tv_privacy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="  Privacy Policy"
        android:textColor="#FFBF00"
        android:visibility="invisible"
        android:textSize="16dp"
        sweet:layout_constraintBottom_toBottomOf="@+id/textView12"
        sweet:layout_constraintStart_toEndOf="@+id/textView12"
        sweet:layout_constraintTop_toTopOf="@+id/textView12" />

<!--    <TextView-->
<!--        android:id="@+id/textView13"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:text="@string/and_i_agree_with_its"-->
<!--        android:textColor="@color/white"-->
<!--        android:textSize="16dp"-->
<!--        sweet:layout_constraintStart_toEndOf="@+id/checkbox_terms"-->
<!--        sweet:layout_constraintTop_toBottomOf="@+id/textView12" />-->

<!--    <TextView-->
<!--        android:id="@+id/tv_terms"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:text=" Terms And Condi."-->
<!--        android:textColor="#FFBF00"-->
<!--        android:textSize="16dp"-->
<!--        sweet:layout_constraintBottom_toBottomOf="@+id/textView13"-->
<!--        sweet:layout_constraintStart_toEndOf="@+id/textView13"-->
<!--        sweet:layout_constraintTop_toTopOf="@+id/textView13" />-->

    <LinearLayout
        android:id="@+id/iv_googlelogin"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:layout_marginStart="50dp"
        android:layout_marginEnd="50dp"
        android:layout_marginBottom="100dp"
        android:background="@drawable/ic_rounded_theme_blue"
        android:gravity="center"
        android:orientation="horizontal"
        sweet:layout_constraintBottom_toBottomOf="parent"
        sweet:layout_constraintEnd_toEndOf="parent"
        sweet:layout_constraintStart_toStartOf="parent">

<!--        <ImageView-->
<!--            android:layout_width="30dp"-->
<!--            android:layout_height="30dp"-->
<!--            android:background="@drawable/search"/>-->
        <TextView
            android:layout_width="wrap_content"
            android:text="START"
            android:textSize="30sp"
            android:textStyle="bold"
            android:textColor="@color/light_white"
            android:layout_marginStart="10dp"
            android:fontFamily="@font/font"
            android:layout_height="wrap_content"/>

    </LinearLayout>

    <!--    <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"

            android:scaleType="fitXY"
            android:src="@drawable/login_google"
         />-->

    <TextView
        android:id="@+id/textView3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="36dp"
        android:fontFamily="@font/font"
        android:text="Login"
        android:textColor="@color/themeBgColor"
        android:textSize="20dp"
        android:textStyle="bold"
        android:visibility="gone"
        sweet:layout_constraintEnd_toEndOf="parent"
        sweet:layout_constraintStart_toStartOf="parent"
        sweet:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout2"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="80dp"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="80dp"
        android:background="@drawable/ic_rounded_theme_white"
        android:visibility="gone"
        sweet:layout_constraintBottom_toBottomOf="parent"
        sweet:layout_constraintEnd_toEndOf="parent"
        sweet:layout_constraintStart_toStartOf="parent"
        sweet:layout_constraintTop_toBottomOf="@+id/textView3">

        <TextView
            android:id="@+id/textView4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="25dp"
            android:layout_marginTop="21dp"
            android:fontFamily="@font/font"
            android:text="Enter your Number"
            android:textColor="@color/black"
            android:textSize="14dp"
            sweet:layout_constraintStart_toStartOf="parent"
            sweet:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/etNumber"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginLeft="25dp"
            android:layout_marginTop="4dp"
            android:layout_marginRight="24dp"
            android:background="@drawable/ic_rounded_theme_white_black"
            android:hint="Enter your Number"
            android:inputType="number"
            android:padding="5dp"
            android:textColor="@color/black"
            sweet:layout_constraintEnd_toEndOf="parent"
            sweet:layout_constraintStart_toStartOf="parent"
            sweet:layout_constraintTop_toBottomOf="@+id/textView4" />

        <TextView
            android:id="@+id/textView5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="25dp"
            android:layout_marginTop="16dp"
            android:fontFamily="@font/font"
            android:text="Password"
            android:textColor="@color/black"
            android:textSize="14dp"
            sweet:layout_constraintStart_toStartOf="parent"
            sweet:layout_constraintTop_toBottomOf="@+id/etNumber" />

        <EditText
            android:id="@+id/etPassword"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginLeft="25dp"
            android:layout_marginTop="4dp"
            android:layout_marginRight="24dp"
            android:background="@drawable/ic_rounded_theme_white_black"
            android:hint="Enter your password"
            android:inputType="textPassword"
            android:padding="5dp"
            android:textColor="@color/black"
            sweet:layout_constraintEnd_toEndOf="parent"
            sweet:layout_constraintStart_toStartOf="parent"
            sweet:layout_constraintTop_toBottomOf="@+id/textView5" />

        <TextView
            android:id="@+id/tvForgotPassword"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:fontFamily="@font/font"
            android:inputType="textPassword"
            android:text="Forgot password?"
            android:textColor="@color/gray"
            android:textSize="14dp"
            android:visibility="gone"
            sweet:layout_constraintEnd_toEndOf="@+id/etPassword"
            sweet:layout_constraintTop_toBottomOf="@+id/etPassword" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btnSignIn"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginLeft="44dp"
            android:layout_marginTop="24dp"
            android:layout_marginRight="44dp"
            android:background="@drawable/ic_rounded_theme_yellow"
            sweet:layout_constraintEnd_toEndOf="parent"
            sweet:layout_constraintStart_toStartOf="parent"
            sweet:layout_constraintTop_toBottomOf="@+id/etPassword">

            <TextView
                android:id="@+id/textView2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font"
                android:text="SIGN IN"
                android:textColor="@color/themeBgColor"
                android:textSize="20dp"
                android:textStyle="bold"
                sweet:layout_constraintBottom_toBottomOf="parent"
                sweet:layout_constraintEnd_toEndOf="parent"
                sweet:layout_constraintStart_toStartOf="parent"
                sweet:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_or"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="OR"
            sweet:layout_constraintEnd_toEndOf="parent"
            sweet:layout_constraintStart_toStartOf="parent"
            sweet:layout_constraintTop_toBottomOf="@+id/btnSignIn" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/hr"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginLeft="25dp"
        android:layout_marginRight="24dp"
        android:background="@color/gray"
        android:visibility="gone"
        sweet:layout_constraintBottom_toTopOf="@+id/iv_googlelogin"
        sweet:layout_constraintEnd_toEndOf="parent"
        sweet:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/textView6"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:fontFamily="@font/font"
        android:text="Don&apos;t have account?"
        android:textColor="#666666"
        android:textSize="14dp"
        android:visibility="gone"
        sweet:layout_constraintEnd_toEndOf="parent"
        sweet:layout_constraintStart_toStartOf="parent"
        sweet:layout_constraintTop_toBottomOf="@+id/hr" />

    <TextView
        android:id="@+id/btnSignUp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:fontFamily="@font/font"
        android:text="SIGN UP"
        android:textColor="#ffb800"
        android:textSize="16dp"
        android:visibility="gone"
        sweet:layout_constraintEnd_toEndOf="parent"
        sweet:layout_constraintStart_toStartOf="parent"
        sweet:layout_constraintTop_toBottomOf="@+id/textView6" />
</androidx.constraintlayout.widget.ConstraintLayout>
    <!--<?xml version="1.0" encoding="utf-8"?>-->
    <!--<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"-->
    <!--    xmlns:app="http://schemas.android.com/apk/res-auto"-->
    <!--    xmlns:tools="http://schemas.android.com/tools"-->
    <!--    android:layout_width="match_parent"-->
    <!--    android:layout_height="match_parent"-->
    <!--    android:orientation="vertical"-->

    <!--    android:gravity="center"-->
    <!--    tools:context="com.druk.developers.spin.to.spinandwin.splash.screentwo">-->


    <!--        <TextView-->
    <!--            android:id="@+id/textView2"-->
    <!--            android:layout_width="match_parent"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:gravity="center"-->
    <!--            android:text="Select Language"-->
    <!--            android:layout_weight="1"-->
    <!--            android:textSize="40sp" />-->




    <!--        <RadioButton-->
    <!--            android:id="@+id/radioButton11"-->
    <!--            android:layout_width="match_parent"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:textSize="30dp"-->
    <!--            android:layout_weight="1"-->
    <!--            android:text="English" />-->

    <!--        <RadioButton-->
    <!--            android:id="@+id/radioButton12"-->
    <!--            android:layout_width="match_parent"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:minHeight="48dp"-->
    <!--            android:textSize="30dp"-->
    <!--            android:layout_weight="1"-->
    <!--            android:text="Hindi" />-->
    <!--        <RadioButton-->
    <!--            android:id="@+id/radioButton14"-->
    <!--            android:layout_width="match_parent"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:minHeight="48dp"-->
    <!--            android:textSize="30dp"-->
    <!--            android:layout_weight="1"-->
    <!--            android:text="marathi" />-->
    <!--        <RadioButton-->
    <!--            android:id="@+id/radioButton13"-->
    <!--            android:layout_width="match_parent"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:minHeight="48dp"-->
    <!--            android:textSize="30dp"-->
    <!--            android:layout_weight="1"-->
    <!--            android:text="Panjabi" />-->

    <!--        <LinearLayout-->
    <!--            android:layout_width="match_parent"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:layout_weight="1"-->
    <!--            android:gravity="bottom"-->
    <!--            android:orientation="vertical">-->

    <!--            <Button-->
    <!--                android:id="@+id/nextbtn"-->
    <!--                android:layout_width="match_parent"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:background="#000000"-->
    <!--                android:text="Next"-->
    <!--                android:textColor="#FFFFFF"-->
    <!--                android:textSize="24sp" />-->
    <!--        </LinearLayout>-->

    <!--    </LinearLayout>-->


