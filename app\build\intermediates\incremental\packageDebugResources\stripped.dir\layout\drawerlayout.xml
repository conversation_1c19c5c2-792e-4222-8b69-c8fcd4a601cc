<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">


    <LinearLayout

        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="20dp"
        android:orientation="vertical">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">


            <ImageView
                android:id="@+id/close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:src="@drawable/ic_baseline_backspace_24"></ImageView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="100dp"
                    android:layout_height="100dp"
                    android:src="@drawable/icgrop"></ImageView>

                <TextView
                    android:id="@+id/useridname"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:gravity="center"
                    android:text=""
                    android:textColor="@color/black"
                    android:textSize="15dp"
                    android:textStyle="bold"></TextView>

                <TextView
                    android:id="@+id/userid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:gravity="center"
                    android:text=""
                    android:textColor="@color/black"
                    android:textSize="12dp"
                    android:textStyle="bold"></TextView>
            </LinearLayout>
        </RelativeLayout>


        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="1dp"
            android:background="@color/black"></View>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/privacy"
                android:layout_width="match_parent"
                android:layout_height="50dp">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="5"
                    android:padding="10dp"
                    android:src="@drawable/ic_baseline_privacy_tip_24"
                    app:tint="@color/black"></ImageView>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="20dp"
                    android:layout_weight="1"
                    android:gravity="start|center_vertical"
                    android:text="Privacy Policy"
                    android:textColor="@color/black"
                    android:textSize="15dp"
                    android:textStyle="bold"></TextView>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="10dp"
                android:background="@color/black"></View>

            <LinearLayout
                android:id="@+id/terms"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="10dp">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="5"
                    android:padding="10dp"
                    android:src="@drawable/terms"
                    app:tint="@color/black"></ImageView>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="20dp"
                    android:layout_weight="1"
                    android:gravity="start|center_vertical"
                    android:text="Terms And Condition"
                    android:textColor="@color/black"
                    android:textSize="15dp"
                    android:textStyle="bold"></TextView>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="10dp"
                android:background="@color/black"></View>

            <LinearLayout
                android:id="@+id/logout"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="10dp"
                android:visibility="visible">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="5"
                    android:padding="10dp"
                    android:src="@drawable/logout"
                    app:tint="@color/themeBgColor"></ImageView>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="20dp"
                    android:layout_weight="1"
                    android:gravity="start|center_vertical"
                    android:text="Logout"
                    android:textColor="@color/black"
                    android:textSize="15dp"
                    android:textStyle="bold"></TextView>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="10dp"
                android:background="@color/black"></View>
        </LinearLayout>

    </LinearLayout>


</LinearLayout>