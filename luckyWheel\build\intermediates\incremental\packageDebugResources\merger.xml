<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\android project\new big 22-06\luckyWheel\src\main\res"/><source path="D:\android project\new big 22-06\luckyWheel\build\generated\res\rs\debug"/><source path="D:\android project\new big 22-06\luckyWheel\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\android project\new big 22-06\luckyWheel\src\main\res"><file name="font" path="D:\android project\new big 22-06\luckyWheel\src\main\res\font\font.ttf" qualifiers="" type="font"/><file name="lucky_wheel_layout" path="D:\android project\new big 22-06\luckyWheel\src\main\res\layout\lucky_wheel_layout.xml" qualifiers="" type="layout"/><file name="audio" path="D:\android project\new big 22-06\luckyWheel\src\main\res\raw\audio.mp3" qualifiers="" type="raw"/><file name="fail" path="D:\android project\new big 22-06\luckyWheel\src\main\res\raw\fail.mp3" qualifiers="" type="raw"/><file name="lossanim" path="D:\android project\new big 22-06\luckyWheel\src\main\res\raw\lossanim.json" qualifiers="" type="raw"/><file name="wheelsong" path="D:\android project\new big 22-06\luckyWheel\src\main\res\raw\wheelsong.mp3" qualifiers="" type="raw"/><file name="winner" path="D:\android project\new big 22-06\luckyWheel\src\main\res\raw\winner.json" qualifiers="" type="raw"/><file name="winningsong" path="D:\android project\new big 22-06\luckyWheel\src\main\res\raw\winningsong.mp3" qualifiers="" type="raw"/><file path="D:\android project\new big 22-06\luckyWheel\src\main\res\values\attrs.xml" qualifiers=""><declare-styleable name="MyTextView">
        <attr format="reference" name="font"/>
    </declare-styleable><declare-styleable name="LuckyWheelView">
        <attr format="color" name="lkwBackgroundColor"/>
        <attr format="reference" name="lkwCursor"/>
        <attr format="reference" name="lkwCenterImage"/>
        <attr format="color" name="lkwTextColor"/>
    </declare-styleable></file><file path="D:\android project\new big 22-06\luckyWheel\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">library</string><string name="winlist">SpinWin : Cash Winner- 73******40 won 500 Rs., 97******93 won 1000 Rs., 63******74 won 450 Rs., 88******66 won 4500 Rs., 98******55 won 1200 Rs., 93******65 won 750 Rs., 99******74 won 500 Rs., 97******03 won 1850 Rs., 90******42 won 500 Rs. ,81******81 won 1500 Rs., 99******99 won 350 Rs., 99******91 won 7100 Rs., 98******54 won 4500 Rs., 86******93 won 3300 Rs., 81******76 won 4200 Rs., 99******27 won 1750 Rs.</string></file></source><source path="D:\android project\new big 22-06\luckyWheel\build\generated\res\rs\debug"/><source path="D:\android project\new big 22-06\luckyWheel\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\android project\new big 22-06\luckyWheel\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\android project\new big 22-06\luckyWheel\src\debug\res"/></dataSet><mergedItems><configuration qualifiers=""><declare-styleable name="MyTextView">
        <attr format="reference" name="font"/>
    </declare-styleable><declare-styleable name="LuckyWheelView">
        <attr format="color" name="lkwBackgroundColor"/>
        <attr format="reference" name="lkwCursor"/>
        <attr format="reference" name="lkwCenterImage"/>
        <attr format="color" name="lkwTextColor"/>
    </declare-styleable></configuration></mergedItems></merger>