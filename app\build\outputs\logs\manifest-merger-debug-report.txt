-- Merging decision tree log ---
manifest
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:2:1-66:12
INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:2:1-66:12
INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:2:1-66:12
INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:2:1-66:12
MERGED from [androidx.databinding:viewbinding:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4c54ec94efd31340983af7810faaf6b9\transformed\jetified-viewbinding-7.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\929536aae40a590fbb05df20b2f1b3b8\transformed\material-1.6.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\5147043c113c0668ad2550623471df2e\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:2:1-136:12
MERGED from [:luckyWheel] D:\android project\new big 22-06\luckyWheel\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [com.airbnb.android:lottie:5.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d99fa6fc5c2ce3349d53a4cabf584c28\transformed\jetified-lottie-5.2.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2ab4175d456e4ffc79f88d3a7d93231\transformed\jetified-appcompat-resources-1.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a6f99c51ca9894dfbe548454cc2e836\transformed\appcompat-1.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.f0ris.sweetalert:library:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fb1b3646413a2cf28059af49a553607\transformed\jetified-library-1.6.2\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-analytics:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b37944038bd900f8bcbb28f0c4c05bc8\transformed\jetified-firebase-analytics-18.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.android.volley:volley:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6ce1ad9ad25adf23e5c050a6f97a7b2b\transformed\jetified-volley-1.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [de.hdodenhof:circleimageview:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b75501fb1d0c4b019bde18a5b04aa904\transformed\jetified-circleimageview-3.1.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\transforms-3\214cedbbf2c701b1abf04d9c644dc4b3\transformed\jetified-picasso-2.71828\AndroidManifest.xml:2:1-14:12
MERGED from [com.intuit.sdp:sdp-android:1.0.6] C:\Users\<USER>\.gradle\caches\transforms-3\953fda5b5e6a547d52b32a6f5fe96aa2\transformed\jetified-sdp-android-1.0.6\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e5a2ddbee3837c844c14e112ce9d7de6\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8ac9638a2ae133db8af40f54ce06b59\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-appcheck-interop:16.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a392519f6b7d19f8188cabc62c3e973\transformed\jetified-firebase-appcheck-interop-16.1.0\AndroidManifest.xml:15:1-25:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e291e32d4fff44a7c4cf5ce3fac5f5f0\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\86b77929db4db14877397e95875d55d5\transformed\jetified-play-services-auth-api-phone-18.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\6cae5637cd3bd31d41ff024c455d5325\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\19a37a520fad68d8857210cc8ccfd545\transformed\jetified-play-services-fido-19.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:17:1-45:12
MERGED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:17:1-47:12
MERGED from [com.google.firebase:firebase-iid-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f492ddeadd6a6909d8dc7d6e5c4eb226\transformed\jetified-firebase-iid-interop-17.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\02436aca31e270f81bb7f76ee8fb72ca\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\6714ca44f01d6a50f73a9f3e703be69b\transformed\jetified-firebase-auth-interop-19.0.2\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:17:1-36:12
MERGED from [com.google.firebase:firebase-datatransport:17.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\b723391e8f48268e968bf5f15f4f7103\transformed\jetified-firebase-datatransport-17.0.10\AndroidManifest.xml:15:1-34:12
MERGED from [com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\75afd29f29a59c314933f9e5db0ea997\transformed\jetified-firebase-installations-16.3.5\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\08aac7575ef243b3fc7ca9c510cb5c17\transformed\jetified-play-services-cloud-messaging-16.0.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-installations-interop:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\12d7dc387263b5244ff3ce0d6d5b0648\transformed\jetified-firebase-installations-interop-16.0.1\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8668edf1716a3a9bcccd72c714d8325a\transformed\jetified-play-services-tasks-18.0.1\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\fc44dab38777a97d2037e2c9ba6408f4\transformed\jetified-play-services-measurement-sdk-18.0.2\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\2f89d754fb566752c59cfba9dfd7f2ec\transformed\jetified-play-services-measurement-impl-18.0.2\AndroidManifest.xml:17:1-31:12
MERGED from [com.google.android.gms:play-services-stats:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\14963a8fba9047c77e501ee044f358da\transformed\jetified-play-services-stats-17.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\67f382bd6b693e573ddb352eb6a9bde9\transformed\jetified-firebase-measurement-connector-18.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-identifier:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\428a7b1a69dafc768d6a380dd9ca7df3\transformed\jetified-play-services-ads-identifier-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\e8ecc43dd0fce97b98a197ed3e32c8d8\transformed\jetified-play-services-measurement-sdk-api-18.0.2\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-base:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f7b01ad24d6769c58e5041d61cf8b060\transformed\jetified-play-services-measurement-base-18.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b84e8a3931c74ff232fa18e9c8d1ffe2\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\308df5cdc024a6b1206654141eba57c8\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d203087179ed6a67dd729f4795197f6\transformed\jetified-activity-1.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\488d6398224c3ab18357f7799713f5ab\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\255a5a0442ea3b2b78db700e5b47d9da\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9bad1171e03a0465abee31c3179e2ba1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\862bfa0a28facbf17be83419d64fb060\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8876b2c73235aa95c4e9181d11090031\transformed\lifecycle-viewmodel-2.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\725b481f89ee834bc24ed2dfabdd23f5\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a8c22f096461693e6ae8054bbb72eee\transformed\jetified-core-ktx-1.8.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4949a26617515f4e13d49486b8420091\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\902837bf16defd030995d9420fe56c7b\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd0b17eda0d81766a4724acd4371798\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\70cba9ad17b546090599a7481a34607d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e508eef870c6df94f7ad0da067926f0\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b812a6fb1adb052c162a1920bebf268d\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0707615e732a631138a72455bc5fdbea\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\43f95e506bc60e5f9b07f68863d9cd8e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f2636cb968702bde5e4e1d445b2113c3\transformed\browser-1.3.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd222a929cc3d119db688c356b8656f4\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d46143ae13607209cfd51feac634454a\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f26db5d6f6beb7dc1d8143d80357f802\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\58dead8d3025c9c586cc352813553a4b\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8d0afd0062e847437455444f8362355\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\acac161c2b87e63842fee74a7d62e9f6\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9af8709de6e7f49062ed925e5e50aad5\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\f556f30d56993798d7ef134459fae338\transformed\core-1.8.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d25aa9dc3f15350bba7fdd7f0efd0ff2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\49e790a591ac03f2dc2a4323894016ce\transformed\jetified-firebase-components-17.0.1\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.exifinterface:exifinterface:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57c631f221c3e882bd47d49d6364dd7b\transformed\exifinterface-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ba820f391a6b9833f8808c735357937\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.lifecycle:lifecycle-service:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\02b3c81f236852be93b2472c9c7af12f\transformed\jetified-lifecycle-service-2.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c54b24c209e46266328113061ecc19d\transformed\lifecycle-runtime-2.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4cca540091b4e5634cd4d6cffce3dd11\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a866d754e8b1221e040f28cf2983cda9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fa9990e1a2e1e9a95d4b89a907240d32\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\cec34019803d9d4ee1a47b1e1d8d3133\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ec1f28c67b546dd7d81033523999b6a\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\4dc13d69c529875d6c5460d4ad7e3591\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\330e35c7c7c21598d9c294e56d4a5133\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\085ef88353fecf708a2702966b000420\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\549f7788fb46e506d329b4d848970de3\transformed\lifecycle-livedata-2.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7e35ee872a3f516211945d51d583fc39\transformed\lifecycle-livedata-core-2.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2503080af49a6f3c4a815a9d57efdde7\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32e81d82b073a2b30ba04cb4c3b9349c\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\604ae337d202480dfbb77828d63e11db\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b91b32fd99f106e3f3246dd156a6f299\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\9868f7cd2371fe862b4e61b4a7ed7b06\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:15:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1456e67674153c9760724e66b6c1fcec\transformed\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.pnikosis:materialish-progress:1.7] C:\Users\<USER>\.gradle\caches\transforms-3\f76f8f3ca8dd38fcc778698b2a71c8de\transformed\jetified-materialish-progress-1.7\AndroidManifest.xml:2:1-13:12
INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:2:1-66:12
INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:2:1-66:12
INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:2:1-66:12
	package
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:3:5-35
		INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml
		INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:2:1-66:12
		INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:2:1-66:12
		INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:5:5-79
MERGED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:25:5-79
MERGED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:25:5-79
MERGED from [com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\75afd29f29a59c314933f9e5db0ea997\transformed\jetified-firebase-installations-16.3.5\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\75afd29f29a59c314933f9e5db0ea997\transformed\jetified-firebase-installations-16.3.5\AndroidManifest.xml:10:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\08aac7575ef243b3fc7ca9c510cb5c17\transformed\jetified-play-services-cloud-messaging-16.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\08aac7575ef243b3fc7ca9c510cb5c17\transformed\jetified-play-services-cloud-messaging-16.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\2f89d754fb566752c59cfba9dfd7f2ec\transformed\jetified-play-services-measurement-impl-18.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\2f89d754fb566752c59cfba9dfd7f2ec\transformed\jetified-play-services-measurement-impl-18.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\e8ecc43dd0fce97b98a197ed3e32c8d8\transformed\jetified-play-services-measurement-sdk-api-18.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\e8ecc43dd0fce97b98a197ed3e32c8d8\transformed\jetified-play-services-measurement-sdk-api-18.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:22:5-79
	android:name
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:5:22-76
uses-permission#android.permission.INTERNET
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:6:5-66
MERGED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:17:5-67
MERGED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:17:5-67
MERGED from [com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\75afd29f29a59c314933f9e5db0ea997\transformed\jetified-firebase-installations-16.3.5\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\75afd29f29a59c314933f9e5db0ea997\transformed\jetified-firebase-installations-16.3.5\AndroidManifest.xml:11:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\08aac7575ef243b3fc7ca9c510cb5c17\transformed\jetified-play-services-cloud-messaging-16.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\08aac7575ef243b3fc7ca9c510cb5c17\transformed\jetified-play-services-cloud-messaging-16.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\2f89d754fb566752c59cfba9dfd7f2ec\transformed\jetified-play-services-measurement-impl-18.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\2f89d754fb566752c59cfba9dfd7f2ec\transformed\jetified-play-services-measurement-impl-18.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\e8ecc43dd0fce97b98a197ed3e32c8d8\transformed\jetified-play-services-measurement-sdk-api-18.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\e8ecc43dd0fce97b98a197ed3e32c8d8\transformed\jetified-play-services-measurement-sdk-api-18.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
	android:name
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:6:22-64
application
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:8:5-64:19
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\929536aae40a590fbb05df20b2f1b3b8\transformed\material-1.6.1\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\929536aae40a590fbb05df20b2f1b3b8\transformed\material-1.6.1\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\5147043c113c0668ad2550623471df2e\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\5147043c113c0668ad2550623471df2e\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:49:5-134:19
MERGED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:49:5-134:19
MERGED from [:luckyWheel] D:\android project\new big 22-06\luckyWheel\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-13:19
MERGED from [:luckyWheel] D:\android project\new big 22-06\luckyWheel\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-13:19
MERGED from [com.airbnb.android:lottie:5.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d99fa6fc5c2ce3349d53a4cabf584c28\transformed\jetified-lottie-5.2.0\AndroidManifest.xml:9:5-20
MERGED from [com.airbnb.android:lottie:5.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d99fa6fc5c2ce3349d53a4cabf584c28\transformed\jetified-lottie-5.2.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.f0ris.sweetalert:library:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fb1b3646413a2cf28059af49a553607\transformed\jetified-library-1.6.2\AndroidManifest.xml:11:5-20
MERGED from [com.github.f0ris.sweetalert:library:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fb1b3646413a2cf28059af49a553607\transformed\jetified-library-1.6.2\AndroidManifest.xml:11:5-20
MERGED from [com.google.firebase:firebase-analytics:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b37944038bd900f8bcbb28f0c4c05bc8\transformed\jetified-firebase-analytics-18.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b37944038bd900f8bcbb28f0c4c05bc8\transformed\jetified-firebase-analytics-18.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:22:5-38:19
MERGED from [de.hdodenhof:circleimageview:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b75501fb1d0c4b019bde18a5b04aa904\transformed\jetified-circleimageview-3.1.0\AndroidManifest.xml:9:5-20
MERGED from [de.hdodenhof:circleimageview:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b75501fb1d0c4b019bde18a5b04aa904\transformed\jetified-circleimageview-3.1.0\AndroidManifest.xml:9:5-20
MERGED from [com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\transforms-3\214cedbbf2c701b1abf04d9c644dc4b3\transformed\jetified-picasso-2.71828\AndroidManifest.xml:7:5-12:19
MERGED from [com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\transforms-3\214cedbbf2c701b1abf04d9c644dc4b3\transformed\jetified-picasso-2.71828\AndroidManifest.xml:7:5-12:19
MERGED from [com.google.firebase:firebase-appcheck-interop:16.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a392519f6b7d19f8188cabc62c3e973\transformed\jetified-firebase-appcheck-interop-16.1.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:16.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a392519f6b7d19f8188cabc62c3e973\transformed\jetified-firebase-appcheck-interop-16.1.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.android.gms:play-services-fido:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\19a37a520fad68d8857210cc8ccfd545\transformed\jetified-play-services-fido-19.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\19a37a520fad68d8857210cc8ccfd545\transformed\jetified-play-services-fido-19.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:22:5-43:19
MERGED from [com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:22:5-43:19
MERGED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:28:5-45:19
MERGED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:28:5-45:19
MERGED from [com.google.firebase:firebase-iid-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f492ddeadd6a6909d8dc7d6e5c4eb226\transformed\jetified-firebase-iid-interop-17.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f492ddeadd6a6909d8dc7d6e5c4eb226\transformed\jetified-firebase-iid-interop-17.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\02436aca31e270f81bb7f76ee8fb72ca\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\02436aca31e270f81bb7f76ee8fb72ca\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\6714ca44f01d6a50f73a9f3e703be69b\transformed\jetified-firebase-auth-interop-19.0.2\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\6714ca44f01d6a50f73a9f3e703be69b\transformed\jetified-firebase-auth-interop-19.0.2\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:26:5-34:19
MERGED from [com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:26:5-34:19
MERGED from [com.google.firebase:firebase-datatransport:17.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\b723391e8f48268e968bf5f15f4f7103\transformed\jetified-firebase-datatransport-17.0.10\AndroidManifest.xml:24:5-32:19
MERGED from [com.google.firebase:firebase-datatransport:17.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\b723391e8f48268e968bf5f15f4f7103\transformed\jetified-firebase-datatransport-17.0.10\AndroidManifest.xml:24:5-32:19
MERGED from [com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\75afd29f29a59c314933f9e5db0ea997\transformed\jetified-firebase-installations-16.3.5\AndroidManifest.xml:14:5-22:19
MERGED from [com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\75afd29f29a59c314933f9e5db0ea997\transformed\jetified-firebase-installations-16.3.5\AndroidManifest.xml:14:5-22:19
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:24:5-37:19
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:24:5-37:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8668edf1716a3a9bcccd72c714d8325a\transformed\jetified-play-services-tasks-18.0.1\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8668edf1716a3a9bcccd72c714d8325a\transformed\jetified-play-services-tasks-18.0.1\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\fc44dab38777a97d2037e2c9ba6408f4\transformed\jetified-play-services-measurement-sdk-18.0.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\fc44dab38777a97d2037e2c9ba6408f4\transformed\jetified-play-services-measurement-sdk-18.0.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\2f89d754fb566752c59cfba9dfd7f2ec\transformed\jetified-play-services-measurement-impl-18.0.2\AndroidManifest.xml:28:5-29:19
MERGED from [com.google.android.gms:play-services-measurement-impl:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\2f89d754fb566752c59cfba9dfd7f2ec\transformed\jetified-play-services-measurement-impl-18.0.2\AndroidManifest.xml:28:5-29:19
MERGED from [com.google.android.gms:play-services-stats:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\14963a8fba9047c77e501ee044f358da\transformed\jetified-play-services-stats-17.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\14963a8fba9047c77e501ee044f358da\transformed\jetified-play-services-stats-17.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\67f382bd6b693e573ddb352eb6a9bde9\transformed\jetified-firebase-measurement-connector-18.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\67f382bd6b693e573ddb352eb6a9bde9\transformed\jetified-firebase-measurement-connector-18.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-ads-identifier:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\428a7b1a69dafc768d6a380dd9ca7df3\transformed\jetified-play-services-ads-identifier-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\428a7b1a69dafc768d6a380dd9ca7df3\transformed\jetified-play-services-ads-identifier-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f7b01ad24d6769c58e5041d61cf8b060\transformed\jetified-play-services-measurement-base-18.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f7b01ad24d6769c58e5041d61cf8b060\transformed\jetified-play-services-measurement-base-18.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b84e8a3931c74ff232fa18e9c8d1ffe2\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b84e8a3931c74ff232fa18e9c8d1ffe2\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\f556f30d56993798d7ef134459fae338\transformed\core-1.8.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\f556f30d56993798d7ef134459fae338\transformed\core-1.8.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ba820f391a6b9833f8808c735357937\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ba820f391a6b9833f8808c735357937\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a866d754e8b1221e040f28cf2983cda9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a866d754e8b1221e040f28cf2983cda9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\cec34019803d9d4ee1a47b1e1d8d3133\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\cec34019803d9d4ee1a47b1e1d8d3133\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\4dc13d69c529875d6c5460d4ad7e3591\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\4dc13d69c529875d6c5460d4ad7e3591\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:25:5-39:19
MERGED from [com.pnikosis:materialish-progress:1.7] C:\Users\<USER>\.gradle\caches\transforms-3\f76f8f3ca8dd38fcc778698b2a71c8de\transformed\jetified-materialish-progress-1.7\AndroidManifest.xml:11:5-20
MERGED from [com.pnikosis:materialish-progress:1.7] C:\Users\<USER>\.gradle\caches\transforms-3\f76f8f3ca8dd38fcc778698b2a71c8de\transformed\jetified-materialish-progress-1.7\AndroidManifest.xml:11:5-20
	android:requestLegacyExternalStorage
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:13:9-52
	android:appComponentFactory
		ADDED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\f556f30d56993798d7ef134459fae338\transformed\core-1.8.0\AndroidManifest.xml:24:18-86
	android:supportsRtl
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:15:9-35
	android:label
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:12:9-41
	android:hardwareAccelerated
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:10:9-43
	android:roundIcon
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:14:9-43
	android:icon
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:11:9-38
	android:allowBackup
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:9:9-35
	android:theme
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:16:9-46
	android:usesCleartextTraffic
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:17:9-44
activity#com.bigwheel.winners.splash.screensix
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:18:9-20:40
	android:exported
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:20:13-37
	android:name
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:19:13-45
activity#com.bigwheel.winners.splash.screenfive
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:21:9-23:40
	android:exported
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:23:13-37
	android:name
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:22:13-46
activity#com.bigwheel.winners.splash.screenfour
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:24:9-26:40
	android:exported
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:26:13-37
	android:name
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:25:13-46
activity#com.bigwheel.winners.splash.screenthree
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:27:9-29:40
	android:exported
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:29:13-37
	android:name
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:28:13-47
activity#com.bigwheel.winners.splash.screentwo
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:30:9-32:40
	android:exported
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:32:13-37
	android:name
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:31:13-45
activity#com.bigwheel.winners.splash.screenOne
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:33:9-35:40
	android:exported
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:35:13-37
	android:name
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:34:13-45
activity#com.bigwheel.winners.splash.splash
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:36:9-44:20
	android:exported
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:38:13-36
	android:name
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:37:13-42
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:39:13-43:29
action#android.intent.action.MAIN
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:40:17-69
	android:name
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:40:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:42:17-77
	android:name
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:42:27-74
activity#com.bigwheel.winners.pay.PaymentTutorial
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:45:9-57
	android:name
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:45:19-54
activity#com.bigwheel.winners.pay.PaymentViaUPI
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:46:9-55
	android:name
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:46:19-52
activity#com.bigwheel.winners.pay.PaymentResultPayuMoney
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:47:9-64
	android:name
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:47:19-61
activity#com.bigwheel.winners.pay.PaymentMethods
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:48:9-56
	android:name
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:48:19-53
activity#com.bigwheel.winners.GoogleLogin
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:49:9-51:52
	android:screenOrientation
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:51:13-49
	android:name
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:50:13-40
activity#com.bigwheel.winners.MainActivity
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:52:9-54:52
	android:screenOrientation
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:54:13-49
	android:name
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:53:13-41
activity#com.bigwheel.winners.pay.PaymentResult
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:55:9-57:52
	android:screenOrientation
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:57:13-49
	android:name
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:56:13-46
activity#com.bigwheel.winners.pay.MainActivitys
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:58:9-60:52
	android:screenOrientation
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:60:13-49
	android:name
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:59:13-46
activity#com.bigwheel.winners.SplashScreen
ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:61:9-63:52
	android:screenOrientation
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:63:13-49
	android:name
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml:62:13-41
uses-sdk
INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml
INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4c54ec94efd31340983af7810faaf6b9\transformed\jetified-viewbinding-7.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.databinding:viewbinding:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4c54ec94efd31340983af7810faaf6b9\transformed\jetified-viewbinding-7.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\929536aae40a590fbb05df20b2f1b3b8\transformed\material-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\929536aae40a590fbb05df20b2f1b3b8\transformed\material-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\5147043c113c0668ad2550623471df2e\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\5147043c113c0668ad2550623471df2e\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:4:5-44
MERGED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:4:5-44
MERGED from [:luckyWheel] D:\android project\new big 22-06\luckyWheel\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:luckyWheel] D:\android project\new big 22-06\luckyWheel\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [com.airbnb.android:lottie:5.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d99fa6fc5c2ce3349d53a4cabf584c28\transformed\jetified-lottie-5.2.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.airbnb.android:lottie:5.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d99fa6fc5c2ce3349d53a4cabf584c28\transformed\jetified-lottie-5.2.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2ab4175d456e4ffc79f88d3a7d93231\transformed\jetified-appcompat-resources-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2ab4175d456e4ffc79f88d3a7d93231\transformed\jetified-appcompat-resources-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a6f99c51ca9894dfbe548454cc2e836\transformed\appcompat-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a6f99c51ca9894dfbe548454cc2e836\transformed\appcompat-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.f0ris.sweetalert:library:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fb1b3646413a2cf28059af49a553607\transformed\jetified-library-1.6.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.f0ris.sweetalert:library:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fb1b3646413a2cf28059af49a553607\transformed\jetified-library-1.6.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:firebase-analytics:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b37944038bd900f8bcbb28f0c4c05bc8\transformed\jetified-firebase-analytics-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b37944038bd900f8bcbb28f0c4c05bc8\transformed\jetified-firebase-analytics-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.volley:volley:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6ce1ad9ad25adf23e5c050a6f97a7b2b\transformed\jetified-volley-1.2.1\AndroidManifest.xml:5:5-43
MERGED from [com.android.volley:volley:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6ce1ad9ad25adf23e5c050a6f97a7b2b\transformed\jetified-volley-1.2.1\AndroidManifest.xml:5:5-43
MERGED from [de.hdodenhof:circleimageview:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b75501fb1d0c4b019bde18a5b04aa904\transformed\jetified-circleimageview-3.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [de.hdodenhof:circleimageview:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b75501fb1d0c4b019bde18a5b04aa904\transformed\jetified-circleimageview-3.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\transforms-3\214cedbbf2c701b1abf04d9c644dc4b3\transformed\jetified-picasso-2.71828\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\transforms-3\214cedbbf2c701b1abf04d9c644dc4b3\transformed\jetified-picasso-2.71828\AndroidManifest.xml:5:5-44
MERGED from [com.intuit.sdp:sdp-android:1.0.6] C:\Users\<USER>\.gradle\caches\transforms-3\953fda5b5e6a547d52b32a6f5fe96aa2\transformed\jetified-sdp-android-1.0.6\AndroidManifest.xml:7:5-9:41
MERGED from [com.intuit.sdp:sdp-android:1.0.6] C:\Users\<USER>\.gradle\caches\transforms-3\953fda5b5e6a547d52b32a6f5fe96aa2\transformed\jetified-sdp-android-1.0.6\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e5a2ddbee3837c844c14e112ce9d7de6\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e5a2ddbee3837c844c14e112ce9d7de6\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8ac9638a2ae133db8af40f54ce06b59\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8ac9638a2ae133db8af40f54ce06b59\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:16.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a392519f6b7d19f8188cabc62c3e973\transformed\jetified-firebase-appcheck-interop-16.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:16.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a392519f6b7d19f8188cabc62c3e973\transformed\jetified-firebase-appcheck-interop-16.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e291e32d4fff44a7c4cf5ce3fac5f5f0\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e291e32d4fff44a7c4cf5ce3fac5f5f0\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\86b77929db4db14877397e95875d55d5\transformed\jetified-play-services-auth-api-phone-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\86b77929db4db14877397e95875d55d5\transformed\jetified-play-services-auth-api-phone-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\6cae5637cd3bd31d41ff024c455d5325\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\6cae5637cd3bd31d41ff024c455d5325\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\19a37a520fad68d8857210cc8ccfd545\transformed\jetified-play-services-fido-19.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\19a37a520fad68d8857210cc8ccfd545\transformed\jetified-play-services-fido-19.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f492ddeadd6a6909d8dc7d6e5c4eb226\transformed\jetified-firebase-iid-interop-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f492ddeadd6a6909d8dc7d6e5c4eb226\transformed\jetified-firebase-iid-interop-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\02436aca31e270f81bb7f76ee8fb72ca\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\02436aca31e270f81bb7f76ee8fb72ca\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\6714ca44f01d6a50f73a9f3e703be69b\transformed\jetified-firebase-auth-interop-19.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\6714ca44f01d6a50f73a9f3e703be69b\transformed\jetified-firebase-auth-interop-19.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-datatransport:17.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\b723391e8f48268e968bf5f15f4f7103\transformed\jetified-firebase-datatransport-17.0.10\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-datatransport:17.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\b723391e8f48268e968bf5f15f4f7103\transformed\jetified-firebase-datatransport-17.0.10\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\75afd29f29a59c314933f9e5db0ea997\transformed\jetified-firebase-installations-16.3.5\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\75afd29f29a59c314933f9e5db0ea997\transformed\jetified-firebase-installations-16.3.5\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\08aac7575ef243b3fc7ca9c510cb5c17\transformed\jetified-play-services-cloud-messaging-16.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\08aac7575ef243b3fc7ca9c510cb5c17\transformed\jetified-play-services-cloud-messaging-16.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\12d7dc387263b5244ff3ce0d6d5b0648\transformed\jetified-firebase-installations-interop-16.0.1\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-installations-interop:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\12d7dc387263b5244ff3ce0d6d5b0648\transformed\jetified-firebase-installations-interop-16.0.1\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8668edf1716a3a9bcccd72c714d8325a\transformed\jetified-play-services-tasks-18.0.1\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8668edf1716a3a9bcccd72c714d8325a\transformed\jetified-play-services-tasks-18.0.1\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\fc44dab38777a97d2037e2c9ba6408f4\transformed\jetified-play-services-measurement-sdk-18.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\fc44dab38777a97d2037e2c9ba6408f4\transformed\jetified-play-services-measurement-sdk-18.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\2f89d754fb566752c59cfba9dfd7f2ec\transformed\jetified-play-services-measurement-impl-18.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\2f89d754fb566752c59cfba9dfd7f2ec\transformed\jetified-play-services-measurement-impl-18.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\14963a8fba9047c77e501ee044f358da\transformed\jetified-play-services-stats-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\14963a8fba9047c77e501ee044f358da\transformed\jetified-play-services-stats-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\67f382bd6b693e573ddb352eb6a9bde9\transformed\jetified-firebase-measurement-connector-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\67f382bd6b693e573ddb352eb6a9bde9\transformed\jetified-firebase-measurement-connector-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\428a7b1a69dafc768d6a380dd9ca7df3\transformed\jetified-play-services-ads-identifier-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\428a7b1a69dafc768d6a380dd9ca7df3\transformed\jetified-play-services-ads-identifier-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\e8ecc43dd0fce97b98a197ed3e32c8d8\transformed\jetified-play-services-measurement-sdk-api-18.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\e8ecc43dd0fce97b98a197ed3e32c8d8\transformed\jetified-play-services-measurement-sdk-api-18.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f7b01ad24d6769c58e5041d61cf8b060\transformed\jetified-play-services-measurement-base-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f7b01ad24d6769c58e5041d61cf8b060\transformed\jetified-play-services-measurement-base-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b84e8a3931c74ff232fa18e9c8d1ffe2\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b84e8a3931c74ff232fa18e9c8d1ffe2\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\308df5cdc024a6b1206654141eba57c8\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\308df5cdc024a6b1206654141eba57c8\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d203087179ed6a67dd729f4795197f6\transformed\jetified-activity-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d203087179ed6a67dd729f4795197f6\transformed\jetified-activity-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\488d6398224c3ab18357f7799713f5ab\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\488d6398224c3ab18357f7799713f5ab\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\255a5a0442ea3b2b78db700e5b47d9da\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\255a5a0442ea3b2b78db700e5b47d9da\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9bad1171e03a0465abee31c3179e2ba1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9bad1171e03a0465abee31c3179e2ba1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\862bfa0a28facbf17be83419d64fb060\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\862bfa0a28facbf17be83419d64fb060\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8876b2c73235aa95c4e9181d11090031\transformed\lifecycle-viewmodel-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8876b2c73235aa95c4e9181d11090031\transformed\lifecycle-viewmodel-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\725b481f89ee834bc24ed2dfabdd23f5\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\725b481f89ee834bc24ed2dfabdd23f5\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a8c22f096461693e6ae8054bbb72eee\transformed\jetified-core-ktx-1.8.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.core:core-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a8c22f096461693e6ae8054bbb72eee\transformed\jetified-core-ktx-1.8.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4949a26617515f4e13d49486b8420091\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4949a26617515f4e13d49486b8420091\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\902837bf16defd030995d9420fe56c7b\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\902837bf16defd030995d9420fe56c7b\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd0b17eda0d81766a4724acd4371798\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd0b17eda0d81766a4724acd4371798\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\70cba9ad17b546090599a7481a34607d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\70cba9ad17b546090599a7481a34607d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e508eef870c6df94f7ad0da067926f0\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e508eef870c6df94f7ad0da067926f0\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b812a6fb1adb052c162a1920bebf268d\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b812a6fb1adb052c162a1920bebf268d\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0707615e732a631138a72455bc5fdbea\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0707615e732a631138a72455bc5fdbea\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\43f95e506bc60e5f9b07f68863d9cd8e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\43f95e506bc60e5f9b07f68863d9cd8e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f2636cb968702bde5e4e1d445b2113c3\transformed\browser-1.3.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f2636cb968702bde5e4e1d445b2113c3\transformed\browser-1.3.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd222a929cc3d119db688c356b8656f4\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd222a929cc3d119db688c356b8656f4\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d46143ae13607209cfd51feac634454a\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d46143ae13607209cfd51feac634454a\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f26db5d6f6beb7dc1d8143d80357f802\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f26db5d6f6beb7dc1d8143d80357f802\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\58dead8d3025c9c586cc352813553a4b\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\58dead8d3025c9c586cc352813553a4b\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8d0afd0062e847437455444f8362355\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8d0afd0062e847437455444f8362355\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\acac161c2b87e63842fee74a7d62e9f6\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\acac161c2b87e63842fee74a7d62e9f6\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9af8709de6e7f49062ed925e5e50aad5\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9af8709de6e7f49062ed925e5e50aad5\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\f556f30d56993798d7ef134459fae338\transformed\core-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\f556f30d56993798d7ef134459fae338\transformed\core-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d25aa9dc3f15350bba7fdd7f0efd0ff2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d25aa9dc3f15350bba7fdd7f0efd0ff2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\49e790a591ac03f2dc2a4323894016ce\transformed\jetified-firebase-components-17.0.1\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-components:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\49e790a591ac03f2dc2a4323894016ce\transformed\jetified-firebase-components-17.0.1\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.exifinterface:exifinterface:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57c631f221c3e882bd47d49d6364dd7b\transformed\exifinterface-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57c631f221c3e882bd47d49d6364dd7b\transformed\exifinterface-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ba820f391a6b9833f8808c735357937\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ba820f391a6b9833f8808c735357937\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-service:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\02b3c81f236852be93b2472c9c7af12f\transformed\jetified-lifecycle-service-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\02b3c81f236852be93b2472c9c7af12f\transformed\jetified-lifecycle-service-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c54b24c209e46266328113061ecc19d\transformed\lifecycle-runtime-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c54b24c209e46266328113061ecc19d\transformed\lifecycle-runtime-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4cca540091b4e5634cd4d6cffce3dd11\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4cca540091b4e5634cd4d6cffce3dd11\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a866d754e8b1221e040f28cf2983cda9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a866d754e8b1221e040f28cf2983cda9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fa9990e1a2e1e9a95d4b89a907240d32\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fa9990e1a2e1e9a95d4b89a907240d32\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\cec34019803d9d4ee1a47b1e1d8d3133\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\cec34019803d9d4ee1a47b1e1d8d3133\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ec1f28c67b546dd7d81033523999b6a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ec1f28c67b546dd7d81033523999b6a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\4dc13d69c529875d6c5460d4ad7e3591\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\4dc13d69c529875d6c5460d4ad7e3591\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\330e35c7c7c21598d9c294e56d4a5133\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\330e35c7c7c21598d9c294e56d4a5133\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\085ef88353fecf708a2702966b000420\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\085ef88353fecf708a2702966b000420\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\549f7788fb46e506d329b4d848970de3\transformed\lifecycle-livedata-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\549f7788fb46e506d329b4d848970de3\transformed\lifecycle-livedata-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7e35ee872a3f516211945d51d583fc39\transformed\lifecycle-livedata-core-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7e35ee872a3f516211945d51d583fc39\transformed\lifecycle-livedata-core-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2503080af49a6f3c4a815a9d57efdde7\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2503080af49a6f3c4a815a9d57efdde7\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32e81d82b073a2b30ba04cb4c3b9349c\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32e81d82b073a2b30ba04cb4c3b9349c\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\604ae337d202480dfbb77828d63e11db\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\604ae337d202480dfbb77828d63e11db\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b91b32fd99f106e3f3246dd156a6f299\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b91b32fd99f106e3f3246dd156a6f299\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\9868f7cd2371fe862b4e61b4a7ed7b06\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\9868f7cd2371fe862b4e61b4a7ed7b06\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1456e67674153c9760724e66b6c1fcec\transformed\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1456e67674153c9760724e66b6c1fcec\transformed\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.pnikosis:materialish-progress:1.7] C:\Users\<USER>\.gradle\caches\transforms-3\f76f8f3ca8dd38fcc778698b2a71c8de\transformed\jetified-materialish-progress-1.7\AndroidManifest.xml:7:5-9:41
MERGED from [com.pnikosis:materialish-progress:1.7] C:\Users\<USER>\.gradle\caches\transforms-3\f76f8f3ca8dd38fcc778698b2a71c8de\transformed\jetified-materialish-progress-1.7\AndroidManifest.xml:7:5-9:41
INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml
INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml
		INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml
		ADDED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml
		INJECTED from D:\android project\new big 22-06\app\src\main\AndroidManifest.xml
permission#${applicationId}.permission.C2D_MESSAGE
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:7:5-9:47
	android:protectionLevel
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:9:9-44
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:8:9-63
permission#com.bigwheel.winners.permission.C2D_MESSAGE
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:7:5-9:47
	android:protectionLevel
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:9:9-44
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:8:9-63
uses-permission#${applicationId}.permission.C2D_MESSAGE
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:11:5-79
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:11:22-76
uses-permission#com.bigwheel.winners.permission.C2D_MESSAGE
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:11:5-79
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:11:22-76
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:16:5-77
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:16:22-74
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:18:5-82
MERGED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\08aac7575ef243b3fc7ca9c510cb5c17\transformed\jetified-play-services-cloud-messaging-16.0.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\08aac7575ef243b3fc7ca9c510cb5c17\transformed\jetified-play-services-cloud-messaging-16.0.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:18:22-79
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:19:5-68
MERGED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\08aac7575ef243b3fc7ca9c510cb5c17\transformed\jetified-play-services-cloud-messaging-16.0.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\08aac7575ef243b3fc7ca9c510cb5c17\transformed\jetified-play-services-cloud-messaging-16.0.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\2f89d754fb566752c59cfba9dfd7f2ec\transformed\jetified-play-services-measurement-impl-18.0.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\2f89d754fb566752c59cfba9dfd7f2ec\transformed\jetified-play-services-measurement-impl-18.0.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\e8ecc43dd0fce97b98a197ed3e32c8d8\transformed\jetified-play-services-measurement-sdk-api-18.0.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\e8ecc43dd0fce97b98a197ed3e32c8d8\transformed\jetified-play-services-measurement-sdk-api-18.0.2\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:19:22-65
uses-permission#android.permission.VIBRATE
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:24:5-66
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:24:22-63
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:30:5-81
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:5-81
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:5-81
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:30:22-78
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:32:5-86
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:32:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:33:5-87
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:33:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:34:5-81
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:34:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:35:5-83
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:35:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:36:5-88
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:36:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:37:5-92
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:37:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:38:5-84
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:38:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:39:5-83
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:39:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:40:5-91
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:40:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:41:5-92
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:41:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:42:5-93
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:42:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:43:5-73
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:43:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:44:5-82
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:44:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:45:5-83
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:45:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:46:5-88
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:46:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:47:5-89
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:47:22-86
receiver#com.onesignal.FCMBroadcastReceiver
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:50:9-61:20
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:52:13-36
	android:permission
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:53:13-73
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:51:13-62
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE+category:name:${applicationId}
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:56:13-60:29
	android:priority
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:56:28-50
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE+category:name:com.bigwheel.winners
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:56:13-60:29
	android:priority
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:56:28-50
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:57:17-81
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:57:25-78
category#${applicationId}
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:59:17-61
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:59:27-58
category#com.bigwheel.winners
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:59:17-61
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:59:27-58
service#com.onesignal.HmsMessageServiceOneSignal
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:63:9-69:19
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:65:13-37
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:64:13-68
intent-filter#action:name:com.huawei.push.action.MESSAGING_EVENT
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:66:13-68:29
action#com.huawei.push.action.MESSAGING_EVENT
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:67:17-81
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:67:25-78
activity#com.onesignal.NotificationOpenedActivityHMS
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:71:9-79:20
	android:noHistory
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:74:13-37
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:73:13-36
	android:theme
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:75:13-72
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:72:13-71
intent-filter#action:name:android.intent.action.VIEW
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:76:13-78:29
action#android.intent.action.VIEW
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:77:17-69
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:77:25-66
service#com.onesignal.FCMIntentService
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:81:9-83:40
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:83:13-37
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:82:13-58
service#com.onesignal.FCMIntentJobService
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:84:9-87:72
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:86:13-37
	android:permission
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:87:13-69
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:85:13-61
service#com.onesignal.SyncService
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:88:9-91:43
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:90:13-37
	android:stopWithTask
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:91:13-40
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:89:13-53
service#com.onesignal.SyncJobService
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:92:9-95:72
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:94:13-37
	android:permission
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:95:13-69
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:93:13-56
activity#com.onesignal.PermissionsActivity
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:97:9-100:75
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:99:13-37
	android:theme
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:100:13-72
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:98:13-61
receiver#com.onesignal.NotificationDismissReceiver
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:102:9-104:39
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:104:13-36
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:103:13-69
receiver#com.onesignal.BootUpReceiver
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:105:9-112:20
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:107:13-36
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:106:13-56
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.QUICKBOOT_POWERON
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:108:13-111:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:109:17-79
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:109:25-76
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:110:17-82
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:110:25-79
receiver#com.onesignal.UpgradeReceiver
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:113:9-119:20
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:115:13-36
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:114:13-57
intent-filter#action:name:android.intent.action.MY_PACKAGE_REPLACED
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:116:13-118:29
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:117:17-84
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:117:25-81
activity#com.onesignal.NotificationOpenedReceiver
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:121:9-127:75
	android:excludeFromRecents
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:123:13-46
	android:noHistory
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:125:13-37
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:124:13-36
	android:theme
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:127:13-72
	android:taskAffinity
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:126:13-36
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:122:13-68
activity#com.onesignal.NotificationOpenedReceiverAndroid22AndOlder
ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:128:9-133:75
	android:excludeFromRecents
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:130:13-46
	android:noHistory
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:132:13-37
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:131:13-36
	android:theme
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:133:13-72
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.12] C:\Users\<USER>\.gradle\caches\transforms-3\0d3760ba818541d623cc73ef085c540c\transformed\jetified-OneSignal-4.8.12\AndroidManifest.xml:129:13-85
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:36:9-42:19
MERGED from [com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:36:9-42:19
MERGED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:27:9-33:19
MERGED from [com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:27:9-33:19
MERGED from [com.google.firebase:firebase-datatransport:17.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\b723391e8f48268e968bf5f15f4f7103\transformed\jetified-firebase-datatransport-17.0.10\AndroidManifest.xml:25:9-31:19
MERGED from [com.google.firebase:firebase-datatransport:17.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\b723391e8f48268e968bf5f15f4f7103\transformed\jetified-firebase-datatransport-17.0.10\AndroidManifest.xml:25:9-31:19
MERGED from [com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\75afd29f29a59c314933f9e5db0ea997\transformed\jetified-firebase-installations-16.3.5\AndroidManifest.xml:15:9-21:19
MERGED from [com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\75afd29f29a59c314933f9e5db0ea997\transformed\jetified-firebase-installations-16.3.5\AndroidManifest.xml:15:9-21:19
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:32:9-36:35
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:32:9-36:35
	android:exported
		ADDED from [com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:30:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:36:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:34:13-43
	android:name
		ADDED from [com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:29:13-84
meta-data#com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar
ADDED from [com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:31:13-33:85
	android:value
		ADDED from [com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:33:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dac7cb38cb0212e3f2c874ca9e92c36d\transformed\jetified-firebase-database-20.1.0\AndroidManifest.xml:32:17-109
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f5896f3432f471e2fa3c46f0dc82e47\transformed\jetified-play-services-auth-20.4.0\AndroidManifest.xml:34:13-89
provider#com.squareup.picasso.PicassoProvider
ADDED from [com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\transforms-3\214cedbbf2c701b1abf04d9c644dc4b3\transformed\jetified-picasso-2.71828\AndroidManifest.xml:8:9-11:40
	android:authorities
		ADDED from [com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\transforms-3\214cedbbf2c701b1abf04d9c644dc4b3\transformed\jetified-picasso-2.71828\AndroidManifest.xml:10:13-72
	android:exported
		ADDED from [com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\transforms-3\214cedbbf2c701b1abf04d9c644dc4b3\transformed\jetified-picasso-2.71828\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\transforms-3\214cedbbf2c701b1abf04d9c644dc4b3\transformed\jetified-picasso-2.71828\AndroidManifest.xml:9:13-64
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:28:9-35:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:31:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:30:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:29:13-82
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:32:13-34:29
	android:priority
		ADDED from [com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:32:28-51
action#com.google.firebase.MESSAGING_EVENT
ADDED from [com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:33:17-78
	android:name
		ADDED from [com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:33:25-75
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:39:13-41:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:41:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\8849a6fbe2ebfe5f57a82d8370a3dd43\transformed\jetified-firebase-messaging-21.0.1\AndroidManifest.xml:40:17-119
meta-data#com.google.firebase.components:com.google.firebase.iid.Registrar
ADDED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:33:17-96
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:37:9-44:20
	android:exported
		ADDED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:39:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:40:13-73
	android:name
		ADDED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:38:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-iid:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c247b7781340ec5d407f72854588632f\transformed\jetified-firebase-iid-21.0.1\AndroidManifest.xml:41:13-43:29
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\02436aca31e270f81bb7f76ee8fb72ca\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\02436aca31e270f81bb7f76ee8fb72ca\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\02436aca31e270f81bb7f76ee8fb72ca\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\02436aca31e270f81bb7f76ee8fb72ca\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:30:13-32:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:32:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\32dc05b17f64c71634bd5ef7a038015f\transformed\jetified-play-services-measurement-api-18.0.2\AndroidManifest.xml:31:17-139
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:17.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\b723391e8f48268e968bf5f15f4f7103\transformed\jetified-firebase-datatransport-17.0.10\AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:17.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\b723391e8f48268e968bf5f15f4f7103\transformed\jetified-firebase-datatransport-17.0.10\AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:17.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\b723391e8f48268e968bf5f15f4f7103\transformed\jetified-firebase-datatransport-17.0.10\AndroidManifest.xml:29:17-115
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\75afd29f29a59c314933f9e5db0ea997\transformed\jetified-firebase-installations-16.3.5\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\75afd29f29a59c314933f9e5db0ea997\transformed\jetified-firebase-installations-16.3.5\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\75afd29f29a59c314933f9e5db0ea997\transformed\jetified-firebase-installations-16.3.5\AndroidManifest.xml:19:17-127
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:25:9-30:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:27:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:29:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:28:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:30:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c45416e9ee267796a5b0c4966b73e506\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:26:13-77
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\2f89d754fb566752c59cfba9dfd7f2ec\transformed\jetified-play-services-measurement-impl-18.0.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\2f89d754fb566752c59cfba9dfd7f2ec\transformed\jetified-play-services-measurement-impl-18.0.2\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\f2c7888fd4b5229158d5b0578f40e969\transformed\jetified-play-services-measurement-18.0.2\AndroidManifest.xml:40:13-87
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b84e8a3931c74ff232fa18e9c8d1ffe2\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b84e8a3931c74ff232fa18e9c8d1ffe2\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b84e8a3931c74ff232fa18e9c8d1ffe2\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\352e396638fb5070b2f71fd464b0adfd\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ba820f391a6b9833f8808c735357937\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ba820f391a6b9833f8808c735357937\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a866d754e8b1221e040f28cf2983cda9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a866d754e8b1221e040f28cf2983cda9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fc02b28a36c19b87c465ede6152f8af\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:22-74
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\df6acf635dd3a874ffd9d6d3c398e3f3\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ba820f391a6b9833f8808c735357937\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ba820f391a6b9833f8808c735357937\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ba820f391a6b9833f8808c735357937\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\4dc13d69c529875d6c5460d4ad7e3591\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\4dc13d69c529875d6c5460d4ad7e3591\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\4dc13d69c529875d6c5460d4ad7e3591\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\4dc13d69c529875d6c5460d4ad7e3591\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\427cbb835f29508fece96b421f46b570\transformed\jetified-transport-runtime-2.2.5\AndroidManifest.xml:33:13-132
