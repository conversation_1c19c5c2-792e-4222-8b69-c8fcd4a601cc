// Generated by view binder compiler. Do not edit!
package com.bigwheel.winners.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airbnb.lottie.LottieAnimationView;
import com.bigwheel.winners.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityPaymentResultPayuMoneyBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LottieAnimationView animationView;

  @NonNull
  public final CardView cardView;

  @NonNull
  public final TextView date;

  @NonNull
  public final TextView finaldata;

  @NonNull
  public final Button okbtn;

  @NonNull
  public final TextView textinfo;

  @NonNull
  public final TextView tranAmu;

  @NonNull
  public final TextView tranID;

  @NonNull
  public final TextView tranemail;

  private ActivityPaymentResultPayuMoneyBinding(@NonNull LinearLayout rootView,
      @NonNull LottieAnimationView animationView, @NonNull CardView cardView,
      @NonNull TextView date, @NonNull TextView finaldata, @NonNull Button okbtn,
      @NonNull TextView textinfo, @NonNull TextView tranAmu, @NonNull TextView tranID,
      @NonNull TextView tranemail) {
    this.rootView = rootView;
    this.animationView = animationView;
    this.cardView = cardView;
    this.date = date;
    this.finaldata = finaldata;
    this.okbtn = okbtn;
    this.textinfo = textinfo;
    this.tranAmu = tranAmu;
    this.tranID = tranID;
    this.tranemail = tranemail;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityPaymentResultPayuMoneyBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityPaymentResultPayuMoneyBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_payment_result_payu_money, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityPaymentResultPayuMoneyBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.animationView;
      LottieAnimationView animationView = ViewBindings.findChildViewById(rootView, id);
      if (animationView == null) {
        break missingId;
      }

      id = R.id.card_view;
      CardView cardView = ViewBindings.findChildViewById(rootView, id);
      if (cardView == null) {
        break missingId;
      }

      id = R.id.date;
      TextView date = ViewBindings.findChildViewById(rootView, id);
      if (date == null) {
        break missingId;
      }

      id = R.id.finaldata;
      TextView finaldata = ViewBindings.findChildViewById(rootView, id);
      if (finaldata == null) {
        break missingId;
      }

      id = R.id.okbtn;
      Button okbtn = ViewBindings.findChildViewById(rootView, id);
      if (okbtn == null) {
        break missingId;
      }

      id = R.id.textinfo;
      TextView textinfo = ViewBindings.findChildViewById(rootView, id);
      if (textinfo == null) {
        break missingId;
      }

      id = R.id.tranAmu;
      TextView tranAmu = ViewBindings.findChildViewById(rootView, id);
      if (tranAmu == null) {
        break missingId;
      }

      id = R.id.tranID;
      TextView tranID = ViewBindings.findChildViewById(rootView, id);
      if (tranID == null) {
        break missingId;
      }

      id = R.id.tranemail;
      TextView tranemail = ViewBindings.findChildViewById(rootView, id);
      if (tranemail == null) {
        break missingId;
      }

      return new ActivityPaymentResultPayuMoneyBinding((LinearLayout) rootView, animationView,
          cardView, date, finaldata, okbtn, textinfo, tranAmu, tranID, tranemail);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
