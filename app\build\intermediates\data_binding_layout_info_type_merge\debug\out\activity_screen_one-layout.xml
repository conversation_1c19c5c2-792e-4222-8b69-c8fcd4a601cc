<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_screen_one" modulePackage="com.bigwheel.winners" filePath="app\src\main\res\layout\activity_screen_one.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.drawerlayout.widget.DrawerLayout" rootNodeViewId="@+id/drwa"><Targets><Target id="@+id/drwa" tag="layout/activity_screen_one_0" view="androidx.drawerlayout.widget.DrawerLayout"><Expressions/><location startLine="1" startOffset="0" endLine="702" endOffset="43"/></Target><Target id="@+id/linearLayout5" view="LinearLayout"><Expressions/><location startLine="16" startOffset="16" endLine="97" endOffset="30"/></Target><Target id="@+id/textView7" view="TextView"><Expressions/><location startLine="50" startOffset="28" endLine="62" endOffset="75"/></Target><Target id="@+id/textView8" view="TextView"><Expressions/><location startLine="64" startOffset="28" endLine="76" endOffset="86"/></Target><Target id="@+id/swith" view="Switch"><Expressions/><location startLine="87" startOffset="24" endLine="95" endOffset="61"/></Target><Target id="@+id/linearLayout4" view="LinearLayout"><Expressions/><location startLine="119" startOffset="8" endLine="472" endOffset="22"/></Target><Target id="@+id/wheelLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="128" startOffset="12" endLine="176" endOffset="63"/></Target><Target id="@+id/cursorView" view="ImageView"><Expressions/><location startLine="150" startOffset="20" endLine="156" endOffset="57"/></Target><Target id="@+id/luckyWheel" view="rubikstudio.library.LuckyWheelView"><Expressions/><location startLine="166" startOffset="20" endLine="172" endOffset="57"/></Target><Target id="@+id/addMoneyLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="180" startOffset="12" endLine="364" endOffset="63"/></Target><Target id="@+id/btn_closeaddmoney" view="ImageView"><Expressions/><location startLine="193" startOffset="16" endLine="201" endOffset="63"/></Target><Target id="@+id/textView9" view="TextView"><Expressions/><location startLine="203" startOffset="16" endLine="213" endOffset="63"/></Target><Target id="@+id/etDeposite" view="EditText"><Expressions/><location startLine="215" startOffset="16" endLine="230" endOffset="74"/></Target><Target id="@+id/linearLayout3" view="LinearLayout"><Expressions/><location startLine="232" startOffset="16" endLine="339" endOffset="30"/></Target><Target id="@+id/deposite_50" view="LinearLayout"><Expressions/><location startLine="244" startOffset="20" endLine="261" endOffset="34"/></Target><Target id="@+id/price1" view="TextView"><Expressions/><location startLine="253" startOffset="24" endLine="260" endOffset="53"/></Target><Target id="@+id/deposite_100" view="LinearLayout"><Expressions/><location startLine="263" startOffset="20" endLine="279" endOffset="34"/></Target><Target id="@+id/price2" view="TextView"><Expressions/><location startLine="271" startOffset="24" endLine="278" endOffset="53"/></Target><Target id="@+id/deposite_150" view="LinearLayout"><Expressions/><location startLine="281" startOffset="20" endLine="298" endOffset="34"/></Target><Target id="@+id/price3" view="TextView"><Expressions/><location startLine="290" startOffset="24" endLine="297" endOffset="53"/></Target><Target id="@+id/deposite_200" view="LinearLayout"><Expressions/><location startLine="300" startOffset="20" endLine="318" endOffset="34"/></Target><Target id="@+id/price4" view="TextView"><Expressions/><location startLine="310" startOffset="24" endLine="317" endOffset="53"/></Target><Target id="@+id/btnDeposite" view="LinearLayout"><Expressions/><location startLine="341" startOffset="16" endLine="362" endOffset="30"/></Target><Target id="@+id/withdrawMoneyLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="368" startOffset="12" endLine="471" endOffset="63"/></Target><Target id="@+id/textView11" view="TextView"><Expressions/><location startLine="381" startOffset="16" endLine="391" endOffset="63"/></Target><Target id="@+id/btn_closewithdraw" view="ImageView"><Expressions/><location startLine="393" startOffset="16" endLine="401" endOffset="63"/></Target><Target id="@+id/etWithdraw" view="EditText"><Expressions/><location startLine="412" startOffset="16" endLine="427" endOffset="75"/></Target><Target id="@+id/etWithdrawAmmount" view="EditText"><Expressions/><location startLine="429" startOffset="16" endLine="443" endOffset="75"/></Target><Target id="@+id/btnWithdrawFinal" view="LinearLayout"><Expressions/><location startLine="445" startOffset="16" endLine="468" endOffset="30"/></Target><Target id="@+id/linearLayout2" view="LinearLayout"><Expressions/><location startLine="474" startOffset="8" endLine="576" endOffset="22"/></Target><Target id="@+id/tvDepositeAmmount" view="TextView"><Expressions/><location startLine="499" startOffset="20" endLine="511" endOffset="67"/></Target><Target id="@+id/tvWiningeAmmount" view="TextView"><Expressions/><location startLine="548" startOffset="20" endLine="560" endOffset="67"/></Target><Target id="@+id/linearLayout9" view="LinearLayout"><Expressions/><location startLine="578" startOffset="8" endLine="633" endOffset="22"/></Target><Target id="@+id/btnAddMoneys" view="LinearLayout"><Expressions/><location startLine="587" startOffset="12" endLine="606" endOffset="26"/></Target><Target id="@+id/btnWithdraws" view="LinearLayout"><Expressions/><location startLine="608" startOffset="12" endLine="630" endOffset="26"/></Target><Target id="@+id/linearLayout6" view="LinearLayout"><Expressions/><location startLine="637" startOffset="8" endLine="675" endOffset="22"/></Target><Target id="@+id/btnSpin" view="LinearLayout"><Expressions/><location startLine="650" startOffset="12" endLine="672" endOffset="26"/></Target><Target id="@+id/nav_view" view="com.google.android.material.navigation.NavigationView"><Expressions/><location startLine="688" startOffset="4" endLine="699" endOffset="58"/></Target></Targets></Layout>