package com.bigwheel.winners;

import android.app.ProgressDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.onesignal.OneSignal;

public class SplashScreen extends AppCompatActivity {

    ConstraintLayout btnGetMoney;
    SharedPreferences pref;
    SharedPreferences.Editor editor;
    private static final String ONESIGNAL_APP_ID = "************************************";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash_screen);
        btnGetMoney = findViewById(R.id.btnGetMoney);
        getSupportActionBar().hide();
        OneSignal.setLogLevel(OneSignal.LOG_LEVEL.VERBOSE, OneSignal.LOG_LEVEL.NONE);

        // OneSignal Initialization
        OneSignal.initWithContext(this);
        OneSignal.setAppId(ONESIGNAL_APP_ID);

        pref = getSharedPreferences("mypref", MODE_PRIVATE);
        editor = pref.edit();

       /* if(!isConnectionAvailable(this))
        {
            new AlertDialog.Builder(SplashScreen.this)
                    .setTitle("Error!")
                    .setMessage("Check Your Internet Connection")
                    .setCancelable(false)
                    .setPositiveButton("Retry", new DialogInterface.OnClickListener() {
                        public void onClick(DialogInterface dialog, int which) {
                            // Continue with delete operation
                            Intent intent = new Intent(SplashScreen.this, SplashScreen.class);
                            startActivity(intent);
                            finish();
                        }
                    })
                    // A null listener allows the button to dismiss the dialog and take no further action.
                    .setNegativeButton("Exit", new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialogInterface, int i) {
                            finish();
                        }
                    })
                    .setIcon(android.R.drawable.ic_dialog_alert)
                    .show();
            return;
        }




        btnGetMoney.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

              *//*  SweetAlertDialog sweetAlertDialog = new SweetAlertDialog(SplashScreen.this, 5);
                pDialog = sweetAlertDialog;
                sweetAlertDialog.getProgressHelper().setBarColor(Color.parseColor("#FFB800"));
                pDialog.setTitleText("Please wait...");
                pDialog.setCancelable(false);
                pDialog.show();*//*


                ProgressDialog progressDialog = new ProgressDialog(SplashScreen.this);
                progressDialog.setCancelable(false);
                progressDialog.setMessage("Please wait...");
                progressDialog.show();

                btnGetMoney.setVisibility(View.GONE);



                FirebaseDatabase database = FirebaseDatabase.getInstance();
                DatabaseReference myRef = database.getReference("mydata");

                myRef.addValueEventListener(new ValueEventListener() {
                    @Override
                    public void onDataChange(DataSnapshot dataSnapshot) {

                        progressDialog.dismiss();
                        // This method is called once with the initial value and again
                        // whenever data at this location is updated.
                        String value = dataSnapshot.child("url").getValue(String.class);

                        editor.putString("baseurl", value);
                        editor.putString("wamu",dataSnapshot.child("wamu").getValue(String.class));
                        editor.putString("damu1",dataSnapshot.child("damu1").getValue(String.class));
                        editor.putString("damu2",dataSnapshot.child("damu2").getValue(String.class));
                        editor.putString("damu3",dataSnapshot.child("damu3").getValue(String.class));
                        editor.putString("upiid",dataSnapshot.child("upiid").getValue(String.class));
                        editor.putString("addmoney",dataSnapshot.child("addmoney").getValue(String.class));
                        editor.apply();
                        int vcode =  Integer.parseInt(dataSnapshot.child("version").getValue(String.class));


                        if(vcode == BuildConfig.VERSION_CODE)
                        {
                            Intent intent = new Intent(SplashScreen.this,GoogleLogin.class);
                            startActivity(intent);
                            finish();
                        }else
                        {

                            new AlertDialog.Builder(SplashScreen.this)
                                    .setTitle("Update!")
                                    .setMessage("New Version is rollout on play store!\nPlease update your app now!")
                                    .setCancelable(false)
                                    .setPositiveButton("Update", new DialogInterface.OnClickListener() {
                                        public void onClick(DialogInterface dialog, int which) {
                                            // Continue with delete operation
                                            startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=" + getPackageName())));
                                            finish();
                                        }
                                    })
                                    // A null listener allows the button to dismiss the dialog and take no further action.
                                    .setNegativeButton("Exit", new DialogInterface.OnClickListener() {
                                        @Override
                                        public void onClick(DialogInterface dialogInterface, int i) {
                                            finish();
                                        }
                                    })
                                    .setIcon(android.R.drawable.ic_dialog_alert)
                                    .show();
                        }

                    }

                    @Override
                    public void onCancelled(DatabaseError error) {
                        // Failed to read value
                     progressDialog.dismiss();
                      //  pDialog.dismiss();

                        new AlertDialog.Builder(SplashScreen.this)
                                .setTitle("Error!")
                                .setMessage("Check Your Internet Connection")
                                .setCancelable(false)
                                .setPositiveButton("Retry", new DialogInterface.OnClickListener() {
                                    public void onClick(DialogInterface dialog, int which) {
                                        // Continue with delete operation
                                        Intent intent = new Intent(SplashScreen.this, SplashScreen.class);
                                        startActivity(intent);
                                        finish();
                                    }
                                })
                                // A null listener allows the button to dismiss the dialog and take no further action.
                                .setNegativeButton("Exit", new DialogInterface.OnClickListener() {
                                    @Override
                                    public void onClick(DialogInterface dialogInterface, int i) {
                                        finish();
                                    }
                                })
                                .setIcon(android.R.drawable.ic_dialog_alert)
                                .show();
                        btnGetMoney.setVisibility(View.VISIBLE);
                    }
                });
            }
        });*/

        btnGetMoney.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

              /*  SweetAlertDialog sweetAlertDialog = new SweetAlertDialog(SplashScreen.this, 5);
                pDialog = sweetAlertDialog;
                sweetAlertDialog.getProgressHelper().setBarColor(Color.parseColor("#FFB800"));
                pDialog.setTitleText("Please wait...");
                pDialog.setCancelable(false);
                pDialog.show();*/

                ProgressDialog progressDialog = new ProgressDialog(SplashScreen.this);
                progressDialog.setCancelable(false);
                progressDialog.setMessage("Please wait...");
                progressDialog.show();

                btnGetMoney.setVisibility(View.GONE);


                int i = Integer.parseInt(pref.getString("version",""));

                if(i == 1)
                {
                    progressDialog.dismiss();
                    Intent intent = new Intent(SplashScreen.this, GoogleLogin.class);
                    startActivity(intent);
                    finish();
                }
                else
                {
                    progressDialog.dismiss();
                    new AlertDialog.Builder(SplashScreen.this)
                            .setTitle("Update!")
                            .setMessage("New Version is rollout on play store!\nPlease update your app now!")
                            .setCancelable(false)
                            .setPositiveButton("Update", new DialogInterface.OnClickListener() {
                                public void onClick(DialogInterface dialog, int which) {
                                    // Continue with delete operation
                                    startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=" + getPackageName())));
                                    finish();
                                }
                            })
                            // A null listener allows the button to dismiss the dialog and take no further action.
                            .setNegativeButton("Exit", new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialogInterface, int i) {
                                    finish();
                                }
                            })
                            .setIcon(android.R.drawable.ic_dialog_alert)
                            .show();
                }

            }
        });


    }

    public static boolean isConnectionAvailable(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo netInfo = connectivityManager.getActiveNetworkInfo();
            if (netInfo != null && netInfo.isConnected()
                    && netInfo.isConnectedOrConnecting()
                    && netInfo.isAvailable()) {
                return true;
            }
        }
        return false;
    }
}