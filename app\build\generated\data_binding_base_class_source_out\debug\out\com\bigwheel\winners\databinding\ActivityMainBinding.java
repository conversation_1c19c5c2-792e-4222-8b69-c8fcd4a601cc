// Generated by view binder compiler. Do not edit!
package com.bigwheel.winners.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bigwheel.winners.R;
import com.google.android.material.navigation.NavigationView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final DrawerLayout rootView;

  @NonNull
  public final ConstraintLayout addMoneyLayout;

  @NonNull
  public final ImageView btnCloseaddmoney;

  @NonNull
  public final ImageView btnClosewithdraw;

  @NonNull
  public final LinearLayout btnDeposite;

  @NonNull
  public final ImageView btnMenu;

  @NonNull
  public final LinearLayout btnSpin;

  @NonNull
  public final LinearLayout btnWithdrawFinal;

  @NonNull
  public final ConstraintLayout constraintLayout;

  @NonNull
  public final DrawerLayout drawerLayout;

  @NonNull
  public final EditText etDeposite;

  @NonNull
  public final EditText etWithdraw;

  @NonNull
  public final EditText etWithdrawAmmount;

  @NonNull
  public final LinearLayout linearLayout4;

  @NonNull
  public final LinearLayout llPricingPlans;

  @NonNull
  public final ImageView luckyWheel;

  @NonNull
  public final NavigationView navView;

  @NonNull
  public final LinearLayout plan100;

  @NonNull
  public final LinearLayout plan150;

  @NonNull
  public final LinearLayout plan200;

  @NonNull
  public final LinearLayout plan50;

  @NonNull
  public final TextView textView;

  @NonNull
  public final TextView textView11;

  @NonNull
  public final TextView textView9;

  @NonNull
  public final TextView tvDepositeAmmount;

  @NonNull
  public final TextView tvWiningeAmmount;

  @NonNull
  public final ConstraintLayout wheelLayout;

  @NonNull
  public final ConstraintLayout withdrawMoneyLayout;

  private ActivityMainBinding(@NonNull DrawerLayout rootView,
      @NonNull ConstraintLayout addMoneyLayout, @NonNull ImageView btnCloseaddmoney,
      @NonNull ImageView btnClosewithdraw, @NonNull LinearLayout btnDeposite,
      @NonNull ImageView btnMenu, @NonNull LinearLayout btnSpin,
      @NonNull LinearLayout btnWithdrawFinal, @NonNull ConstraintLayout constraintLayout,
      @NonNull DrawerLayout drawerLayout, @NonNull EditText etDeposite,
      @NonNull EditText etWithdraw, @NonNull EditText etWithdrawAmmount,
      @NonNull LinearLayout linearLayout4, @NonNull LinearLayout llPricingPlans,
      @NonNull ImageView luckyWheel, @NonNull NavigationView navView, @NonNull LinearLayout plan100,
      @NonNull LinearLayout plan150, @NonNull LinearLayout plan200, @NonNull LinearLayout plan50,
      @NonNull TextView textView, @NonNull TextView textView11, @NonNull TextView textView9,
      @NonNull TextView tvDepositeAmmount, @NonNull TextView tvWiningeAmmount,
      @NonNull ConstraintLayout wheelLayout, @NonNull ConstraintLayout withdrawMoneyLayout) {
    this.rootView = rootView;
    this.addMoneyLayout = addMoneyLayout;
    this.btnCloseaddmoney = btnCloseaddmoney;
    this.btnClosewithdraw = btnClosewithdraw;
    this.btnDeposite = btnDeposite;
    this.btnMenu = btnMenu;
    this.btnSpin = btnSpin;
    this.btnWithdrawFinal = btnWithdrawFinal;
    this.constraintLayout = constraintLayout;
    this.drawerLayout = drawerLayout;
    this.etDeposite = etDeposite;
    this.etWithdraw = etWithdraw;
    this.etWithdrawAmmount = etWithdrawAmmount;
    this.linearLayout4 = linearLayout4;
    this.llPricingPlans = llPricingPlans;
    this.luckyWheel = luckyWheel;
    this.navView = navView;
    this.plan100 = plan100;
    this.plan150 = plan150;
    this.plan200 = plan200;
    this.plan50 = plan50;
    this.textView = textView;
    this.textView11 = textView11;
    this.textView9 = textView9;
    this.tvDepositeAmmount = tvDepositeAmmount;
    this.tvWiningeAmmount = tvWiningeAmmount;
    this.wheelLayout = wheelLayout;
    this.withdrawMoneyLayout = withdrawMoneyLayout;
  }

  @Override
  @NonNull
  public DrawerLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addMoneyLayout;
      ConstraintLayout addMoneyLayout = ViewBindings.findChildViewById(rootView, id);
      if (addMoneyLayout == null) {
        break missingId;
      }

      id = R.id.btn_closeaddmoney;
      ImageView btnCloseaddmoney = ViewBindings.findChildViewById(rootView, id);
      if (btnCloseaddmoney == null) {
        break missingId;
      }

      id = R.id.btn_closewithdraw;
      ImageView btnClosewithdraw = ViewBindings.findChildViewById(rootView, id);
      if (btnClosewithdraw == null) {
        break missingId;
      }

      id = R.id.btnDeposite;
      LinearLayout btnDeposite = ViewBindings.findChildViewById(rootView, id);
      if (btnDeposite == null) {
        break missingId;
      }

      id = R.id.btn_menu;
      ImageView btnMenu = ViewBindings.findChildViewById(rootView, id);
      if (btnMenu == null) {
        break missingId;
      }

      id = R.id.btnSpin;
      LinearLayout btnSpin = ViewBindings.findChildViewById(rootView, id);
      if (btnSpin == null) {
        break missingId;
      }

      id = R.id.btnWithdrawFinal;
      LinearLayout btnWithdrawFinal = ViewBindings.findChildViewById(rootView, id);
      if (btnWithdrawFinal == null) {
        break missingId;
      }

      id = R.id.constraintLayout;
      ConstraintLayout constraintLayout = ViewBindings.findChildViewById(rootView, id);
      if (constraintLayout == null) {
        break missingId;
      }

      DrawerLayout drawerLayout = (DrawerLayout) rootView;

      id = R.id.etDeposite;
      EditText etDeposite = ViewBindings.findChildViewById(rootView, id);
      if (etDeposite == null) {
        break missingId;
      }

      id = R.id.etWithdraw;
      EditText etWithdraw = ViewBindings.findChildViewById(rootView, id);
      if (etWithdraw == null) {
        break missingId;
      }

      id = R.id.etWithdrawAmmount;
      EditText etWithdrawAmmount = ViewBindings.findChildViewById(rootView, id);
      if (etWithdrawAmmount == null) {
        break missingId;
      }

      id = R.id.linearLayout4;
      LinearLayout linearLayout4 = ViewBindings.findChildViewById(rootView, id);
      if (linearLayout4 == null) {
        break missingId;
      }

      id = R.id.llPricingPlans;
      LinearLayout llPricingPlans = ViewBindings.findChildViewById(rootView, id);
      if (llPricingPlans == null) {
        break missingId;
      }

      id = R.id.luckyWheel;
      ImageView luckyWheel = ViewBindings.findChildViewById(rootView, id);
      if (luckyWheel == null) {
        break missingId;
      }

      id = R.id.nav_view;
      NavigationView navView = ViewBindings.findChildViewById(rootView, id);
      if (navView == null) {
        break missingId;
      }

      id = R.id.plan100;
      LinearLayout plan100 = ViewBindings.findChildViewById(rootView, id);
      if (plan100 == null) {
        break missingId;
      }

      id = R.id.plan150;
      LinearLayout plan150 = ViewBindings.findChildViewById(rootView, id);
      if (plan150 == null) {
        break missingId;
      }

      id = R.id.plan200;
      LinearLayout plan200 = ViewBindings.findChildViewById(rootView, id);
      if (plan200 == null) {
        break missingId;
      }

      id = R.id.plan50;
      LinearLayout plan50 = ViewBindings.findChildViewById(rootView, id);
      if (plan50 == null) {
        break missingId;
      }

      id = R.id.textView;
      TextView textView = ViewBindings.findChildViewById(rootView, id);
      if (textView == null) {
        break missingId;
      }

      id = R.id.textView11;
      TextView textView11 = ViewBindings.findChildViewById(rootView, id);
      if (textView11 == null) {
        break missingId;
      }

      id = R.id.textView9;
      TextView textView9 = ViewBindings.findChildViewById(rootView, id);
      if (textView9 == null) {
        break missingId;
      }

      id = R.id.tvDepositeAmmount;
      TextView tvDepositeAmmount = ViewBindings.findChildViewById(rootView, id);
      if (tvDepositeAmmount == null) {
        break missingId;
      }

      id = R.id.tvWiningeAmmount;
      TextView tvWiningeAmmount = ViewBindings.findChildViewById(rootView, id);
      if (tvWiningeAmmount == null) {
        break missingId;
      }

      id = R.id.wheelLayout;
      ConstraintLayout wheelLayout = ViewBindings.findChildViewById(rootView, id);
      if (wheelLayout == null) {
        break missingId;
      }

      id = R.id.withdrawMoneyLayout;
      ConstraintLayout withdrawMoneyLayout = ViewBindings.findChildViewById(rootView, id);
      if (withdrawMoneyLayout == null) {
        break missingId;
      }

      return new ActivityMainBinding((DrawerLayout) rootView, addMoneyLayout, btnCloseaddmoney,
          btnClosewithdraw, btnDeposite, btnMenu, btnSpin, btnWithdrawFinal, constraintLayout,
          drawerLayout, etDeposite, etWithdraw, etWithdrawAmmount, linearLayout4, llPricingPlans,
          luckyWheel, navView, plan100, plan150, plan200, plan50, textView, textView11, textView9,
          tvDepositeAmmount, tvWiningeAmmount, wheelLayout, withdrawMoneyLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
