<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"

    android:background="@drawable/splashbg"
    tools:context="splash.screenfour">

    <TextView
        android:id="@+id/name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="serif"
        android:gravity="center"
        android:text="Welcome"
        android:textAlignment="center"
        android:textColor="@color/blue"
        android:textSize="40sp"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="400dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>


    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
      <Button
          android:layout_width="match_parent"
           android:layout_height="wrap_content"
           android:layout_margin="@dimen/_20sdp"
           android:text="Privacy Policy"
           android:textColor="#fff"
           android:textStyle="bold"
           android:textSize="@dimen/_20sdp"
           android:id="@+id/b1"
      />

        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/_20sdp"
            android:layout_marginRight="@dimen/_20sdp"
            android:text="Term And Condition"
            android:textColor="#fff"
            android:textStyle="bold"
            android:textSize="@dimen/_20sdp"
            android:id="@+id/b2"
            />


    </LinearLayout>

</LinearLayout>