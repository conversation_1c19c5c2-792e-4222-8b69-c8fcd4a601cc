// Generated by view binder compiler. Do not edit!
package com.bigwheel.winners.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bigwheel.winners.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogPaymentOptionsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnCancelPayment;

  @NonNull
  public final LinearLayout layoutGooglePay;

  @NonNull
  public final LinearLayout layoutPaytm;

  @NonNull
  public final LinearLayout layoutPhonePay;

  @NonNull
  public final TextView paymentAmount;

  private DialogPaymentOptionsBinding(@NonNull LinearLayout rootView,
      @NonNull Button btnCancelPayment, @NonNull LinearLayout layoutGooglePay,
      @NonNull LinearLayout layoutPaytm, @NonNull LinearLayout layoutPhonePay,
      @NonNull TextView paymentAmount) {
    this.rootView = rootView;
    this.btnCancelPayment = btnCancelPayment;
    this.layoutGooglePay = layoutGooglePay;
    this.layoutPaytm = layoutPaytm;
    this.layoutPhonePay = layoutPhonePay;
    this.paymentAmount = paymentAmount;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogPaymentOptionsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogPaymentOptionsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_payment_options, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogPaymentOptionsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_cancel_payment;
      Button btnCancelPayment = ViewBindings.findChildViewById(rootView, id);
      if (btnCancelPayment == null) {
        break missingId;
      }

      id = R.id.layout_google_pay;
      LinearLayout layoutGooglePay = ViewBindings.findChildViewById(rootView, id);
      if (layoutGooglePay == null) {
        break missingId;
      }

      id = R.id.layout_paytm;
      LinearLayout layoutPaytm = ViewBindings.findChildViewById(rootView, id);
      if (layoutPaytm == null) {
        break missingId;
      }

      id = R.id.layout_phone_pay;
      LinearLayout layoutPhonePay = ViewBindings.findChildViewById(rootView, id);
      if (layoutPhonePay == null) {
        break missingId;
      }

      id = R.id.payment_amount;
      TextView paymentAmount = ViewBindings.findChildViewById(rootView, id);
      if (paymentAmount == null) {
        break missingId;
      }

      return new DialogPaymentOptionsBinding((LinearLayout) rootView, btnCancelPayment,
          layoutGooglePay, layoutPaytm, layoutPhonePay, paymentAmount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
