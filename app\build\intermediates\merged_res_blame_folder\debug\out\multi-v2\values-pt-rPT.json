{"logs": [{"outputFile": "D:\\android project\\new big 22-06\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-pt-rPT\\values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\02436aca31e270f81bb7f76ee8fb72ca\\transformed\\jetified-play-services-base-18.0.1\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,457,581,685,849,973,1091,1196,1380,1484,1650,1777,1932,2106,2170,2235", "endColumns": "100,158,123,103,163,123,117,104,183,103,165,126,154,173,63,64,82", "endOffsets": "297,456,580,684,848,972,1090,1195,1379,1483,1649,1776,1931,2105,2169,2234,2317"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3395,3500,3663,3791,3899,4067,4195,4317,4571,4759,4867,5037,5168,5327,5505,5573,5642", "endColumns": "104,162,127,107,167,127,121,108,187,107,169,130,158,177,67,68,86", "endOffsets": "3495,3658,3786,3894,4062,4190,4312,4421,4754,4862,5032,5163,5322,5500,5568,5637,5724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8849a6fbe2ebfe5f57a82d8370a3dd43\\transformed\\jetified-firebase-messaging-21.0.1\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "211", "endColumns": "102", "endOffsets": "313"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "6315", "endColumns": "106", "endOffsets": "6417"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\929536aae40a590fbb05df20b2f1b3b8\\transformed\\material-1.6.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,310,413,533,614,678,770,849,914,1004,1072,1134,1207,1271,1325,1451,1509,1571,1625,1701,1844,1931,2013,2122,2204,2286,2373,2440,2506,2581,2661,2748,2821,2898,2971,3045,3138,3215,3308,3406,3480,3561,3660,3713,3779,3868,3956,4018,4082,4145,4261,4364,4471,4575", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,102,119,80,63,91,78,64,89,67,61,72,63,53,125,57,61,53,75,142,86,81,108,81,81,86,66,65,74,79,86,72,76,72,73,92,76,92,97,73,80,98,52,65,88,87,61,63,62,115,102,106,103,85", "endOffsets": "223,305,408,528,609,673,765,844,909,999,1067,1129,1202,1266,1320,1446,1504,1566,1620,1696,1839,1926,2008,2117,2199,2281,2368,2435,2501,2576,2656,2743,2816,2893,2966,3040,3133,3210,3303,3401,3475,3556,3655,3708,3774,3863,3951,4013,4077,4140,4256,4359,4466,4570,4656"}, "to": {"startLines": "2,33,34,35,36,56,57,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3009,3091,3194,3314,5845,5909,6422,6501,6566,6656,6724,6786,6859,6923,6977,7103,7161,7223,7277,7353,7496,7583,7665,7774,7856,7938,8025,8092,8158,8233,8313,8400,8473,8550,8623,8697,8790,8867,8960,9058,9132,9213,9312,9365,9431,9520,9608,9670,9734,9797,9913,10016,10123,10227", "endLines": "5,33,34,35,36,56,57,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "endColumns": "12,81,102,119,80,63,91,78,64,89,67,61,72,63,53,125,57,61,53,75,142,86,81,108,81,81,86,66,65,74,79,86,72,76,72,73,92,76,92,97,73,80,98,52,65,88,87,61,63,62,115,102,106,103,85", "endOffsets": "273,3086,3189,3309,3390,5904,5996,6496,6561,6651,6719,6781,6854,6918,6972,7098,7156,7218,7272,7348,7491,7578,7660,7769,7851,7933,8020,8087,8153,8228,8308,8395,8468,8545,8618,8692,8785,8862,8955,9053,9127,9208,9307,9360,9426,9515,9603,9665,9729,9792,9908,10011,10118,10222,10308"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f2636cb968702bde5e4e1d445b2113c3\\transformed\\browser-1.3.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,171,270,382", "endColumns": "115,98,111,102", "endOffsets": "166,265,377,480"}, "to": {"startLines": "55,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "5729,6001,6100,6212", "endColumns": "115,98,111,102", "endOffsets": "5840,6095,6207,6310"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5a6f99c51ca9894dfbe548454cc2e836\\transformed\\appcompat-1.5.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "278,386,492,599,688,789,907,992,1072,1164,1258,1355,1449,1548,1642,1738,1833,1925,2017,2102,2209,2320,2422,2530,2638,2745,2910,10313", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "381,487,594,683,784,902,987,1067,1159,1253,1350,1444,1543,1637,1733,1828,1920,2012,2097,2204,2315,2417,2525,2633,2740,2905,3004,10394"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b84e8a3931c74ff232fa18e9c8d1ffe2\\transformed\\jetified-play-services-basement-18.1.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4426", "endColumns": "144", "endOffsets": "4566"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f556f30d56993798d7ef134459fae338\\transformed\\core-1.8.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "111", "startColumns": "4", "startOffsets": "10399", "endColumns": "100", "endOffsets": "10495"}}]}]}