<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/et_api_version"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:enabled="false"
            android:focusable="false"
            android:text="@string/api_version"
            android:textColor="@android:color/holo_blue_light" />

        <EditText
            android:id="@+id/et_api_version_value"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:enabled="false"
            android:text="7" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/et_si"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:enabled="false"
            android:focusable="false"
            android:text="@string/si"
            android:textColor="@android:color/holo_blue_light" />

        <EditText
            android:id="@+id/et_si_value"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:enabled="false"
            android:text="1" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/et_billingCycle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:enabled="false"
            android:focusable="false"
            android:text="@string/billingCycle"
            android:textColor="@android:color/holo_blue_light" />

        <androidx.appcompat.widget.AppCompatSpinner
            android:id="@+id/et_billingCycle_value"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/et_billingInterval"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:enabled="false"
            android:focusable="false"
            android:text="@string/billingInterval"
            android:textColor="@android:color/holo_blue_light" />

        <EditText
            android:id="@+id/et_billingInterval_value"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:inputType="number"
            android:text="1" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/et_billingAmount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:enabled="false"
            android:focusable="false"
            android:text="@string/billingAmount"
            android:textColor="@android:color/holo_blue_light" />

        <EditText
            android:id="@+id/et_billingAmount_value"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:inputType="numberDecimal"
            android:text="1.00" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/et_billingCurrency"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:enabled="false"
            android:focusable="false"
            android:text="@string/billingCurrency"
            android:textColor="@android:color/holo_blue_light" />

        <EditText
            android:id="@+id/et_billingCurrency_value"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:enabled="false"
            android:text="INR" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/et_paymentStartDate"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:enabled="false"
            android:focusable="false"
            android:text="@string/paymentStartDate"
            android:textColor="@android:color/holo_blue_light" />

        <EditText
            android:id="@+id/et_paymentStartDate_value"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:hint="@string/si_date_hint"
            android:text="2021-12-24" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/et_paymentEndDate"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:enabled="false"
            android:focusable="false"
            android:text="@string/paymentEndDate"
            android:textColor="@android:color/holo_blue_light" />

        <EditText
            android:id="@+id/et_paymentEndDate_value"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:hint="@string/si_date_hint"
            android:text="2022-12-24" />
    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/sp_free_trial_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:enabled="false"
            android:focusable="false"
            android:text="@string/free_trial"
            android:textColor="@android:color/holo_blue_light" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/sp_free_trial"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/et_remarks"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:enabled="false"
            android:focusable="false"
            android:text="@string/remarks"
            android:textColor="@android:color/holo_blue_light" />

        <EditText
            android:id="@+id/et_remarks_value"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5" />
    </LinearLayout>
</LinearLayout>