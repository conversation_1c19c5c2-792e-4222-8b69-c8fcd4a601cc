<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    app:cardCornerRadius="20dp"
    android:padding="20dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:padding="20dp"
        android:backgroundTint="@color/themeBgColor"
        android:background="@drawable/strok"
        android:layout_height="match_parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/winner"
                android:layout_width="100dp"
                android:layout_centerInParent="true"
                android:layout_height="100dp"
                app:lottie_loop="true"
                app:lottie_autoPlay="true"
                android:layout_gravity="center"></com.airbnb.lottie.LottieAnimationView>

            <com.airbnb.lottie.LottieAnimationView
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:id="@+id/loss"
                app:lottie_loop="true"
                app:lottie_autoPlay="true"
                android:layout_centerInParent="true"></com.airbnb.lottie.LottieAnimationView>
        </RelativeLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_marginTop="5dp"
            android:gravity="center"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:visibility="gone"
                android:text="Congratulations! You Win"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/winamount"
                android:layout_width="wrap_content"
                android:layout_marginStart="5dp"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:layout_height="wrap_content"
                android:text=""></TextView>


        </LinearLayout>


        <TextView
            android:id="@+id/tvDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginHorizontal="10dp"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:text="Thanks For A Playing"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <LinearLayout
            android:id="@+id/btnLater"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_gravity="center"
                android:layout_marginLeft="5dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="5dp"
                android:background="@drawable/ic_xml_button_">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginRight="5dp"
                    android:gravity="center"
                    android:text="Later"
                    android:textSize="18sp"
                    android:textColor="@color/white" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/btnRateNow"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_gravity="center"
                android:layout_marginLeft="5dp"
                android:layout_marginHorizontal="5dp"
                android:layout_weight="1"
                android:background="@drawable/ic_xml_button_">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textSize="18sp"
                    android:layout_gravity="center"
                    android:text="One More Spin"
                    android:textColor="@color/white" />
            </LinearLayout>


        </LinearLayout>

    </LinearLayout>


</androidx.cardview.widget.CardView>